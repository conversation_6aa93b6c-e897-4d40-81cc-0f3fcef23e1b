<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.hanyi</groupId>
        <artifactId>parent-worker</artifactId>
        <version>${revision}.${sha1}-${changelist}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>cem-compat</artifactId>
    <!--  要修改版本号，直接改最外层pom的revision  -->
    <version>${revision}.${sha1}-${changelist}</version>
    <name>cem-compat</name>
    <description>cem compat project</description>

    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-customer-core</artifactId>
        </dependency>
    </dependencies>

</project>