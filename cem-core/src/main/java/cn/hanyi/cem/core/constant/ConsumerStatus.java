package cn.hanyi.cem.core.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@Getter
public enum ConsumerStatus {

    FAILED(false),
    SUCCESS(true),
    CANCELED(true);     // 取消状态，也认定为消费成功

    private final boolean success;

    ConsumerStatus(boolean success) {
        this.success = success;
    }

    public static ConsumerStatus parse(Object s) {
        String status = s == null ? null : s.toString();
        if (StringUtils.isEmpty(status)) {
            return null;
        }
        return Arrays.stream(values()).filter(i -> i.name().equals(status)).findFirst().orElse(null);
    }
}
