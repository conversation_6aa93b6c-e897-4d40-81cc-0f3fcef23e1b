package cn.hanyi.cem.core.constant;

import cn.hanyi.cem.core.dto.event.*;

import static cn.hanyi.cem.core.constant.WorkerType.*;


public enum EventType implements IWorkerMsgType {
    SURVEY_CREATE(EVENT100, "survey", EventSurveyDto.class),
    SURVEY_UPDATE(EVENT100, "survey", EventSurveyDto.class),
    SURVEY_ENABLE(EVENT100, "survey", EventSurveyDto.class),
    SURVEY_DISABLE(EVENT100, "survey", EventSurveyDto.class),
    SURVEY_DELETE(EVENT100, "survey", EventSurveyDto.class),
    SURVEY_CONTENT_AUDIT(EVENT100, "survey", EventSurveyContentAuditDto.class),
    SURVEY_REPORT(EVENT100, "survey", EventSurveyReportDto.class),
    SURVEY_MANUAL_CHECK(EVENT100, "survey", EventSurveyManualCheckDto.class),

    QUESTION_UPDATE(EVENT100, "question", EventQuestionDto.class),
    QUESTION_TYPE_UPDATE(EVENT100, "question", EventQuestionTypeDto.class),
    QUESTION_DELETE(EVENT100, "question", EventQuestionDto.class),
    QUESTION_ITEM_UPDATE(EVENT100, "question", EventQuestionItemDto.class),
    QUESTION_ITEM_DELETE(EVENT100, "question", EventQuestionItemDto.class),

    RESPONSE_CREATE(EVENT200, "response", EventResponseDto.class),
    RESPONSE_VIEW(EVENT200, "response", EventResponseDto.class),
    RESPONSE_SUBMIT(EVENT200, "response", EventResponseDto.class),
    RESPONSE_SUBMIT_FINAL(EVENT200, "response", EventResponseDto.class),
    RESPONSE_IMPORT(EVENT500, "response", EventResponseDto.class),
    RESPONSE_INVALID(EVENT200, "response", EventResponseDto.class),
    RESPONSE_RECOVER(EVENT200, "response", EventResponseDto.class),
    RESPONSE_DELETE(EVENT200, "response", EventResponseDto.class),
    RESPONSE_DELETE_BY_SURVEY(EVENT200, "response", EventResponseDto.class),
    RESPONSE_DELETE_BY_CHANNEL(EVENT200, "response", EventResponseDto.class),
    RESPONSE_SYNC_DYNAMIC_ITEM(EVENT200, "response", EventResponseSyncDynamicItemDto.class),

    CHANNEL_CREATE(EVENT300, "channel", EventChannelDto.class),
    CHANNEL_PAUSE(EVENT300, "channel", EventChannelDto.class),
    CHANNEL_CLOSE(EVENT300, "channel", EventChannelDto.class),

    DEPARTMENT_CREATE(EVENT300, "department", EventDepartmentDto.class),
    DEPARTMENT_DELETE(EVENT300, "department", EventDepartmentDto.class),

    API_KEY_REFRESH(EVENT300, "apikey", EventApiKeyDto.class),


    WARNING(EVENT300, "warning", EventWarningDto.class),
    WARNING_COOPERATE(EVENT300, "warning", EventWarningCooperateDto.class),
    WARNING_CLOSE(EVENT300, "warning", EventWarningCloseDto.class),
    WARNING_CHANGE_STATUS(EVENT300, "warning", EventWarningChangeStatusDto.class),

    RULE_CHANGE(EVENT300, "warning", EventRuleChangeDto.class),

    //    CUSTOMER_ADD_JOURNEY(EVENT, "customer", EventCustomerAddJourneyDto.class),
    CUSTOMER_STAT(EVENT300, "customer", EventCustomerStatDto.class),

    RESOURCE_PERMISSION_ADD_USER(EVENT400, "resourcePermission", EventResourcePermissionUserChangeDto.class),
    RESOURCE_PERMISSION_REMOVE_USER(EVENT400, "resourcePermission", EventResourcePermissionUserChangeDto.class),

    USER_REGISTER(EVENT400, "auth", EventUserRegisterDto.class),
    USER_LOGIN(EVENT400, "user", EventUserLoginDto.class),
    USER_DELETE(EVENT400, "user", EventUserDto.class),

    ORG_UPDATE_NAME(EVENT400, "org", EventOrgUpdateNameDto.class),

    JOURNEY_INDICATOR_CREATE(EVENT400, "journey", EventIndicatorCreateDto.class),

    RECHARGE_COMPLETED(EVENT400, "recharge", EventRechargeDto.class),
    RECHARGE_EXPIRED(EVENT400, "recharge", EventRechargeDto.class),
    RECHARGE_REFUND_COMPLETED(EVENT400, "recharge", EventRechargeRefundDto.class),
    RECHARGE_REFUND_QUERY(EVENT400, "recharge", EventRechargeRefundDto.class),

    ORDER_COMPLETED(EVENT400, "order", EventOrderDto.class),

    SURVEY_LOTTERY_CLOSE(EVENT400, "surveyLottery", EventSurveyLotteryDto.class),
    SURVEY_LOTTERY_DELETE(EVENT400, "surveyLottery", EventSurveyLotteryDto.class),
    REFUND_RED_PACK(EVENT400, "refund", EventRefundRedPackDto.class),
    SEND_RED_PACK_DELAY(EVENT400, "order", EventSendRedPackDelayDto.class),

    WECHAT_OPEN_AUTHORIZE(EVENT400, "wechatOpen", EventWechatOpenAuthorizeDto.class),
    WECHAT_OPEN_DELETE(EVENT400, "wechatOpen", EventWechatOpenDeleteDto.class),
    WECHAT_OPEN_SUBSCRIBE(EVENT400, "wechatOpen", EventWechatOpenSubscribeDto.class),
    WECHAT_OPEN_UNSUBSCRIBE(EVENT400, "wechatOpen", EventWechatOpenSubscribeDto.class),

    ADMINX_CHANNEL_ORDER_REQUEST(EVENT400, "adminxChannel", EventAdminxChannelOrderRequest.class),
    ADMINX_CHANNEL_ORDER_REJECT(EVENT400, "adminxChannel", EventAdminxChannelOrderReject.class),
    ADMINX_CHANNEL_ORDER_QUOTED(EVENT400, "adminxChannel", EventAdminxChannelOrderQuoted.class),
    ADMINX_CHANNEL_ORDER_PAID(EVENT400, "adminxChannel", EventAdminxChannelOrderPaid.class),
    ADMINX_CHANNEL_ORDER_START_END(EVENT400, "adminxChannel", EventAdminxChannelOrderStartEnd.class),
    ADMINX_CHANNEL_ORDER_REFUND(EVENT400, "adminxChannel", EventAdminxChannelOrderRefund.class),
    ADMINX_CHANNEL_ORDER_RE_REFUND(EVENT400, "adminxChannel", EventAdminxChannelOrderRefund.class),

    DATA_ACCESS_HANDLE_MESSAGE(EVENT400, "dataAccess", EventDataAccessHandleMessageDto.class),
    ;

    private final WorkerType workerType;
    public final String group;
    public final Class<?> paramClass;

    EventType(WorkerType workerType, String group, Class<?> paramClass) {
        this.workerType = workerType;
        this.group = group;
        this.paramClass = paramClass;
    }


    @Override
    public WorkerType getWorkerType() {
        return workerType;
    }

    @Override
    public String getEumName() {
        return this.name();
    }


}
