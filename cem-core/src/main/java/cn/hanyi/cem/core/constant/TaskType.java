package cn.hanyi.cem.core.constant;

import cn.hanyi.cem.core.dto.task.*;
import lombok.Getter;

import static cn.hanyi.cem.core.constant.WorkerType.*;


@Getter
public enum TaskType implements IWorkerMsgType {

    SEND_CHANNEL_SURVEY(TASK100, "channelSend", TaskSendChannelSurveyDto.class),
    RESEND_CHANNEL_SURVEY_DELAY(TASK100, "channelSend", TaskResendChannelSurveyDelayDto.class),

    CUSTOMER_SEND_API(TASK200, "customerSend", TaskCustomerSendApiDto.class),
    CUSTOMER_SEND_SMS(TASK200, "customerSend", TaskCustomerSendSmsDto.class),
    CUSTOMER_SEND_WECHAT(TASK200, "customerSend", TaskCustomerSendWechatDto.class),
    CUSTOMER_SEND_EMAIL(TASK200, "customerSend", TaskCustomerSendEmailDto.class),
    CUSTOMER_SEND_COMPOSITED(TASK200, "customerSend", TaskCustomerSendCompositedDto.class),

    WEBHOOK(TASK300, "webhook", TaskWebHookDto.class),

    WARNING_NOTIFY(TASK300, "warning", TaskNotifyWarningDto.class),
    WARNING_NOTIFY_JOURNEY(TASK300, "warning", TaskNotifyWarningJourneyDto.class),
    WARNING_NOTIFY_INBOX(TASK300, "warning", TaskNotifyInboxDto.class),
    WARNING_NOTIFY_OUTWORKER(TASK300, "warning", TaskNotifyOutWorkerDto.class),
    WARNING_BOT_NOTIFY(TASK300, "warning", TaskBotNotifyDto.class),
    WARNING_RULE_RERUN(TASK301, "warning", TaskProcessWarningDto.class),

    EVENT_GROUP_NOTIFY_USER(TASK300, "eventGroup", TaskEventGroupNotifyDto.class),
    EVENT_GROUP_NOTIFY_ROBOT(TASK300, "eventGroup", TaskEventGroupNotifyDto.class),

    CUSTOMER_BATCH_ADD_JOURNEY(TASK400, "customer", TaskCustomerBatchAddJourneyDto.class),
    CUSTOMER_THIRDPARTY_MESSAGE_TRIGGER_SEND_MANAGE(TASK400, "customer", TaskCustomerThirdpartyTriggerSendManageDto.class),

    WECHAT_OPEN_SYNC_CUSTOMER_LIST(TASK400, "wechatCustomer", TaskWechatOpenSyncCustomerListDto.class),
    WECHAT_OPEN_SYNC_CUSTOMER_INFO(TASK400, "wechatCustomer", TaskWechatOpenSyncCustomerInfoDto.class),
    WECHAT_OPEN_SYNC_TEMPLATE(TASK400, "wechatCustomer", TaskWechatOpenSyncTemplateDto.class),

    RESPONSE_DOWNLOAD(TASK400, "response", TaskResponseDownloadDto.class),
    RESPONSE_DOWNLOAD_ATTACHMENT(TASK500, "response", TaskResponseDownloadDto.class),
    RESPONSE_CALC_QUOTA(TASK400, "response", TaskResponseDownloadDto.class),
    RESPONSE_IMPORT(TASK600, "response", TaskResponseImportDto.class),

    SMS_NOTIFICATION_DELAY(TASK400, "notification", TaskSmsNotificationDelayDto.class),

    ORG_CHANGE_NOTIFY(TASK400, "orgChange", TaskOrgChangeDto.class),

    /**
     * 数据看板推送
     */
    BI_DASHBOARD_PUSH(TASK, "dashboard", TaskDashboardPushDto.class),
    BI_DASHBOARD_PUSH_BOT(TASK, "dashboard", TaskDashboardPushDto.class),
    BI_ANALYSE_TEXT_PROJECT(TASK, "textProject", TaskProcessTextProjectDto.class),

    CUSTOMER_RESET_DEPARTMENT(TASK, "customer", TaskCustomerResetDepartmentDto.class),

    QUOTA_SYNC(TASK500, "quota", TaskSyncQuotaDto.class),
    SURVEY_TRANSLATE(TASK, "translateSurvey", TaskSurveyTranslateDto.class),
    ;

    public final WorkerType workerType;
    public final String group;
    public final Class<?> paramClass;

    TaskType(WorkerType workerType, String group, Class<?> paramClass) {
        this.workerType = workerType;
        this.group = group;
        this.paramClass = paramClass;
    }


    @Override
    public WorkerType getWorkerType() {
        return workerType;
    }

    @Override
    public String getEumName() {
        return this.name();
    }


}
