package cn.hanyi.cem.core.constant;

import lombok.Getter;

@Getter
public enum WorkerStatus {
    INIT(true),
    DELAY(true),
    RESET(true),  // 重置状态，可以再次执行
    RUNNING,
    FAILED,
    SUCCESS,
    IGNORED,
    CANCELED;

    private final boolean runnable; // 当前状态的是否可以运行

    WorkerStatus() {
        this(false);
    }

    WorkerStatus(boolean runnable) {
        this.runnable = runnable;
    }
}
