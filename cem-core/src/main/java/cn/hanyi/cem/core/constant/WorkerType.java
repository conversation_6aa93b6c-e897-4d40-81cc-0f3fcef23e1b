package cn.hanyi.cem.core.constant;

import lombok.Getter;

@Getter
public enum WorkerType {
    EVENT("事件-默认队列", WorkerQueueName.EVENT),
    EVENT100("事件-队列100", WorkerQueueName.EVENT100),
    EVENT200("事件-队列200", WorkerQueueName.EVENT200),
    EVENT300("事件-队列300", WorkerQueueName.EVENT300),
    EVENT400("事件-队列400", WorkerQueueName.EVENT400),
    EVENT500("事件-队列500", WorkerQueueName.EVENT_IMPORT_RESPONSE),
    TASK("任务-默认队列", WorkerQueueName.TASK),
    TASK100("任务-队列100", WorkerQueueName.TASK100),
    TASK200("任务-队列200", WorkerQueueName.TASK200),
    TASK300("任务-队列300", WorkerQueueName.TASK300),
    TASK301("任务-队列301", WorkerQueueName.TASK301),
    TASK400("任务-队列400", WorkerQueueName.TASK400),
    TASK500("任务-队列500", WorkerQueueName.TASK_DOWNLOAD_ATTACHMENT),
    TASK600("任务-队列600", WorkerQueueName.TASK_IMPORT_RESPONSE),
    ;

    private final String text;
    private final String queue;
    WorkerType(String text,String queue) {
        this.text = text;
        this.queue = queue;
    }
}
