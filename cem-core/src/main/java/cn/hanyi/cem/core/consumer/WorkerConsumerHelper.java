package cn.hanyi.cem.core.consumer;

import static cn.hanyi.cem.core.constant.WorkerStatus.FAILED;
import static cn.hanyi.cem.core.constant.WorkerStatus.IGNORED;
import static cn.hanyi.cem.core.constant.WorkerStatus.RUNNING;
import static cn.hanyi.cem.core.constant.WorkerStatus.SUCCESS;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.WorkerStatus;
import cn.hanyi.cem.core.constant.WorkerType;
import cn.hanyi.cem.core.dto.WorkerDto;
import cn.hanyi.cem.core.entity.CemWorkerBase;
import cn.hanyi.cem.core.repository.CemWorkerRepository;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ThreadUtils;
import org.aspectj.weaver.NewParentTypeMunger;
import org.befun.core.utils.JsonHelper;
import org.befun.task.metrics.TaskMetrics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

import static cn.hanyi.cem.core.constant.WorkerStatus.*;

@Slf4j
public abstract class WorkerConsumerHelper<
        E extends CemWorkerBase,
        R extends CemWorkerRepository<E, Long>,
        C extends IWorkerConsumer<E, Object>
        > {
    private static final int THREAD_SIZE = Math.max(Runtime.getRuntime().availableProcessors(), 8);
    private final ExecutorService commonPool = Executors.newFixedThreadPool(THREAD_SIZE);
    /**
     * 异步执行的并发数, 和线程池保持一致
     */
    private final Semaphore semaphore = new Semaphore(THREAD_SIZE);
    @Autowired
    private R repository;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired(required = false)
    private TaskMetrics taskMetrics;

    public abstract List<C> getAllConsumers(E entity);

    public abstract Object parseParam(E entity);

    public void consumer(WorkerDto dto) {
        if (dto == null || dto.getType() == null || dto.getWorkerId() == null || dto.getWorkerId() <= 0) {
            log.warn("dto不完整：{}", JsonHelper.toJson(dto));
            return;
        }
        E entity = repository.findById(dto.getWorkerId()).orElse(null);
        if (entity == null) {
            log.warn("entity不存在：{}", JsonHelper.toJson(dto));
            return;
        }
        if (entity.getStatus() != null && !entity.getStatus().isRunnable()) { // 不是可运行状态
            log.warn("{}已处理：{}", dto.getType().getText(), JsonHelper.toJson(entity));
            return;
        }
        List<C> consumers = getAllConsumers(entity);
        if (CollectionUtils.isEmpty(consumers)) {
            log.warn("{}已忽略：{}", dto.getType().getText(), JsonHelper.toJson(entity));
            entity.setStatus(IGNORED);
        } else {
            WorkerStatus workerStatus = FAILED;
            Object param = parseParam(entity);
            if (param == null) {
                log.warn("{}参数解析失败：{}", dto.getType().getText(), JsonHelper.toJson(entity));
            } else {
                if (dto.isConsumerAsync()) {
                    workerStatus = asyncConsumerAllCommonPool(consumers, entity, param, dto.getType());
                } else {
                    workerStatus = syncConsumerAll(consumers, entity, param, null, dto.getType());
                }
            }
            entity.setStatus(workerStatus);
        }
        repository.save(entity);
    }

    /**
     * 异步执行所有的消费者，公共线程池(并发控制，超过信号量，阻塞消费者线程)
     */
    private WorkerStatus asyncConsumerAllCommonPool(List<C> consumers, E entity, Object param, WorkerType type) {
        WorkerStatus workerStatus = FAILED;
        try {
            Semaphore lock = semaphore;
            lock.acquire(); // 获取令牌，保证最多只有n个异步执行，如果没有
            try {
                commonPool.execute(() -> {
                    WorkerStatus status = syncConsumerAll(consumers, entity, param, lock, type);
                    entity.setStatus(status);    // 这里永远会执行，应为上一行已经把异常捕捉了，返回了一个状态
                    repository.save(entity);
                });
                workerStatus = RUNNING;
            } catch (Throwable e) {
                lock.release(); // 如果添加异步任务失败，则立即释放令牌
                throw e;
            }
        } catch (Throwable e) {
            log.error("{}处理失败：id={}", type.getText(), entity.getId(), e);
        }
        return workerStatus;
    }

    /**
     * 同步执行所有的消费者
     */
    private WorkerStatus syncConsumerAll(List<C> consumers, E entity, Object param, Semaphore lock, WorkerType type) {
        WorkerStatus workerStatus = FAILED;
        Map<String, ConsumerStatus> allConsumerStatus = new HashMap<>();
        List<String> failedResponseMessages = new ArrayList<>();
        try {
            // 解析所有消费者的状态
            parseAllConsumerStatus(allConsumerStatus, entity.getConsumerStatus());
            // 解析所有消费者的失败日志
            parseAllFailedResponseMessages(failedResponseMessages, entity.getResponse());
            if (allConsumerStatus.isEmpty()) {
                // 第一次消费，所有的消费者都执行一次
                consumers.forEach(consumer -> {
                    ConsumerStatus consumerStatus = syncConsumer(consumer, entity, param, failedResponseMessages, type);
                    // 上面的消费者不管是否执行成功都会有一个返回状态
                    allConsumerStatus.put(consumer.name(), consumerStatus);
                });
            } else {
                // 有消费记录，这里是重新执行，找到成功了的
                HashMap<String, ConsumerStatus> successConsumers = new HashMap<>();
                allConsumerStatus.forEach((consumerName, status) -> {
                    if (status.isSuccess()) {
                        successConsumers.put(consumerName, status);
                    }
                });
                allConsumerStatus.clear(); // 重置所有的消费者状态，并重新添加成功了的消费者
                allConsumerStatus.putAll(successConsumers);
                // 重新遍历所有的消费者，如果此消费者已经执行成功了，则跳过
                consumers.forEach(consumer -> {
                    if (!successConsumers.containsKey(consumer.name())) {
                        ConsumerStatus consumerStatus = syncConsumer(consumer, entity, param, failedResponseMessages, type);
                        // 上面的消费者不管是否执行成功都会有一个返回状态
                        allConsumerStatus.put(consumer.name(), consumerStatus);
                    }
                });
            }
            if (allConsumerStatus.values().stream().allMatch(ConsumerStatus::isSuccess)) {
                // 所有消费者都成功了，则状态才是成功
                workerStatus = SUCCESS;
            }
        } catch (Throwable e) {
            log.error("{}处理失败：id={}", type.getText(), entity.getId(), e);
        } finally {
            if (lock != null) {
                lock.release();
            }
            // 重新把所有消费者的状态，写入消费者状态中
            entity.setConsumerStatus(JsonHelper.toJson(allConsumerStatus));
            // 重新把所有消费者的失败日志，写入消费者响应中
            if (!failedResponseMessages.isEmpty()) {
                entity.setResponse(JsonHelper.toJson(failedResponseMessages));
            }
            log.info("{}处理结束：id={}, status={}", type.getText(), entity.getId(), workerStatus.name());
        }
        return workerStatus;
    }

    /**
     * 单个消费者处理
     */
    private ConsumerStatus syncConsumer(C consumer, E entity, Object param, List<String> failedResponseMessages, WorkerType type) {
        ConsumerStatus consumerStatus = ConsumerStatus.FAILED;
        String exceptionMessage = null;
        long startMs = System.currentTimeMillis();
        try {
            entity.setResponse(null);
            consumerStatus = consumer.consumer(entity, param);
        } catch (Throwable e) {
            exceptionMessage = e.getMessage();
            log.error("{}处理失败：consumer={}, id={}", type.getText(), consumer.name(), entity.getId(), e);
        } finally {
            Optional.ofNullable(taskMetrics).ifPresent(metrics -> metrics.tagTimer(consumer.name(), type.getQueue(), startMs));
            autoUpdateProgress(consumer, entity, consumerStatus, param);
            Optional.ofNullable(buildFailedMessage(consumer, entity, exceptionMessage)).ifPresent(failedResponseMessages::add);
            log.info("{}处理结束：consumer={}, id={}, status={}", type.getText(), consumer.name(), entity.getId(), consumerStatus.name());
        }
        return consumerStatus;
    }

    protected void autoUpdateProgress(C consumer, E entity, ConsumerStatus consumerStatus, Object param) {

    }

    private String buildFailedMessage(C consumer, E entity, String exceptionMessage) {
        if (StringUtils.isNotEmpty(entity.getResponse()) || StringUtils.isNotEmpty(exceptionMessage)) {
            StringBuilder message = new StringBuilder();
            message.append(LocalDateTime.now());
            message.append(",");
            message.append(consumer.name());
            if (StringUtils.isNotEmpty(entity.getResponse())) {
                message.append(",");
                message.append(entity.getResponse());
            }
            if (StringUtils.isNotEmpty(exceptionMessage)) {
                message.append(",");
                if (exceptionMessage.length() <= 200) {
                    message.append(exceptionMessage);
                } else {
                    message.append(exceptionMessage, 0, 200);
                }
            }
            return message.toString();
        }
        return null;
    }

    /**
     * 解析所有消费者的处理状态
     */
    private void parseAllConsumerStatus(Map<String, ConsumerStatus> allConsumerStatus, String consumerStatus) {
        Map<String, Object> parsedMap = JsonHelper.toMap(consumerStatus);
        if (parsedMap != null) {
            parsedMap.forEach((consumerName, status) -> {
                Optional.ofNullable(ConsumerStatus.parse(status)).ifPresent(s -> {
                    allConsumerStatus.put(consumerName, s);
                });
            });
        }
    }

    /**
     * 解析所有消费者的处理异常信息
     */
    private void parseAllFailedResponseMessages(List<String> failedResponseMessages, String consumerResponse) {
        List<String> parsedList = JsonHelper.toList(consumerResponse, String.class);
        if (parsedList != null) {
            failedResponseMessages.addAll(parsedList);
        }
    }
}
