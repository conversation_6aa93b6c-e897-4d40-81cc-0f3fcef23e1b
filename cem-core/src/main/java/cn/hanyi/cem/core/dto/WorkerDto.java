package cn.hanyi.cem.core.dto;

import cn.hanyi.cem.core.constant.WorkerType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.task.BaseTaskDetailDto;

/**
 * 发布任务时只需要类型和id
 * 实现的service自行查content转换成对应dto
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WorkerDto extends BaseTaskDetailDto {
    WorkerType type;
    Long workerId;
    boolean consumerAsync;
    boolean withNewPool = false;
}
