package cn.hanyi.cem.core.dto.event;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class EventCustomerStatDto {
    private static final String FROM_JOURNEY = "journey";
    private static final String FROM_SEND_SURVEY = "sendSurvey";
    private static final String FROM_SUBMIT_SURVEY = "submitSurvey";
    private static final String FROM_TRIGGER_EVENT = "triggerEvent";

    private Long customerId;
    private String fromType; // journey sendSurvey submitSurvey triggerEvent

    public static EventCustomerStatDto fromJourney(Long customerId) {
        return new EventCustomerStatDto(customerId, FROM_JOURNEY);
    }

    public static EventCustomerStatDto fromSendSurvey(Long customerId) {
        return new EventCustomerStatDto(customerId, FROM_SEND_SURVEY);
    }

    public static EventCustomerStatDto fromSubmitSurvey(Long customerId) {
        return new EventCustomerStatDto(customerId, FROM_SUBMIT_SURVEY);
    }

    public static EventCustomerStatDto fromTriggerEvent(Long customerId) {
        return new EventCustomerStatDto(customerId, FROM_TRIGGER_EVENT);
    }
}
