package cn.hanyi.cem.core.dto.event;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Setter
@Getter
@NoArgsConstructor
public class EventIndicatorCreateDto extends EventAuthDto{
    @NotNull
    private Long journeyMapId;

    @NotNull
    private Long componentId;

    @NotNull
    private Long indicatorId;

    public EventIndicatorCreateDto(@NotNull Long orgIdId , @NotNull Long userId, @NotNull Long journeyMapId, @NotNull Long componentId, @NotNull Long indicatorId) {
        super(userId, orgIdId);
        this.journeyMapId = journeyMapId;
        this.componentId = componentId;
        this.indicatorId = indicatorId;
    }
}
