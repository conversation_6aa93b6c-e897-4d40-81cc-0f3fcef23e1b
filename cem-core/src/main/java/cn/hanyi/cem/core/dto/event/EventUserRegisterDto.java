package cn.hanyi.cem.core.dto.event;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Setter
@Getter
@NoArgsConstructor
public class EventUserRegisterDto extends EventAuthDto {

    private String customer;

    private String password;

    private String companyName;

    public EventUserRegisterDto(@NotNull Long userId , @NotNull Long orgId, String password, String companyName, String customer) {
        super(userId, orgId);
        this.customer = customer;
        this.password = password;
        this.companyName = companyName;

    }
}
