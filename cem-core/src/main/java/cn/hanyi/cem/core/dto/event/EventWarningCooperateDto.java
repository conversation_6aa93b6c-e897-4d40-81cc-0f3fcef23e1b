package cn.hanyi.cem.core.dto.event;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventWarningCooperateDto {

    private Long warningId;
    private Long fromUserId;
    private Set<Long> toUserIds;
    private String mark;
    // WECHAT ,EMAIL, SMS, WECHAT_WORK;
    private List<String> types = new ArrayList<>();
}
