package cn.hanyi.cem.core.dto.task;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskBotNotifyDto extends TaskNotifyBaseDto {
    private Set<Long> warningRuleIds;

    private Long responseId;

    private Long warningId;

    public TaskBotNotifyDto(Long orgId, Set<Long> warningRuleIds, Long responseId, Long warningId) {
        super.setOrgId(orgId);
        this.warningRuleIds = warningRuleIds;
        this.responseId = responseId;
        this.warningId = warningId;
    }


}
