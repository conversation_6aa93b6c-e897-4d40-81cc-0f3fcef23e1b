package cn.hanyi.cem.core.dto.task;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.progress.TaskProgressiveDto;
import cn.hanyi.cem.core.dto.task.send.SendApiInfo;
import cn.hanyi.cem.core.dto.task.send.SendFromInfo;
import cn.hanyi.ctm.workertrigger.dto.CustomerSendApiDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskCustomerSendApiDto extends TaskProgressiveDto implements SendApiInfo, SendFromInfo {

    private CustomerSendFromType fromType;

    private Long fromId;

    private Long connectorId;

    private Boolean isRemind = false;

    private String body;

    private String surveyUrl = "";

    private Long sceneId;

    private Long customerId;

    private Object externalUserId;

    private Long departmentId;


    public static TaskCustomerSendApiDto mapFromJourney(CustomerSendApiDto from) {
        TaskCustomerSendApiDto dto = new TaskCustomerSendApiDto(
                CustomerSendFromType.JOURNEY_INTERACTION,
                from.getJourneyRecordId(),
                from.getConnectorId(),
                false,
                null,
                from.getSurveyUrl(),
                from.getSceneId(),
                from.getCustomerId(),
                from.getExternalUserId(),
                from.getDepartmentId());
        dto.setTaskProgressId(from.getTaskProgressId());
        dto.setTotalSize(0);
        return dto;
    }

}
