package cn.hanyi.cem.core.dto.task;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.progress.TaskProgressiveDto;
import cn.hanyi.cem.core.dto.task.send.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.utils.DateHelper;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskCustomerSendCompositedDto extends TaskProgressiveDto implements SendFromInfo {

    private CustomerSendFromType fromType;

    private Long fromId;

    /**
     * 如果任务大量堆积导致消费不及时，所以需要判断，当前时间是否在有效时间之内
     */
    private String deadTime;

    /**
     * 短信后付费
     */
    private boolean smsPostPaid;

    /**
     * 是否是催答发送
     */
    private boolean isRemind;

    /**
     * 发送渠道
     */
    private List<CompositedSms> sms = new ArrayList<>();
    private List<CompositedWechat> wechat = new ArrayList<>();
    private List<CompositedEmail> email = new ArrayList<>();
    private List<CompositedApi> api = new ArrayList<>();

    public boolean checkCurrentTime() {
        LocalDateTime s = DateHelper.parseDateTime(deadTime);
        LocalDateTime now = LocalDateTime.now();
        return s == null || now.isBefore(s);
    }

    public boolean prioritySend(
            Function<CompositedSms, Boolean> sendSms,
            Function<CompositedWechat, Boolean> sendWechat,
            Function<CompositedEmail, Boolean> sendEmail,
            Function<CompositedApi, Boolean> sendApi
    ) {
        for (int i = 0; i < 3; i++) {
            if (prioritySend(i, sendSms, sendWechat, sendEmail, sendApi)) {
                return true;
            }
        }
        return false;
    }

    private boolean prioritySend(
            int priority,
            Function<CompositedSms, Boolean> sendSms,
            Function<CompositedWechat, Boolean> sendWechat,
            Function<CompositedEmail, Boolean> sendEmail,
            Function<CompositedApi, Boolean> sendApi
    ) {
        AtomicInteger successCount = new AtomicInteger(0);
        send(priority, successCount, sms, sendSms);
        send(priority, successCount, wechat, sendWechat);
        send(priority, successCount, email, sendEmail);
        send(priority, successCount, api, sendApi);
        return successCount.get() > 0;
    }

    private <C extends Composited> void send(int priority, AtomicInteger successCount, List<C> list, Function<C, Boolean> send) {
        if (CollectionUtils.isNotEmpty(list) && send != null) {
            list.forEach(c -> {
                if (c.getPriority() == priority) {
                    Boolean success = send.apply(c);
                    if (success != null && success) {
                        successCount.incrementAndGet();
                    }
                }
            });
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Composited {
        private int priority;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompositedSms extends Composited implements SendSmsInfo {
        private String content;
        private String mobile;
        private String templateId;
        private String templateName;
        private String templateContent;
        private String signId;
        private String realSign;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompositedWechat extends Composited implements SendWechatInfo {
        private Long thirdpartyAuthId;
        private String appId;
        private String templateId;
        private String openId;
        private String message;
        private String url;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompositedEmail extends Composited implements SendEmailInfo {
        private String content;
        private String email;
        private String sender;
        private String title;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompositedApi extends Composited implements SendApiInfo {
        private Long connectorId;
        private String body;
    }

}
