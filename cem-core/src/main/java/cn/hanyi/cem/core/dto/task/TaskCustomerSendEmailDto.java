
package cn.hanyi.cem.core.dto.task;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.progress.TaskProgressiveDto;
import cn.hanyi.cem.core.dto.task.send.SendEmailInfo;
import cn.hanyi.cem.core.dto.task.send.SendFromInfo;
import cn.hanyi.survey.workertrigger.dto.CustomerSendEmailDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskCustomerSendEmailDto extends TaskProgressiveDto implements SendEmailInfo, SendFromInfo {

    private CustomerSendFromType fromType;

    private Long fromId;

    private String content;

    private String email;

    private String sender;

    private String title;

    public static TaskCustomerSendEmailDto mapFromSurveyChannel(CustomerSendEmailDto from) {
        TaskCustomerSendEmailDto dto = new TaskCustomerSendEmailDto(
                CustomerSendFromType.SURVEY_CHANNEL,
                from.getChannelRecordId(),
                from.getContent(),
                from.getEmail(),
                from.getSender(),
                from.getTitle());
        dto.setTaskProgressId(from.getTaskProgressId());
        return dto;
    }

}