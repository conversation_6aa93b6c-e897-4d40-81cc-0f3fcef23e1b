
package cn.hanyi.cem.core.dto.task;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.progress.TaskProgressiveDto;
import cn.hanyi.cem.core.dto.task.send.SendFromInfo;
import cn.hanyi.cem.core.dto.task.send.SendSmsInfo;
import cn.hanyi.ctm.workertrigger.dto.CustomerSendSmsDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskCustomerSendSmsDto extends TaskProgressiveDto implements SendSmsInfo, SendFromInfo {

    private CustomerSendFromType fromType;

    private Long fromId;

    private String content;

    private String mobile;

    private String templateId;

    private String templateName;

    private String templateContent;

    private String signId;
    private String realSign;

    public static TaskCustomerSendSmsDto mapFromCtm(CustomerSendSmsDto from) {
        TaskCustomerSendSmsDto dto;
        if (CustomerSendFromType.JOURNEY_INTERACTION.name().equals(from.getFrom())) {
            dto = new TaskCustomerSendSmsDto(
                    CustomerSendFromType.JOURNEY_INTERACTION,
                    from.getJourneyRecordId(),
                    from.getContent(),
                    from.getMobile(),
                    from.getTemplateId(),
                    from.getTemplateName(),
                    null,
                    from.getSignId(),
                    from.getRealSign());
            dto.setTaskProgressId(from.getTaskProgressId());
            dto.setTotalSize(0);
        } else {
            dto = new TaskCustomerSendSmsDto(
                    CustomerSendFromType.EVENT_NOTIFY_CUSTOMER,
                    from.getEventId(),
                    from.getContent(),
                    from.getMobile(),
                    from.getTemplateId(),
                    from.getTemplateName(),
                    null,
                    from.getSignId(),
                    from.getRealSign());
            dto.setTaskProgressId(from.getTaskProgressId());
        }
        return dto;
    }

    public static TaskCustomerSendSmsDto mapFromSurveyChannel(cn.hanyi.survey.workertrigger.dto.CustomerSendSmsDto from) {
        TaskCustomerSendSmsDto dto = new TaskCustomerSendSmsDto(
                CustomerSendFromType.SURVEY_CHANNEL,
                from.getChannelRecordId(),
                from.getContent(),
                from.getMobile(),
                from.getTemplateId(),
                from.getTemplateName(),
                null,
                from.getSignId(),
                from.getRealSign());
        dto.setTaskProgressId(from.getTaskProgressId());
        return dto;
    }

}