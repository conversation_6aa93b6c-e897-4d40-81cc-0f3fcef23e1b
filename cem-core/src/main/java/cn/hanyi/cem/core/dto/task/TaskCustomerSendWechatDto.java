package cn.hanyi.cem.core.dto.task;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.progress.TaskProgressiveDto;
import cn.hanyi.cem.core.dto.task.send.SendFromInfo;
import cn.hanyi.cem.core.dto.task.send.SendWechatInfo;
import cn.hanyi.ctm.workertrigger.dto.CustomerSendWechatDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskCustomerSendWechatDto extends TaskProgressiveDto implements SendWechatInfo, SendFromInfo {

    private CustomerSendFromType fromType;
    private Long fromId;

    private Long thirdpartyAuthId;

    private String appId;
    private String templateId;
    private String openId;
    private String message;
    private String url;

    public static TaskCustomerSendWechatDto mapFromCtm(CustomerSendWechatDto from) {
        if (CustomerSendFromType.EVENT_NOTIFY_CUSTOMER.name().equals(from.getFrom())) {
            return mapFromEventNotify(from);
        } else {
            return mapFromJourney(from);
        }
    }

    public static TaskCustomerSendWechatDto mapFromJourney(CustomerSendWechatDto from) {
        TaskCustomerSendWechatDto dto = new TaskCustomerSendWechatDto(
                CustomerSendFromType.JOURNEY_INTERACTION,
                from.getJourneyRecordId(),
                from.getThirdpartyAuthId(),
                from.getAppId(),
                from.getTemplateId(),
                from.getOpenId(),
                from.getMessage(),
                from.getUrl());
        dto.setTaskProgressId(from.getTaskProgressId());
        dto.setTotalSize(0);
        return dto;
    }

    public static TaskCustomerSendWechatDto mapFromEventNotify(CustomerSendWechatDto from) {
        TaskCustomerSendWechatDto dto = new TaskCustomerSendWechatDto(
                CustomerSendFromType.EVENT_NOTIFY_CUSTOMER,
                from.getEventId(),
                from.getThirdpartyAuthId(),
                from.getAppId(),
                from.getTemplateId(),
                from.getOpenId(),
                from.getMessage(),
                from.getUrl());
        dto.setTaskProgressId(from.getTaskProgressId());
        return dto;
    }


    public static TaskCustomerSendWechatDto mapFromSurveyChannel(cn.hanyi.survey.workertrigger.dto.CustomerSendWechatDto from) {
        TaskCustomerSendWechatDto dto = new TaskCustomerSendWechatDto(
                CustomerSendFromType.SURVEY_CHANNEL,
                from.getChannelRecordId(),
                from.getThirdpartyAuthId(),
                from.getAppId(),
                from.getTemplateId(),
                from.getOpenId(),
                from.getMessage(),
                from.getUrl());
        dto.setTaskProgressId(from.getTaskProgressId());
        return dto;
    }


}
