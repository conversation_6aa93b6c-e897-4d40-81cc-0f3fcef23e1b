package cn.hanyi.cem.core.dto.task;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskDashboardPushDto extends TaskNotifyBaseDto {

    private Long dashboardId;
    private String template;
    private List<PushChannel> channels = new ArrayList<>();
    private Map<String, Object> params = new HashMap<>();
    private Map<Long, String> userScreenShotUrlMap = new HashMap<>();
    private String botScreenShotUrl;

    public TaskDashboardPushDto(Long dashboardId, String templateName, Map<String, Object> params,
            Long orgId, String resourceType) {
        this.dashboardId = dashboardId;
        this.template = templateName;
        this.params = params;
        this.setOrgId(orgId);
        this.setResourceType(resourceType);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PushChannel implements Serializable {
        // EMAIL,WECHAT_WORK
        private String connectorType;
        // LINK, LINK_IMAGE, LINK_IMAGE_PGF
        private String pushContentType;
        private String gateWay;
    }

}
