package cn.hanyi.cem.core.dto.task;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskEventGroupNotifyDto {
    private Long eventGroupId;
    private String url;
    private Set<Long> userIds = new HashSet<>();

    public TaskEventGroupNotifyDto(Long eventGroupId, String url) {
        this.eventGroupId = eventGroupId;
        this.url = url;
    }
}
