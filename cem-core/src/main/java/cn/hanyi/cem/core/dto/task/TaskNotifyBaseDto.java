package cn.hanyi.cem.core.dto.task;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
public class TaskNotifyBaseDto {
    // 用于查询通知user
    private Long orgId;
    private Long departmentId;
    private Set<Long> roleIds = new HashSet<>();
    private Set<Long> userIds = new HashSet<>();

    // 用于需要添加权限
    private Long sourceId;
    // JOURNEY, SURVEY, SURVEY_VERIFY, EVENT
    private String resourceType;

}
