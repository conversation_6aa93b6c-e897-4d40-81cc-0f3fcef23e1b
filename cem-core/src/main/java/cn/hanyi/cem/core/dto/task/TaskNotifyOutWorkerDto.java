package cn.hanyi.cem.core.dto.task;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class TaskNotifyOutWorkerDto extends TaskNotifyBaseDto {
    private String app;
    private String template;
    // WECHAT ,EMAIL, SMS, WECHAT_WORK;
    private List<String> types = new ArrayList<>();
    private Map<String, Object> params = new Hashtable<>();

}
