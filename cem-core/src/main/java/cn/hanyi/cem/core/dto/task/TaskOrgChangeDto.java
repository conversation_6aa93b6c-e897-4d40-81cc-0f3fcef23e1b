package cn.hanyi.cem.core.dto.task;

import cn.hanyi.cem.core.constant.OrgChangeType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class TaskOrgChangeDto {

    private OrgChangeType changeType;
    private OrgRegister register;
    private OrgChangeVersion changeVersion;

    public TaskOrgChangeDto(Long orgId) {
        this.changeType = OrgChangeType.REGISTER;
        this.register = new OrgRegister(orgId);
    }

    public TaskOrgChangeDto(Long orgId, String fromVersion, String toVersion) {
        this.changeType = OrgChangeType.CHANGE_VERSION;
        this.changeVersion = new OrgChangeVersion(orgId, fromVersion, toVersion);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrgRegister {
        private Long orgId;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrgChangeVersion {
        private Long orgId;
        private String fromVersion;
        private String toVersion;


    }

}
