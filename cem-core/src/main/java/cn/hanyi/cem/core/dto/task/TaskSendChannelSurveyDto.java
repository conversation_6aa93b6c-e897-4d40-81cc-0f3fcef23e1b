package cn.hanyi.cem.core.dto.task;

import cn.hanyi.survey.workertrigger.dto.SendChannelSurveyDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TaskSendChannelSurveyDto {

    private Long taskProgressId;
    private Long surveyId;
    private Long channelId;
    private String data;

    public static TaskSendChannelSurveyDto mapFromSurveyChannel(SendChannelSurveyDto from) {
        return new TaskSendChannelSurveyDto(from.getTaskProgressId(), from.getSurveyId(), from.getChannelId(), from.getData());
    }
}
