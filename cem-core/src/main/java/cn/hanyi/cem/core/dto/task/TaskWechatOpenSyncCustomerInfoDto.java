package cn.hanyi.cem.core.dto.task;

import cn.hanyi.cem.core.dto.task.progress.TaskPageableDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
public class TaskWechatOpenSyncCustomerInfoDto extends TaskPageableDto {
    private Long orgId;
    private Long thirdpartyAuthId;
    private String appId;
    private String openIds;

    public TaskWechatOpenSyncCustomerInfoDto(Long taskProgressId, Long orgId, Long thirdpartyAuthId, String appId, List<String> openIds) {
        setTaskProgressId(taskProgressId);
        this.orgId = orgId;
        this.thirdpartyAuthId = thirdpartyAuthId;
        this.appId = appId;
        if (CollectionUtils.isEmpty(openIds)) {
            this.setTotalSize(0);
            this.openIds = "";
        } else {
            this.setTotalSize(openIds.size());
            this.openIds = String.join(",", openIds);
        }
    }

    public List<String> parseOpenIds() {
        return Optional.ofNullable(openIds).map(i -> Arrays.stream(i.split(",")).collect(Collectors.toList())).orElse(new ArrayList<>());
    }
}
