package cn.hanyi.cem.core.dto.task.progress;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Getter
@Setter
public class TaskPageableDto extends TaskProgressiveDto {
    private int page;
    private int pageSize;

    public static <R> List<R> splitPageList(List<R> originList, int page, int size) {
        if (CollectionUtils.isEmpty(originList)) {
            return null;
        }
        int length = originList.size();
        int start = (page - 1) * size;
        int end = start + size;
        end = Math.min(end, length);
        if (start >= 0 && start < end) {
            return originList.subList(start, end);
        }
        return null;
    }

}
