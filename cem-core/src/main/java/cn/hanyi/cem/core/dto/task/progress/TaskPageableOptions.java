package cn.hanyi.cem.core.dto.task.progress;

import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.Assert;

import java.time.Duration;
import java.util.List;

@Getter
public class TaskPageableOptions {

    private final Long taskProgressId;
    private final int count;
    private int pageSize = 100;
    private int delaySeconds;  // 每页的延迟时间

    private int maxPage;

    private TaskPageableOptions(Long taskProgressId, int count) {
        this.taskProgressId = taskProgressId;
        this.count = count;
    }

    public static Builder create(Long taskProgressId, int count) {
        return new Builder(taskProgressId, count);
    }

    public static class Builder {
        private final TaskPageableOptions options;

        public Builder(Long taskProgressId, int count) {
            Assert.isTrue(count > 0, "count 必须大于 0");
            options = new TaskPageableOptions(taskProgressId, count);
        }

        public Builder pageSize(int pageSize) {
            Assert.isTrue(pageSize > 0, "pageSize 必须大于 0");
            options.pageSize = pageSize;
            return this;
        }

        public Builder delaySeconds(int delaySeconds) {
            Assert.isTrue(delaySeconds >= 0, "delaySeconds 必须大于等于 0");
            options.delaySeconds = delaySeconds;
            return this;
        }

        public TaskPageableOptions build() {
            options.maxPage = (options.count % options.pageSize) > 0 ? (options.count / options.pageSize + 1) : options.count / options.pageSize;
            return options;
        }

    }

    public Duration delay(int page) {
        if (delaySeconds == 0) {
            return null;
        } else {
            return Duration.ofSeconds((long) delaySeconds * (page + 1));
        }
    }

    public <T> List<T> splitPageList(List<T> originList, int page, int size) {
        if (CollectionUtils.isEmpty(originList)) {
            return null;
        }
        int length = originList.size();
        int start = page * size;
        int end = start + size;
        end = Math.min(end, length);
        if (start >= 0 && start < end) {
            return originList.subList(start, end);
        }
        return null;
    }

}
