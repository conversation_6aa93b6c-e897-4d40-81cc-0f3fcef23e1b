package cn.hanyi.cem.core.entity;

import cn.hanyi.cem.core.constant.EventType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Setter
@Getter
@Table(name = "worker_event")
@NoArgsConstructor
public class CemEvent extends CemWorkerBase {
    @Schema(description = "事件类型")
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private EventType type;

    public CemEvent(EventType type) {
        this.type = type;
    }
}
