package cn.hanyi.cem.core.entity;

import cn.hanyi.cem.core.constant.TaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Setter
@Getter
@Table(name = "worker_task")
@NoArgsConstructor
public class CemTask extends CemWorkerBase {

    @Schema(description = "任务类型")
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private TaskType type;

    public CemTask(TaskType type) {
        this.type = type;
    }
}
