package cn.hanyi.cem.core.entity;

import cn.hanyi.cem.core.constant.WorkerStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Setter
@Getter
@MappedSuperclass
public class CemWorkerBase {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT '主键'")
    private Long id;

    @Schema(description = "企业Id 可为空")
    @Column(name = "org_id")
    private Long orgId;

    @Schema(description = "创建者Id 可为空 ")
    @Column(name = "user_id")
    private Long userId;

    @Schema(description = "（事件|任务）状态")
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private WorkerStatus status = WorkerStatus.INIT;

    @Schema(description = "每个消费者的状态")
    @Column(name = "consumer_status")
    private String consumerStatus;

    @Schema(description = "（事件|任务）内容")
    @Column(name = "content")
    private String content;

    @Schema(description = "响应内容")
    @Column(name = "response")
    private String response;

    @Schema(description = "运行次数")
    @Column(name = "counts")
    private Integer counts = 0;

    @Schema(description = "（事件|任务）的来源：")
    @Column(name = "source")
    private String source;

    @Schema(description = "延迟执行时间")
    @Column(name = "delay")
    private String delay;


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_time", updatable = false, columnDefinition = "TIMESTAMP")
    @Schema(description = "新增和编辑时无需此参数")
    private Date createTime;

    @Temporal(TemporalType.TIMESTAMP)
    @UpdateTimestamp
    @Column(name = "modify_time", columnDefinition = "TIMESTAMP")
    @Schema(description = "新增和编辑时无需此参数")
    private Date modifyTime;

    @PrePersist
    public void setup() {
        if (this.createTime == null) {
            this.createTime = new Date();
        }
    }

}
