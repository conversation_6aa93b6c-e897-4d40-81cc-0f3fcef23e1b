package cn.hanyi.cem.core.producer;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.constant.QuestionItemType;
import cn.hanyi.cem.core.dto.event.*;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.repository.CemEventRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.rest.context.TenantContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static cn.hanyi.cem.core.constant.EventType.*;

@Slf4j
@Service
@Transactional
public class EventProducerHelper extends WorkerProducerHelper<EventType, CemEvent, CemEventRepository> {

    @Override
    protected CemEvent newInstance(EventType type) {
        return new CemEvent(type);
    }

    public void addEvent(boolean producerAsync, boolean consumerAsync, EventType type, Long sourceId, Object param) {
        add(producerAsync, consumerAsync, false, source(type.group, sourceId), type, param);
    }

    public void addEvent(boolean producerAsync, boolean consumerAsync, Long orgId, Long userId, EventType type, Long sourceId, Object param) {
        add(producerAsync, consumerAsync, false, orgId, userId, source(type.group, sourceId), type, param);
    }

    public void addEvent(boolean producerAsync, boolean consumerAsync, Long orgId, Long userId, EventType type, Long sourceId, Object param, Duration delay) {
        add(producerAsync, consumerAsync, false, orgId, userId, source(type.group, sourceId), type, param, delay, null);
    }

    public void addEvent(Long orgId, Long userId, EventType type, Object param) {
        add(true, true, false, orgId, userId, source(type.group, 0L), type, param);
    }

    public void addEvent(Long orgId, Long userId, EventType type, Long sourceId, Object param) {
        add(true, true, false, orgId, userId, source(type.group, sourceId), type, param);
    }

    private void addEventFromTask(boolean producerAsync, boolean consumerAsync, CemTask task, EventType type, Object param) {
        add(producerAsync, consumerAsync, false, task.getOrgId(), task.getUserId(), source("task", task.getId()), type, param);
    }

    //******************************************************************************
    //************************************ 问卷事件 **********************************
    //******************************************************************************

    public void surveyCreate(Long surveyId) {
        addEvent(true, true, SURVEY_CREATE, surveyId, new EventSurveyDto(surveyId));
    }

    public void surveyUpdate(Long surveyId) {
        addEvent(true, true, SURVEY_UPDATE, surveyId, new EventSurveyDto(surveyId));
    }

    public void surveyStop(Long surveyId) {
        addEvent(false, false, SURVEY_DISABLE, surveyId, new EventSurveyDto(surveyId));
    }

    public void surveyPublish(Long surveyId) {
        addEvent(false, false, SURVEY_ENABLE, surveyId, new EventSurveyDto(surveyId));
    }

    public void surveyDelete(Long surveyId) {
        addEvent(false, false, SURVEY_DELETE, surveyId, new EventSurveyDto(surveyId));
    }

    //******************************************************************************
    //************************************ 答卷事件 **********************************
    //******************************************************************************

    public void responseCreate(Long orgId, Long surveyId, Long responseId) {
        addEvent(true, true, orgId, null, RESPONSE_CREATE, responseId, new EventResponseDto(surveyId, responseId));
    }

    public void responseView(Long orgId, Long surveyId, Long responseId) {
        addEvent(true, true, orgId, null, RESPONSE_VIEW, responseId, new EventResponseDto(surveyId, responseId));
    }

    /**
     * 答题事件没有上下文，只能通过问卷获取后传入
     */
    public void responseSubmit(Long orgId, Long surveyId, Long responseId, boolean finalSubmit) {
        addEvent(false, true, orgId, null, RESPONSE_SUBMIT, responseId, new EventResponseDto(surveyId, responseId, finalSubmit));
    }

    public void responseSubmitFinal(Long orgId, Long surveyId, Long responseId, boolean finalSubmit) {
        addEvent(false, true, orgId, null, RESPONSE_SUBMIT_FINAL, responseId, new EventResponseDto(surveyId, responseId, finalSubmit));
    }
    public void responseImport(Long orgId, Long surveyId, Long responseId, boolean finalSubmit) {
        addEvent(false, true, orgId, null, RESPONSE_IMPORT, responseId, new EventResponseDto(surveyId, responseId, finalSubmit));
    }

    public void responseInvalid(Long surveyId, Long responseId) {
        addEvent(true, true, RESPONSE_INVALID, responseId, new EventResponseDto(surveyId, responseId));
    }

    public void responseRecover(Long surveyId, Long responseId) {
        addEvent(true, true, RESPONSE_RECOVER, responseId, new EventResponseDto(surveyId, responseId));
    }

    public void responseDelete(Long surveyId, Long responseId) {
        addEvent(true, true, RESPONSE_DELETE, responseId, new EventResponseDto(surveyId, responseId));
    }

    public void responseDeleteBySurvey(Long surveyId) {
        addEvent(true, true, RESPONSE_DELETE_BY_SURVEY, surveyId, new EventResponseDto(surveyId));
    }

    public void responseDeleteByChannel(Long surveyId, Long channelId) {
        addEvent(true, true, RESPONSE_DELETE_BY_CHANNEL, channelId, new EventResponseDto(surveyId, null, channelId));
    }

    public void channelCreate(Long surveyId, Long channelId) {
        addEvent(true, true, CHANNEL_CREATE, channelId, new EventChannelDto(surveyId, channelId));
    }

    public void channelPause(Long surveyId, Long channelId) {
        addEvent(true, true, CHANNEL_PAUSE, channelId, new EventChannelDto(surveyId, channelId));
    }

    public void channelClose(Long surveyId, Long channelId) {
        addEvent(true, true, CHANNEL_CLOSE, channelId, new EventChannelDto(surveyId, channelId));
    }

    //******************************************************************************
    //************************************ 问题事件 **********************************
    //******************************************************************************
    public void questionUpdate(Long surveyId, Long questionId) {
        addEvent(true, true, QUESTION_UPDATE, questionId, new EventQuestionDto(surveyId, questionId));
    }

    public void questionTypeUpdate(Long surveyId, Long questionId, String type) {
        addEvent(true, true, QUESTION_TYPE_UPDATE, questionId, new EventQuestionTypeDto(surveyId, questionId, type));
    }

    public void questionDelete(Long surveyId, Long questionId) {
        addEvent(true, true, QUESTION_DELETE, questionId, new EventQuestionDto(surveyId, questionId));
    }

    public void ItemUpdate(Long surveyId, Long questionId, Long itemId, QuestionItemType type) {
        addEvent(true, true, QUESTION_ITEM_UPDATE, itemId, new EventQuestionItemDto(surveyId, questionId, itemId, type));
    }

    public void ItemDelete(Long surveyId, Long questionId, Long itemId, QuestionItemType type) {
        addEvent(true, true, QUESTION_ITEM_DELETE, itemId, new EventQuestionItemDto(surveyId, questionId, itemId, type));
    }


    //******************************************************************************
    //************************************ 部门事件 **********************************
    //******************************************************************************

    public void departmentCreate(Long departmentId, String departmentCode) {
        addEvent(true, true, DEPARTMENT_CREATE, departmentId, new EventDepartmentDto(departmentId, departmentCode));
    }

    public void departmentDelete(Long departmentId, String departmentCode) {
        addEvent(true, true, DEPARTMENT_DELETE, departmentId, new EventDepartmentDto(departmentId, departmentCode));
    }

    //******************************************************************************
    //********************************** apikey事件 ********************************
    //******************************************************************************

    public void apiKeyRefresh(Long id, Long orgId, Long userId) {
        addEvent(true, true, API_KEY_REFRESH, id, new EventApiKeyDto(id, orgId, userId));
    }

    //******************************************************************************
    //************************************ 数据权限 **********************************
    //******************************************************************************

    public void resourcePermissionAddUser(Long orgId, Long userId, Long resourceId, String resourceType, List<Long> targetUserIds) {
        addEvent(true, true, orgId, userId, RESOURCE_PERMISSION_ADD_USER, resourceId, new EventResourcePermissionUserChangeDto(resourceId, resourceType, targetUserIds));
    }

    public void resourcePermissionRemoveUser(Long orgId, Long userId, Long resourceId, String resourceType, List<Long> targetUserIds) {
        addEvent(true, true, orgId, userId, RESOURCE_PERMISSION_REMOVE_USER, resourceId, new EventResourcePermissionUserChangeDto(resourceId, resourceType, targetUserIds));
    }


    //******************************************************************************
    //************************************ 事件中心事件 **********************************
    //******************************************************************************

    /**
     * 事件协作
     */
    public void warningCooperate(Long orgId, Long warningId, Long fromUserId, Set<Long> toUserIds, String mark, List<String> notifyTypes) {
        addEvent(false, false, orgId, fromUserId, WARNING_COOPERATE, warningId, new EventWarningCooperateDto(warningId, fromUserId, toUserIds, mark, notifyTypes));
    }

    /**
     * 关闭事件
     */
    public void warningClose(Long orgId, Long warningId, Long userId) {
        addEvent(false, false, orgId, userId, WARNING_CLOSE, warningId, new EventWarningCloseDto(warningId, userId));
    }

    /**
     * 规则修改事件
     */
    public void ruleChange(Long orgId, Long userId, Long warningId) {
        addEvent(false, false, orgId, userId, RULE_CHANGE, warningId, new EventRuleChangeDto(warningId, userId));
    }

    //******************************************************************************
    //************************************ 用户事件 **********************************
    //******************************************************************************

    /**
     * 用户注册
     */
    public void userRegister(Long orgId, Long userId, String password, String companyName, String customer) {
        addEvent(false, false, orgId, userId, USER_REGISTER, userId, new EventUserRegisterDto(userId, orgId, password, companyName, customer));
    }

    //******************************************************************************
    //************************************ 企业事件 **********************************
    //******************************************************************************

    /**
     * 修改企业数据
     */

    public void updateOrgName(Long orgId, Long userId, String name, String code) {
        addEvent(false, false, orgId, userId, ORG_UPDATE_NAME, orgId, new EventOrgUpdateNameDto(orgId, name, code));
    }


    //******************************************************************************
    //************************************ 客户旅程事件 **********************************
    //******************************************************************************

    /**
     * 创建体验指标
     */
    public void indicatorCreate(Long orgId, Long userId, Long journeyId, Long componentId, Long indicatorId) {
        addEvent(false, false, orgId, userId, JOURNEY_INDICATOR_CREATE, orgId, new EventIndicatorCreateDto(orgId, userId, journeyId, componentId, indicatorId));
    }

    /**
     * 红包退款，商户到余额
     */
    public void refundRedPack(Long orgid, Long surveyId, Long lotteryId, Long orderId, Long winnerId, String mchBillNo, Duration delay) {
        EventRefundRedPackDto dto = new EventRefundRedPackDto(surveyId, lotteryId, orderId, winnerId, mchBillNo);
        addEvent(true, true, orgid, TenantContext.getCurrentUserId(), REFUND_RED_PACK, winnerId, dto, delay);
    }

    /**
     * 红包延时发送
     */
    public void sendRedPackDelay(Long orgId, Long winnerId, String openid, Duration delay) {
        EventSendRedPackDelayDto dto = new EventSendRedPackDelayDto(winnerId, openid);
        addEvent(true, true, orgId, TenantContext.getCurrentUserId(), SEND_RED_PACK_DELAY, winnerId, dto, delay);
    }

}
