package cn.hanyi.cem.core.producer;

import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.*;
import cn.hanyi.cem.core.dto.task.progress.TaskPageableDto;
import cn.hanyi.cem.core.dto.task.progress.TaskPageableOptions;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.repository.CemTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@Transactional
@SuppressWarnings("SameParameterValue")
public class TaskProducerHelper extends WorkerProducerHelper<TaskType, CemTask, CemTaskRepository> {

    @Override
    protected CemTask newInstance(TaskType type) {
        return new CemTask(type);
    }

    public void addTaskFromEvent(boolean producerAsync, boolean consumerAsync, boolean withNewPool, CemEvent event, TaskType type, Object param, Duration delay) {
        add(producerAsync, consumerAsync, withNewPool, event.getOrgId(), event.getUserId(), source("event", event.getId()), type, param, delay, null);
    }

    public void addTaskFromEvent(CemEvent event, TaskType type, Object param, Duration delay) {
        add(true, true, false, event.getOrgId(), event.getUserId(), source("event", event.getId()), type, param, delay, null);
    }

    public void addTaskFromTask(boolean producerAsync, boolean consumerAsync, boolean withNewPool, CemTask task, TaskType type, Object param, Duration delay) {
        add(producerAsync, consumerAsync, withNewPool, task.getOrgId(), task.getUserId(), source("task", task.getId()), type, param, delay, null);
    }

    public void addTaskFromTask(CemTask task, TaskType type, Object param, Duration delay) {
        add(true, true, false, task.getOrgId(), task.getUserId(), source("task", task.getId()), type, param, delay, null);
    }

    public <P extends TaskPageableDto> void addPageableTaskFromTask(boolean producerAsync, boolean consumerAsync, boolean withNewPool, CemTask task, TaskType type, TaskPageableOptions options, BiFunction<Integer/*page 从0开始*/, Integer/*size*/, P/*param*/> getPageParam) {
        // 开始创建任务
        List<P> pageParams = IntStream.range(1, options.getMaxPage() + 1).mapToObj(page -> {
            // 构造每页的任务参数（可能会比较耗时）
            P pageParam = getPageParam.apply(page, options.getPageSize());
            pageParam.setTaskProgressId(options.getTaskProgressId());
            pageParam.setPage(page);
            pageParam.setPageSize(options.getPageSize());
            Assert.notNull(pageParam, "pageParam is null");
            return pageParam;
        }).collect(Collectors.toList());
        if (pageParams.isEmpty()) {
            Assert.notEmpty(pageParams, "pageParams is empty");
        }
        String source = source("task", task.getId());
        pageParams.forEach(pageParam -> add(producerAsync, consumerAsync, withNewPool, task.getOrgId(), task.getUserId(), source, type, pageParam, options.delay(pageParam.getPage()), null));
    }

    public void addTask(boolean producerAsync, boolean consumerAsync, boolean withNewPool, TaskType type, Long sourceId, Object param, Duration delay) {
        addTask(producerAsync, consumerAsync, withNewPool, type, type.group, sourceId, param, delay);
    }

    public void addTask(boolean producerAsync, boolean consumerAsync, boolean withNewPool, Long orgId, Long userId, TaskType type, String sourceKey, Long sourceId, Object param, Duration delay) {
        add(producerAsync, consumerAsync, withNewPool, orgId, userId, source(sourceKey, sourceId), type, param, delay, null);
    }

    public void addTask(boolean producerAsync, boolean consumerAsync, boolean withNewPool, Long orgId, Long userId, TaskType type, String sourceKey, Long sourceId, Object param, Duration delay, String timed) {
        add(producerAsync, consumerAsync, withNewPool, orgId, userId, source(sourceKey, sourceId), type, param, delay, timed);
    }

    public void addTask(boolean producerAsync, boolean consumerAsync, boolean withNewPool, Long orgId, Long userId, TaskType type, Long sourceId, Object param, Duration delay) {
        addTask(producerAsync, consumerAsync, withNewPool, orgId, userId, type, type.group, sourceId, param, delay);
    }

    public void addTask(boolean producerAsync, boolean consumerAsync, boolean withNewPool, TaskType type, String sourceKey, Long sourceId, Object param, Duration delay) {
        add(producerAsync, consumerAsync, withNewPool, source(sourceKey, sourceId), type, param, delay);
    }

    public void addTask(Long orgId, Long userId, TaskType type, Long sourceId, Object param, Duration delay) {
        add(false, true, false, orgId, userId, source(type.group, sourceId), type, param, delay, null);
    }

    public void addTask(TaskType type, String sourceKey, Long sourceId, Object param) {
        add(true, true, false, source(sourceKey, sourceId), type, param, null);
    }

    //******************************************************************************
    //************************************ webhook *********************************
    //******************************************************************************

    public void webhookCreateFromEvent(CemEvent event, Long pusId) {
        addTaskFromEvent(true, true, false, event, TaskType.WEBHOOK, new TaskWebHookDto(pusId), null);
    }

    //******************************************************************************
    //************************************   发送通知   *********************************
    //******************************************************************************

    public void warningNotify(TaskNotifyWarningDto dto) {
        addTask(true, true, false, TaskType.WARNING_NOTIFY, dto.getResponseId(), dto, null);
    }

    public void journeyWarningNotify(TaskNotifyWarningJourneyDto dto) {
        addTask(true, true, false, TaskType.WARNING_NOTIFY_JOURNEY, dto.getJourneyWarningId(), dto, null);
    }

    public void notifyInbox(TaskNotifyInboxDto dto, Long orgId, Long userId, Long sourceId, Duration delay) {
        addTask(true, true, false, orgId, userId, TaskType.WARNING_NOTIFY_INBOX, sourceId, dto, delay);
    }

    public void notifyOutWork(TaskNotifyOutWorkerDto dto, Long orgId, Long userId, Long sourceId, Duration delay) {
        addTask(true, true, false, orgId, userId, TaskType.WARNING_NOTIFY_OUTWORKER, sourceId, dto, delay);
    }

    public void botNotify(TaskBotNotifyDto dto, Long orgId, Long userId, Long sourceId, Duration delay) {
        addTask(true, true, false, orgId, userId, TaskType.WARNING_BOT_NOTIFY, sourceId, dto, delay);
    }


    //******************************************************************************
    //********************************** 渠道问卷发送 ********************************
    //******************************************************************************

    public void sendChannelSurvey(Long orgId, Long userId, TaskSendChannelSurveyDto dto, Duration delay) {
        addTask(false, true, false, orgId, userId, TaskType.SEND_CHANNEL_SURVEY, dto.getChannelId(), dto, delay);
    }

    public void resendChannelSurveyDelay(Long orgId, Long userId, TaskResendChannelSurveyDelayDto dto, Duration delay) {
        addTask(false, true, false, orgId, userId, TaskType.RESEND_CHANNEL_SURVEY_DELAY, dto.getChannelId(), dto, delay);
    }

    //******************************************************************************
    //************************************ 客户 **********************************
    //******************************************************************************

    public void customerSendApi(Long orgId, Long userId, TaskCustomerSendApiDto dto, Duration delay, String timed) {
        addTask(false, true, false, orgId, userId, TaskType.CUSTOMER_SEND_API, "userTask", dto.getTaskProgressId(), dto, delay, timed);
    }

    public void customerSendSms(Long orgId, Long userId, TaskCustomerSendSmsDto dto, Duration delay, String timed) {
        addTask(false, true, false, orgId, userId, TaskType.CUSTOMER_SEND_SMS, "userTask", dto.getTaskProgressId(), dto, delay, timed);
    }

    public void customerSendWechat(Long orgId, Long userId, TaskCustomerSendWechatDto dto, Duration delay, String timed) {
        addTask(false, true, false, orgId, userId, TaskType.CUSTOMER_SEND_WECHAT, "userTask", dto.getTaskProgressId(), dto, delay, timed);
    }

    //******************************************************************************
    //********************************* 微信开放平台 *********************************
    //******************************************************************************

    public void wechatOpenSyncCustomerList(Long orgId, Long userId, Long taskProgressId, Long thirdpartyAuthId, String appId) {
        addTask(false, true, false, orgId, userId, TaskType.WECHAT_OPEN_SYNC_CUSTOMER_LIST, "userTask", taskProgressId, new TaskWechatOpenSyncCustomerListDto(taskProgressId, orgId, thirdpartyAuthId, appId, null), null);
    }

    public void wechatOpenSyncCustomerList(CemTask task, Long taskProgressId, Long thirdpartyAuthId, String appId, String nextId) {
        addTaskFromTask(false, true, false, task, TaskType.WECHAT_OPEN_SYNC_CUSTOMER_LIST, new TaskWechatOpenSyncCustomerListDto(taskProgressId, task.getOrgId(), thirdpartyAuthId, appId, nextId), null);
    }

    public void wechatOpenSyncCustomerInfo(CemTask task, Long taskProgressId, Long thirdpartyAuthId, String appId, int count, BiFunction<Integer, Integer, List<String>> getPageOpenIds) {
        addPageableTaskFromTask(false, true, false, task, TaskType.WECHAT_OPEN_SYNC_CUSTOMER_INFO, TaskPageableOptions.create(taskProgressId, count).build(), (p, s) -> {
            List<String> openIds = Optional.ofNullable(getPageOpenIds.apply(p, s)).orElse(List.of());
            return new TaskWechatOpenSyncCustomerInfoDto(taskProgressId, task.getOrgId(), thirdpartyAuthId, appId, openIds);
        });
    }

    public void wechatOpenSyncCustomerInfoSingle(Long orgId, Long userId, Long thirdpartyAuthId, String appId, String openId) {
        addTask(false, true, false, orgId, userId, TaskType.WECHAT_OPEN_SYNC_CUSTOMER_INFO, "wechatOpen", thirdpartyAuthId, new TaskWechatOpenSyncCustomerInfoDto(null, orgId, thirdpartyAuthId, appId, List.of(openId)), null);
    }

    public void wechatOpenSyncTemplate(Long orgId, Long userId, Long taskProgressId, Long thirdpartyAuthId, boolean all) {
        addTask(false, true, false, orgId, userId, TaskType.WECHAT_OPEN_SYNC_TEMPLATE, "wechatOpen", thirdpartyAuthId, new TaskWechatOpenSyncTemplateDto(taskProgressId, orgId, thirdpartyAuthId, all), null);
    }

    //******************************************************************************
    //*********************************** 答卷  *************************************
    //******************************************************************************

    public void responseDownload(Long orgId, Long userId, Long taskProgressId, TaskResponseDownloadDto dto) {
        addTask(false, true, true, orgId, userId, TaskType.RESPONSE_DOWNLOAD, "userTask", taskProgressId, dto, null);
    }

    public void responseDownloadAttachment(Long orgId, Long userId, Long taskProgressId, TaskResponseDownloadDto dto) {
        addTask(false, true, false, orgId, userId, TaskType.RESPONSE_DOWNLOAD_ATTACHMENT, "userTask", taskProgressId, dto, null);
    }

    public void responseImport(Long orgId, Long userId, Long taskProgressId, TaskResponseImportDto dto) {
        addTask(false, true, true, orgId, userId, TaskType.RESPONSE_IMPORT, "userTask", taskProgressId, dto, null);
    }

    //******************************************************************************
    //********************************* 数据看板 *************************************
    //******************************************************************************

    public void pushDashboard(TaskDashboardPushDto dto, Long orgId, Long userId, Long dashboardId, Duration delay) {
        addTask(true, true, false, orgId, userId, TaskType.BI_DASHBOARD_PUSH, "dashboard", dashboardId, dto, delay, null);
    }

    public void pushDashboardToBot(TaskDashboardPushDto dto, Long orgId, Long userId, Long dashboardId, Duration delay) {
        addTask(true, true, false, orgId, userId, TaskType.BI_DASHBOARD_PUSH_BOT, "dashboard", dashboardId, dto, delay);
    }

    public void analyseTextProject(Long orgId, Long userId, Long textProjectId, Long taskProgressId, Boolean isRestart) {
        addTask(true, true, false, TaskType.BI_ANALYSE_TEXT_PROJECT, "textProject", textProjectId, new TaskProcessTextProjectDto(orgId, userId, textProjectId, taskProgressId, isRestart), null);
    }

    //******************************************************************************
    //************************************   事件中心   *********************************
    //******************************************************************************

    public void warningRerun(Long orgId, Long userId, Long warningId, Long taskProgressId) {
        addTask(true, true, false, TaskType.WARNING_RULE_RERUN, warningId, new TaskProcessWarningDto(orgId, userId, warningId, taskProgressId), null);
    }
}