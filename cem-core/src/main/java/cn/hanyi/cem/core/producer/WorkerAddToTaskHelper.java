package cn.hanyi.cem.core.producer;

import cn.hanyi.cem.core.constant.IWorkerMsgType;
import cn.hanyi.cem.core.dto.WorkerDto;
import lombok.extern.slf4j.Slf4j;
import org.befun.task.mq.ITaskService;
import org.befun.task.service.TaskDelayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class WorkerAddToTaskHelper implements TransactionSynchronization {

    @Autowired
    private ITaskService taskService;
    @Autowired
    private TaskDelayService taskDelayService;

    private final ThreadLocal<List<SimpleWorker>> localTask = ThreadLocal.withInitial(ArrayList::new);
    private final ThreadLocal<List<SimpleWorker>> localEvent = ThreadLocal.withInitial(ArrayList::new);

    public void addTask(Long taskId, boolean consumerAsync, boolean withNewPool, Duration delay, IWorkerMsgType workerType) {
        List<SimpleWorker> events = localTask.get();
        if (events != null) { // 这里永远都不会是null
            events.add(new SimpleWorker(taskId, consumerAsync, withNewPool, delay, workerType));
        }
    }

    public void addEvent(Long eventId, boolean consumerAsync, boolean withNewPool, Duration delay, IWorkerMsgType workerType) {
        List<SimpleWorker> events = localEvent.get();
        if (events != null) { // 这里永远都不会是null
            events.add(new SimpleWorker(eventId, consumerAsync, withNewPool, delay, workerType));
        }
    }

    @Override
    public void afterCompletion(int status) {
        if (status == STATUS_COMMITTED) {
            Optional.ofNullable(localTask.get()).ifPresent(list -> list.forEach(this::addToTask));
            Optional.ofNullable(localEvent.get()).ifPresent(list -> list.forEach(this::addToTask));
        }
        localTask.remove();
        localEvent.remove();
    }

    public void addToTask(SimpleWorker worker) {
        WorkerDto taskDto = new WorkerDto();
        taskDto.setType(worker.workerType.getWorkerType());
        taskDto.setWorkerId(worker.id);
        taskDto.setConsumerAsync(worker.consumerAsync);
        taskDto.setWithNewPool(worker.withNewPool);

        String queue = worker.workerType.getWorkerType().getQueue();
        if (worker.delay != null) {
            taskDelayService.addTaskDelay(queue, taskDto, worker.delay);
        } else {
            taskService.addTask(queue, taskDto);
        }
        log.info("{}({}) 已发送到队列", queue, worker.id);
    }

    public static class SimpleWorker {
        private final Long id;
        private final boolean consumerAsync;
        private final boolean withNewPool;
        private final Duration delay;

        private final IWorkerMsgType workerType;

        public SimpleWorker(Long id, boolean consumerAsync, boolean withNewPool, Duration delay, IWorkerMsgType workerType) {
            this.id = id;
            this.consumerAsync = consumerAsync;
            this.withNewPool = withNewPool;
            this.delay = delay;
            this.workerType = workerType;
        }
    }
}
