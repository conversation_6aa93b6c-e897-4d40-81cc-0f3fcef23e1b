package cn.hanyi.cem.core.producer;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.constant.IWorkerMsgType;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.constant.WorkerStatus;
import cn.hanyi.cem.core.entity.CemWorkerBase;
import cn.hanyi.cem.core.repository.CemWorkerRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.befun.task.constant.TaskExecutionTimedType;
import org.befun.task.dto.TimedTaskDto;
import org.befun.task.utils.TimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.Duration;
import java.util.concurrent.ForkJoinPool;

@Slf4j
abstract class WorkerProducerHelper<T extends IWorkerMsgType, E extends CemWorkerBase, R extends CemWorkerRepository<E, Long>> {

    @Autowired
    private R repository;
    @Autowired
    private WorkerAddToTaskHelper workerAddToTaskHelper;
    @Autowired
    protected StringRedisTemplate stringRedisTemplate;
    private final ForkJoinPool executePool = ForkJoinPool.commonPool();

    protected abstract E newInstance(T type);

    protected String source(String key, Long id) {
        return String.format("%s:%d", key, id);
    }

    protected void add(boolean producerAsync, boolean consumerAsync, boolean withNewPool, String source, T type, Object param) {
        add(producerAsync, consumerAsync, withNewPool, TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), source, type, param, null, null);
    }

    protected void add(boolean producerAsync, boolean consumerAsync, boolean withNewPool, String source, T type, Object param, Duration delay) {
        add(producerAsync, consumerAsync, withNewPool, TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), source, type, param, delay, null);
    }

    protected void add(boolean producerAsync, boolean consumerAsync, boolean withNewPool, Long orgId, Long userId, String source, T type, Object param) {
        add(producerAsync, consumerAsync, withNewPool, orgId, userId, source, type, param, null, null);
    }

    protected void add(boolean producerAsync, boolean consumerAsync, boolean withNewPool, Long orgId, Long userId, String source, T type, Object param, Duration delay, String timed) {
        if (param == null) {
            log.error("添加{}失败, 参数不能为null", type.getWorkerType().getText());
            throw new RuntimeException();
        }
        baseAdd(consumerAsync, withNewPool, orgId, userId, source, type, param, delay, timed);
//        if (producerAsync) {
//            executePool.execute(() -> baseAdd(consumerAsync, orgId, userId, source, type, param, delay));
//        } else {
//            baseAdd(consumerAsync, orgId, userId, source, type, param, delay);
//        }
    }

    private void baseAdd(boolean consumerAsync, boolean withNewPool, Long orgId, Long userId, String source, T type, Object param, Duration delay, String timed) {
        E entity = newInstance(type);
        entity.setOrgId(orgId);
        entity.setUserId(userId);
        entity.setSource(source);
        if (param instanceof String) {
            entity.setContent((String) param);
        } else {
            entity.setContent(JsonHelper.toJson(param));
        }
        if (delay != null) {
            entity.setStatus(WorkerStatus.DELAY);
            entity.setDelay(String.format("delay:%s", delay));
        } else if (StringUtils.isNotEmpty(timed)) {
            entity.setStatus(WorkerStatus.DELAY);
            entity.setDelay(String.format("timed:%s", timed));
            delay = parseTimed(timed);
        }
        repository.save(entity);
        log.info("{}({},{},{}) 保存成功, param={}", type.getWorkerType().name(), entity.getId(), type.getWorkerType().getQueue(), type.getEumName(), entity.getContent());
        TransactionSynchronizationManager.registerSynchronization(workerAddToTaskHelper);

        if (type instanceof EventType) {
            workerAddToTaskHelper.addEvent(entity.getId(), consumerAsync, withNewPool, delay, type);
        } else if (type instanceof TaskType) {
            workerAddToTaskHelper.addTask(entity.getId(), consumerAsync, withNewPool, delay, type);
        }
    }

    private Duration parseTimed(String timedInfo) {
        String[] items = timedInfo.split("/");
        if (items.length == 5) {
            TimedTaskDto timed = TimedTaskDto.builder()
                    .timedType(TaskExecutionTimedType.valueOf(items[0]))
                    .dayOfWeeks(items[1].split(","))
                    .hour(Integer.parseInt(items[2]))
                    .minute(Integer.parseInt(items[3]))
                    .skipHoliday(Integer.parseInt(items[4]) == 1)
                    .build();
            return TimeUtils.parseAtDuration(timed);
        }
        return null;
    }
}
