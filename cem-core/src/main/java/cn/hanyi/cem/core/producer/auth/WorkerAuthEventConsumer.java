package cn.hanyi.cem.core.producer.auth;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.*;
import cn.hanyi.cem.core.producer.EventProducerHelper;
import org.befun.auth.workertrigger.IAuthEventConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

@Component
@ConditionalOnClass(IAuthEventConsumer.class)
@ConditionalOnProperty(prefix = "worker.producer.event.enabled", name = "auth", havingValue = "true", matchIfMissing = true)
public class WorkerAuthEventConsumer implements IAuthEventConsumer {

    @Autowired
    private EventProducerHelper eventProducerHelper;

    @Override
    public void resourcePermissionAddUser(Long orgId, Long userId, Long resourceId, String resourceType, List<Long> targetUserId) {
        eventProducerHelper.resourcePermissionAddUser(orgId, userId, resourceId, resourceType, targetUserId);
    }

    @Override
    public void resourcePermissionRemoveUser(Long orgId, Long userId, Long resourceId, String resourceType, List<Long> targetUserId) {
        eventProducerHelper.resourcePermissionRemoveUser(orgId, userId, resourceId, resourceType, targetUserId);
    }

    @Override
    public void userRegister(Long orgId, Long userId, String password, String companyName, String customer) {
        eventProducerHelper.userRegister(orgId, userId, password, companyName, customer);
    }

    @Override
    public void userLogin(Long orgId, Long userId) {
        eventProducerHelper.addEvent(true, true, orgId, userId, EventType.USER_LOGIN, userId, new EventUserLoginDto(orgId, userId));
    }

    @Override
    public void userDelete(Long orgId, Long userId) {
        eventProducerHelper.addEvent(true, true, orgId, userId, EventType.USER_DELETE, userId, new EventUserDto(orgId, userId));
    }

    @Override
    public void updateOrgName(Long orgId, Long userId, String name, String code) {
        eventProducerHelper.updateOrgName(orgId, userId, name, code);
    }

    @Override
    public void rechargeCompleted(Long orgId, Long userId, Long rechargeId, String status) {
        eventProducerHelper.addEvent(true, true, orgId, userId, EventType.RECHARGE_COMPLETED, rechargeId, new EventRechargeDto(rechargeId, status));
    }

    @Override
    public void rechargeExpired(Long orgId, Long userId, Long rechargeId, Duration delay) {
        eventProducerHelper.addEvent(true, true, orgId, userId, EventType.RECHARGE_EXPIRED, rechargeId, new EventRechargeDto(rechargeId, null), delay);
    }

    @Override
    public void rechargeRefundCompleted(Long orgId, Long userId, Long rechargeId, Long rechargeRefundId, String status) {
        eventProducerHelper.addEvent(true, true, orgId, userId, EventType.RECHARGE_REFUND_COMPLETED, rechargeRefundId, new EventRechargeRefundDto(rechargeId, rechargeRefundId, status));
    }

    @Override
    public void rechargeRefundQuery(Long orgId, Long userId, Long rechargeId, Long rechargeRefundId, Duration delay) {
        eventProducerHelper.addEvent(true, true, orgId, userId, EventType.RECHARGE_REFUND_QUERY, rechargeRefundId, new EventRechargeRefundDto(rechargeId, rechargeRefundId, null), delay);
    }

    @Override
    public void orderCompleted(Long orgId, Long userId, Long orderId, String status) {
        eventProducerHelper.addEvent(true, true, orgId, userId, EventType.ORDER_COMPLETED, orderId, new EventOrderDto(orderId, status));
    }

    @Override
    public void wechatOpenAuthorize(Long orgId, Long userId, Long thirdpartyAuthId, String appId) {
        eventProducerHelper.addEvent(orgId, userId, EventType.WECHAT_OPEN_AUTHORIZE, new EventWechatOpenAuthorizeDto(orgId, thirdpartyAuthId, appId));
    }

    @Override
    public void wechatOpenDelete(Long orgId, Long userId, Long thirdpartyAuthId, boolean deleteUser) {
        eventProducerHelper.addEvent(orgId, userId, EventType.WECHAT_OPEN_DELETE, new EventWechatOpenDeleteDto(orgId, thirdpartyAuthId, deleteUser));
    }

    @Override
    public void wechatOpenSubscribe(String appId, String openId) {
        eventProducerHelper.addEvent(null, null, EventType.WECHAT_OPEN_SUBSCRIBE, new EventWechatOpenSubscribeDto(appId, openId));
    }

    @Override
    public void wechatOpenUnsubscribe(String appId, String openId) {
        eventProducerHelper.addEvent(null, null, EventType.WECHAT_OPEN_UNSUBSCRIBE, new EventWechatOpenSubscribeDto(appId, openId));
    }
}
