package cn.hanyi.cem.core.producer.auth;

import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerThirdpartyTriggerSendManageDto;
import cn.hanyi.cem.core.dto.task.TaskSmsNotificationDelayDto;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import org.befun.auth.workertrigger.IAuthTaskConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;

@Component
@ConditionalOnClass(IAuthTaskConsumer.class)
@ConditionalOnProperty(prefix = "worker.producer.task.enabled", name = "auth", havingValue = "true", matchIfMissing = true)
public class WorkerAuthTaskConsumer implements IAuthTaskConsumer {

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Override
    public void smsNotificationDelay(Long orgId, Long userId, String templateName, String mobile, Map<String, Object> parameters, Duration delay) {
        taskProducerHelper.addTask(true, true, false, orgId, userId,
                TaskType.SMS_NOTIFICATION_DELAY, 0L, new TaskSmsNotificationDelayDto(templateName, mobile, parameters), delay);
    }

    @Override
    public void youzanMessageTriggerSendManage(Long orgId, Long userId, Long sendManageId, String thirdpartyMessageType, Long thirdpartyMessageId) {
        taskProducerHelper.addTask(true, true, false, orgId, userId,
                TaskType.CUSTOMER_THIRDPARTY_MESSAGE_TRIGGER_SEND_MANAGE, thirdpartyMessageId,
                new TaskCustomerThirdpartyTriggerSendManageDto(orgId, userId, sendManageId, thirdpartyMessageType, thirdpartyMessageId), null);
    }
}
