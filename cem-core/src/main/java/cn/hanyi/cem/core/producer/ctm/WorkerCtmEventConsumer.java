package cn.hanyi.cem.core.producer.ctm;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventDataAccessHandleMessageDto;
import cn.hanyi.cem.core.dto.event.EventWarningChangeStatusDto;
import cn.hanyi.cem.core.producer.EventProducerHelper;
import cn.hanyi.ctm.workertrigger.ICtmEventConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Slf4j
@Component
@ConditionalOnClass(ICtmEventConsumer.class)
@ConditionalOnProperty(prefix = "worker.producer.event.enabled", name = "ctm", havingValue = "true", matchIfMissing = true)
public class WorkerCtmEventConsumer implements ICtmEventConsumer {

    @Autowired
    private EventProducerHelper eventProducerHelper;

    @Override
    public void eventCooperate(Long orgId, Long warningId, Long fromUserId, Set<Long> toUserIds, String mark, List<String> notifyTypes) {
        log.info("用户:{} 协作事件:{}", fromUserId, warningId);
        eventProducerHelper.warningCooperate(orgId, warningId, fromUserId, toUserIds, mark, notifyTypes);
    }

    @Override
    public void eventClose(Long orgId, Long warningId, Long userId) {
        log.info("用户:{} 关闭事件:{}", userId, warningId);
        eventProducerHelper.warningClose(orgId, warningId, userId);
    }

    @Override
    public void eventRuleChange(Long orgId, Long userId, Long warningId) {
        log.info("用户:{} 修改事件规则:{}", userId, warningId);
        eventProducerHelper.ruleChange(orgId, userId, warningId);
    }

    @Override
    public void eventChangeStatus(Long orgId, Long userId, Long eventId, Long actionId, String status) {
        eventProducerHelper.addEvent(orgId, userId, EventType.WARNING_CHANGE_STATUS, new EventWarningChangeStatusDto(eventId, actionId, userId, status));
    }

    @Override
    public void indicatorCreate(Long orgId, Long userId, Long journeyMapId, Long componentId, Long indicatorId) {
        log.info("用户:{} 创建体验指标:{}", userId, indicatorId);
        eventProducerHelper.indicatorCreate(orgId, userId, journeyMapId, componentId, indicatorId);
    }

    @Override
    public void dataAccess(Long orgId, Long userId, Long accessId, Long cellId) {
        eventProducerHelper.addEvent(orgId, userId, EventType.DATA_ACCESS_HANDLE_MESSAGE, new EventDataAccessHandleMessageDto(accessId, cellId));
    }
}
