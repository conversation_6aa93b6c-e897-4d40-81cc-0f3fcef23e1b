package cn.hanyi.cem.core.producer.ctm;

import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerBatchAddJourneyDto;
import cn.hanyi.cem.core.dto.task.TaskCustomerResetDepartmentDto;
import cn.hanyi.cem.core.dto.task.TaskCustomerSendSmsDto;
import cn.hanyi.cem.core.dto.task.TaskCustomerSendWechatDto;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.ctm.workertrigger.ICtmTaskConsumer;
import cn.hanyi.ctm.workertrigger.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Component
@ConditionalOnClass(ICtmTaskConsumer.class)
@ConditionalOnProperty(prefix = "worker.producer.task.enabled", name = "ctm", havingValue = "true", matchIfMissing = true)
public class WorkerCtmTaskConsumer implements ICtmTaskConsumer {

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Override
    public void customerBatchAddJourney(CustomerBatchAddJourneyDto dto, Duration delay) {
        TaskCustomerBatchAddJourneyDto tasKDto = new TaskCustomerBatchAddJourneyDto(dto.getTaskProgressId(), dto.getData());
        taskProducerHelper.addTask(false, true, false, dto.getOrgId(), dto.getUserId(), TaskType.CUSTOMER_BATCH_ADD_JOURNEY, "userTask", dto.getTaskProgressId(), tasKDto, delay);
    }

    @Override
    public void customerSendSms(CustomerSendSmsDto dto) {
        taskProducerHelper.customerSendSms(dto.getOrgId(), dto.getUserId(), TaskCustomerSendSmsDto.mapFromCtm(dto), dto.getDelay(), dto.getTimed());
    }

    @Override
    public void customerSendWechat(CustomerSendWechatDto dto) {
        taskProducerHelper.customerSendWechat(dto.getOrgId(), dto.getUserId(), TaskCustomerSendWechatDto.mapFromCtm(dto), dto.getDelay(), dto.getTimed());
    }

    @Override
    public void customerSendComposited(Long orgId, Long userId, String sourceKey, Long sourceId, String composited, Duration delay) {
        taskProducerHelper.addTask(false, true, false, orgId, userId, TaskType.CUSTOMER_SEND_COMPOSITED, sourceKey, sourceId, composited, delay, null);
    }

    @Override
    public void wechatOpenSyncCustomerList(WechatOpenSyncCustomerListDto dto) {
        taskProducerHelper.wechatOpenSyncCustomerList(dto.getOrgId(), dto.getUserId(), dto.getTaskProgressId(), dto.getThirdpartyAuthId(), dto.getAppId());
    }

    @Override
    public void wechatOpenSyncCustomerInfoSingle(WechatOpenSyncCustomerInfoSingleDto dto) {
        taskProducerHelper.wechatOpenSyncCustomerInfoSingle(dto.getOrgId(), dto.getUserId(), dto.getThirdpartyAuthId(), dto.getAppId(), dto.getOpenId());
    }

    @Override
    public void wechatOpenSyncTemplate(WechatOpenSyncTemplateDto dto) {
        taskProducerHelper.wechatOpenSyncTemplate(dto.getOrgId(), dto.getUserId(), dto.getTaskProgressId(), dto.getThirdpartyAuthId(), dto.isAll());
    }

    @Override
    public void customerResetDepartment(Long orgId, Long userId) {
        taskProducerHelper.addTask(false, false, true, TaskType.CUSTOMER_RESET_DEPARTMENT, "org", orgId, new TaskCustomerResetDepartmentDto(orgId), null);
    }

    @Override
    public void eventRerun(Long orgId, Long userId, Long warningId, Long taskProgressId) {
        taskProducerHelper.warningRerun(orgId, userId, warningId, taskProgressId);
    }
}