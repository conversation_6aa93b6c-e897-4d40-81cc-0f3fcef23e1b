package cn.hanyi.cem.core.producer.survey;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.constant.QuestionItemType;
import cn.hanyi.cem.core.dto.event.EventAdminxChannelOrderRequest;
import cn.hanyi.cem.core.dto.event.EventResponseSyncDynamicItemDto;
import cn.hanyi.cem.core.dto.event.EventSurveyContentAuditDto;
import cn.hanyi.cem.core.dto.event.EventSurveyLotteryDto;
import cn.hanyi.cem.core.producer.EventProducerHelper;
import cn.hanyi.survey.workertrigger.ISurveyEventConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Slf4j
@Component
@ConditionalOnClass(ISurveyEventConsumer.class)
@ConditionalOnProperty(prefix = "worker.producer.event.enabled", name = "survey", havingValue = "true", matchIfMissing = true)
public class WorkerSurveyEventConsumer implements ISurveyEventConsumer {

    @Autowired
    private EventProducerHelper eventProducerHelper;

    @Override
    public void surveyUpdate(Long surveyId) {
        eventProducerHelper.surveyUpdate(surveyId);
    }

    @Override
    public void surveyStop(Long surveyId) {
        eventProducerHelper.surveyStop(surveyId);
    }

    @Override
    public void surveyPublish(Long surveyId) {
        eventProducerHelper.surveyPublish(surveyId);
    }

    @Override
    public void surveyDelete(Long surveyId) {
        eventProducerHelper.surveyDelete(surveyId);
    }

    @Override
    public void surveyContentAudit(Long orgId, Long userId, Long surveyId, Long auditId) {
        eventProducerHelper.addEvent(orgId, userId, EventType.SURVEY_CONTENT_AUDIT, surveyId, new EventSurveyContentAuditDto(surveyId, auditId));
    }

    @Override
    public void questionDelete(Long surveyId, Long questionId) {
        eventProducerHelper.questionDelete(surveyId, questionId);
    }

    @Override
    public void questionUpdate(Long surveyId, Long questionId) {
        eventProducerHelper.questionUpdate(surveyId, questionId);
    }

    @Override
    public void questionTypeUpdate(Long surveyId, Long questionId, String type) {
        eventProducerHelper.questionTypeUpdate(surveyId, questionId, type);
    }

    @Override
    public void questionItemDelete(Long surveyId, Long questionId, Long itemId, String type) {
        eventProducerHelper.ItemDelete(surveyId, questionId, itemId, QuestionItemType.valueOf(type));
    }

    @Override
    public void responseCreate(Long orgId, Long surveyId, Long responseId) {
        eventProducerHelper.responseCreate(orgId, surveyId, responseId);
    }

    @Override
    public void responseView(Long orgId, Long surveyId, Long responseId) {
        eventProducerHelper.responseView(orgId, surveyId, responseId);
    }

    @Override
    public void responseSubmit(Long orgId, Long surveyId, Long responseId, boolean finalSubmit) {
        eventProducerHelper.responseSubmit(orgId, surveyId, responseId, finalSubmit);
    }
    @Override
    public void responseImport(Long orgId, Long surveyId, Long responseId, boolean finalSubmit) {
        eventProducerHelper.responseImport(orgId, surveyId, responseId, finalSubmit);
    }

    @Override
    public void responseSubmitFinal(Long orgId, Long surveyId, Long responseId, boolean finalSubmit) {
        eventProducerHelper.responseSubmitFinal(orgId, surveyId, responseId, finalSubmit);
    }

    @Override
    public void responseSyncDynamicItem(Long orgId, Long surveyId, Long responseId, String dynamicQuestions) {
        eventProducerHelper.addEvent(orgId, 0L, EventType.RESPONSE_SYNC_DYNAMIC_ITEM,
                new EventResponseSyncDynamicItemDto(surveyId, responseId, dynamicQuestions));
    }

    @Override
    public void responseDelete(Long surveyId, Long responseId) {
        eventProducerHelper.responseDelete(surveyId, responseId);
    }

    @Override
    public void responseDeleteBySurvey(Long surveyId) {
        eventProducerHelper.responseDeleteBySurvey(surveyId);
    }

    @Override
    public void responseDeleteByChannel(Long surveyId, Long channelId) {
        eventProducerHelper.responseDeleteByChannel(surveyId, channelId);
    }

    @Override
    public void channelCreate(Long surveyId, Long channelId) {
        eventProducerHelper.channelCreate(surveyId, channelId);
    }

    @Override
    public void channelClose(Long surveyId, Long channelId) {
        eventProducerHelper.channelClose(surveyId, channelId);
    }

    @Override
    public void channelPause(Long surveyId, Long channelId) {
        eventProducerHelper.channelPause(surveyId, channelId);
    }

    @Override
    public void responseInvalid(Long surveyId, Long responseId) {
        eventProducerHelper.responseInvalid(surveyId, responseId);
    }

    @Override
    public void responseRecover(Long surveyId, Long responseId) {
        eventProducerHelper.responseRecover(surveyId, responseId);
    }

    @Override
    public void refundRedPack(Long orgId, Long surveyId, Long lotteryId, Long orderId, Long winnerId, String mchBillno, Duration delay) {
        eventProducerHelper.refundRedPack(orgId, surveyId, lotteryId, orderId, winnerId, mchBillno, delay);
    }

    @Override
    public void sendRedPackDelay(Long orgId, Long winnerId, String openid, Duration delay) {
        eventProducerHelper.sendRedPackDelay(orgId, winnerId, openid, delay);
    }

    @Override
    public void lotteryClose(Long orgId, Long userId, Long surveyId, Long lotteryId, String lotteryType) {
        eventProducerHelper.addEvent(true, true, orgId, userId, EventType.SURVEY_LOTTERY_CLOSE, lotteryId, new EventSurveyLotteryDto(surveyId, lotteryId, lotteryType));
    }

    @Override
    public void lotteryDelete(Long orgId, Long userId, Long surveyId, Long lotteryId, String lotteryType) {
        eventProducerHelper.addEvent(true, true, orgId, userId, EventType.SURVEY_LOTTERY_DELETE, lotteryId, new EventSurveyLotteryDto(surveyId, lotteryId, lotteryType));
    }

    @Override
    public void adminxChannelOrderRequest(Long orgId, Long userId, Long surveyId, Long channelId, String contacts, String requestTime) {
        eventProducerHelper.addEvent(orgId, userId, EventType.ADMINX_CHANNEL_ORDER_REQUEST, new EventAdminxChannelOrderRequest(surveyId, channelId, contacts, requestTime));
    }
}
