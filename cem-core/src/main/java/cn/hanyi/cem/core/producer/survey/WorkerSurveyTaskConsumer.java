package cn.hanyi.cem.core.producer.survey;

import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.*;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.survey.workertrigger.ISurveyTaskConsumer;
import cn.hanyi.survey.workertrigger.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Component
@ConditionalOnClass(ISurveyTaskConsumer.class)
@ConditionalOnProperty(prefix = "worker.producer.task.enabled", name = "survey", havingValue = "true", matchIfMissing = true)
public class WorkerSurveyTaskConsumer implements ISurveyTaskConsumer {

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Override
    public void sendChannelSurvey(SendChannelSurveyDto dto) {
        taskProducerHelper.sendChannelSurvey(dto.getOrgId(), dto.getUserId(), TaskSendChannelSurveyDto.mapFromSurveyChannel(dto), null);
    }

    @Override
    public void resendChannelSurveyDelay(Long orgId, Long userId, Long surveyId, Long channelId, String jobId, long delaySeconds) {
        taskProducerHelper.resendChannelSurveyDelay(orgId, userId, new TaskResendChannelSurveyDelayDto(jobId, surveyId, channelId), Duration.ofSeconds(delaySeconds));
    }

    @Override
    public void customerSendSms(CustomerSendSmsDto dto) {
        taskProducerHelper.customerSendSms(dto.getOrgId(), dto.getUserId(), TaskCustomerSendSmsDto.mapFromSurveyChannel(dto), dto.getDelay(), dto.getTimed());
    }

    @Override
    public void customerSendWechat(CustomerSendWechatDto dto) {
        taskProducerHelper.customerSendWechat(dto.getOrgId(), dto.getUserId(), TaskCustomerSendWechatDto.mapFromSurveyChannel(dto), dto.getDelay(), dto.getTimed());
    }

    @Override
    public void customerSendEmail(CustomerSendEmailDto dto) {
        taskProducerHelper.addTask(true, true, false,
                dto.getOrgId(), dto.getUserId(), TaskType.CUSTOMER_SEND_EMAIL,
                "userTask", dto.getTaskProgressId(),
                TaskCustomerSendEmailDto.mapFromSurveyChannel(dto),
                dto.getDelay(), dto.getTimed());
    }

    @Override
    public void responseDownload(Long orgId, Long userId, Long taskProgressId, Long surveyId, String data) {
        taskProducerHelper.responseDownload(orgId, userId, taskProgressId, new TaskResponseDownloadDto(taskProgressId, surveyId, data));
    }

    @Override
    public void responseDownloadAttachment(Long orgId, Long userId, Long taskProgressId, Long surveyId, String data) {
        taskProducerHelper.responseDownloadAttachment(orgId, userId, taskProgressId, new TaskResponseDownloadDto(taskProgressId, surveyId, data));
    }

    @Override
    public void responseImport(Long orgId, Long userId, Long taskProgressId, Long surveyId, String data) {
        taskProducerHelper.responseImport(orgId, userId, taskProgressId, new TaskResponseImportDto(taskProgressId, surveyId, data));
    }

    @Override
    public void quotaSync(Long orgId, Long userId, TaskQuotaSyncDto data) {
        taskProducerHelper.addTask(false, true, false, orgId, userId, TaskType.QUOTA_SYNC, "surveyQuota", data.getSurveyId(), data, null);
    }

    @Override
    public void translateSurvey(Long orgId, Long userId, Long surveyId, String data) {
        taskProducerHelper.addTask(true, true, true, orgId, userId, TaskType.SURVEY_TRANSLATE, "surveyTranslate", surveyId, data, null);
    }
}
