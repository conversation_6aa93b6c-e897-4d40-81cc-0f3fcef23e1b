package cn.hanyi.cem.core.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "worker")
public class WorkerProperties {

    private EventConfig event = new EventConfig();
    private TaskConfig task = new TaskConfig();
    private NotifyConfig notify = new NotifyConfig();

    @Setter
    @Getter
    public static class EventConfig {
        private Boolean enabled = true;
        private WarningConfig warning = new WarningConfig();
    }

    @Setter
    @Getter
    public static class WarningConfig {
        private Boolean enableNotify = true;
        private String eventUrl = "";
        private String journeyUrl = "";
    }

    @Setter
    @Getter
    public static class TaskConfig {
        private Boolean enabled = true;
    }

    @Setter
    @Getter
    public static class NotifyConfig {

        private Boolean enabled = true;
        private String app;
        private String warning = "warning";
        private String cooperation = "cooperation";
        private String close = "close";
        private String journey = "journey";
        private String orgChangeRegister = "org-change-notify-register";
        private String orgChangeVersion = "org-change-notify-change-version";

    }
}