package cn.hanyi.cem.core.repository;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.constant.WorkerStatus;
import cn.hanyi.cem.core.entity.CemEvent;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CemEventRepository extends CemWorkerRepository<CemEvent, Long> {

    Optional<CemEvent> findTopByTypeAndSource(EventType type, String source);
    List<CemEvent> findByTypeAndStatus(EventType type, WorkerStatus status);

}
