package cn.hanyi.cem.core.utils;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class SignGenerator {

    private static final String ALGORITHM = "MD5";
    private static final Logger logger = Logger.getLogger(SignGenerator.class.getName());

    /**
     * 生成签名
     * @param url
     * @param secretKey
     * @return
     * @throws UnsupportedEncodingException
     * @throws NoSuchAlgorithmException
     * @throws MalformedURLException
     */
    public static String generate(String url, String secretKey) throws UnsupportedEncodingException, NoSuchAlgorithmException, MalformedURLException {
        String urlDecode = URLDecoder.decode(url, StandardCharsets.UTF_8);
        URL parsedUrl = new URL(urlDecode);
        String path = parsedUrl.getPath();
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        String query = parsedUrl.getQuery();

        Map<String, String> params = Arrays.stream(query.split("&"))
            .map(s -> s.split("="))
            .collect(Collectors.toMap(a -> a[0], a -> a[1]));


        return generate(path, params, secretKey);
    }


    public static String generate(String urlResourcePart, Map<String, String> params, String secretKey) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        if (urlResourcePart.startsWith("/")) {
            urlResourcePart = urlResourcePart.substring(1);
        }
        List<Map.Entry<String, String>> parameters = new LinkedList<>(params.entrySet());
        parameters.sort(Map.Entry.comparingByKey());
        StringBuilder sb = new StringBuilder();
        sb.append(urlResourcePart).append("_");

        for (Map.Entry<String, String> parameter : parameters) {
            sb.append(parameter.getKey()).append("=").append(parameter.getValue()).append("_");
        }
        sb.append(secretKey);
        String baseString = URLEncoder.encode(sb.toString(), StandardCharsets.UTF_8);
//        logger.info(sb + "\n== urlEncode ==> \n" + baseString);
        String result = md5(baseString);
//        logger.info(sb + "\n== md5 ==> \n" + result);
        return result;
    }

    public static String md5(String str) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance(ALGORITHM);
        md.update(str.getBytes());
        byte[] digest = md.digest();
        BigInteger bi = new BigInteger(1, digest);
        return String.format("%0" + (digest.length << 1) + "x", bi);
    }

    /**
     * webhook签名验证 验签
     * @param url webhook推送过来的url 可以通过HttpServletRequest request 获取
     * request.getRequestURL().toString() + request.getQueryString()
     * @param secretKey 数据推送设置的secret值
     * @return
     */
    public static Boolean verifySign(String url, String secretKey) throws UnsupportedEncodingException, NoSuchAlgorithmException, MalformedURLException  {
        System.out.println("*****************start to verify sign*****************");
        String urlDecode = URLDecoder.decode(url, StandardCharsets.UTF_8);
        URL parsedUrl = null;
        try {
            parsedUrl = new URL(urlDecode);
        } catch (Exception e) {
            return false;
        }

        String path = parsedUrl.getPath();
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        //获取url的参数
        String query = parsedUrl.getQuery();
        System.out.println("queryParams: " + query);
        if (query == null || query.isEmpty()) return false;

        //获取
        Map<String, String> params = Arrays.stream(query.split("&"))
                .map(s -> s.split("="))
                .collect(Collectors.toMap(a -> a[0], a -> a[1]));

        //获取url的签名值
        String sign = params.getOrDefault("sign",null);
        System.out.println("sign:   " + sign);
        if (sign == null || sign.isEmpty()) return false;

        // 删除键为sign的键值对
        params.remove("sign");
        //根据url参数生成签名
        String mySign =  generate(path, params, secretKey);
        System.out.println("mySign: " + mySign);
        return mySign.equals(sign);
    }

    public static void main(String[] args) throws UnsupportedEncodingException, NoSuchAlgorithmException, MalformedURLException {
        System.out.println("*****************start to create sign*****************");
        Map<String, String> params = new HashMap<>();
        String appKey = "1234567890";
        String secretKey = "abcdefghijklmnopqrstuvwxyz123456";
        params.put("signt", "now");
        params.put("nonce", "requestId");
        params.put("paramB", "valueB");
        params.put("paramA", "valueA");
        params.put("paramC", "valueC");
        params.put("appkey", appKey);
        String urlResourcePart = "user/user/getUserInfo";
        String sign = generate(urlResourcePart, params, secretKey);
        System.out.println("sign:  " + sign);
        //生成签名
        String url1 = "http://www.baidu.com/user/user/getUserInfo?nonce=requestId&paramB=valueB&paramA=valueA&paramC=valueC&appkey=" + appKey+ "&signt=now";
        String sign1 = generate(url1, secretKey);
        System.out.println("sign1: " + sign1);

        String url2 = "http%3A%2F%2Fwww.baidu.com%2Fuser%2Fuser%2FgetUserInfo%3Fnonce%3DrequestId%26paramB%3DvalueB%26paramA%3DvalueA%26paramC%3DvalueC%26appkey%3D"+ appKey +"%26signt%3Dnow";
        String sign2 = generate(url2, secretKey);
        System.out.println("sign2: " + sign2);

        String url3 = "http://www.baidu.com/user/user/getUserInfo?nonce=requestId&paramB=valueB&paramA=valueA&paramC=valueC";
        url3 = url3 + ((url3.contains("?") || url3.contains("%3F")) ? "&signt=" : "?signt=") + "now";
        url3 = url3 + "&appkey=" + appKey;
        System.out.println("sign3: " + SignGenerator.generate(url3, secretKey));

        String url4 = "http://www.baidu.com/user/user/getUserInfo?nonce=requestId&paramB=valueB&paramA=valueA&paramC=valueC";
        String signtParam = (url4.contains("?") || url4.contains("%3F")) ? "&signt=" : "?signt=";
        url4 = url4 + signtParam + "now";
        url4 += "&appkey=" + appKey;
        System.out.println("sign4: " + SignGenerator.generate(url4, secretKey));
        System.out.println("*****************end to create sign*****************");

        //验证签名
        String url5 = "http://www.baidu.com/user/user/getUserInfo?nonce=requestId&paramB=valueB&paramA=valueA&paramC=valueC&appkey=" + appKey + "&signt=now&sign=02524548c38570d952e4ca9a843949cc";
        Boolean bool = verifySign(url5, secretKey);
        System.out.println("verify sign " + bool);
        System.out.println("*****************end to verify sign*****************");

    }
}
