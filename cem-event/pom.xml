<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.hanyi</groupId>
        <artifactId>parent-worker</artifactId>
        <version>${revision}.${sha1}-${changelist}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>cem-event</artifactId>
    <!--  要修改版本号，直接改最外层pom的revision  -->
    <version>${revision}.${sha1}-${changelist}</version>
    <name>cem-event</name>
    <description>cem core project</description>

    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.befun.auth</groupId>
            <artifactId>befun-auth-pay</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>cem-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-customer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-event-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-data-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-journey-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-base</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mayabot.mynlp</groupId>
            <artifactId>mynlp</artifactId>
            <version>${mynlp.version}</version>
        </dependency>
        <dependency>
            <groupId>org.befun.nlp</groupId>
            <artifactId>befun-nlp-model</artifactId>
            <version>${befun.nlp.model.version}</version>
        </dependency>
        <dependency>
            <groupId>org.befun.nlp</groupId>
            <artifactId>befun-nlp-core</artifactId>
            <version>${befun.nlp.core.version}</version>
        </dependency>
        <dependency>
            <groupId>org.befun.bi</groupId>
            <artifactId>bi-dashboard</artifactId>
        </dependency>
        <dependency>
            <groupId>org.befun.bi</groupId>
            <artifactId>bi-template</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-csv</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mayabot.mynlp.resource</groupId>
            <artifactId>mynlp-resource-coredict</artifactId>
            <version>${mynlp.resource.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mayabot.mynlp.resource</groupId>
            <artifactId>mynlp-resource-cws</artifactId>
            <version>${mynlp.resource.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hanyi.expression</groupId>
            <artifactId>survey-expression</artifactId>
            <version>${hanyi.expression.version}</version>
        </dependency>
        <dependency>
            <groupId>org.befun</groupId>
            <artifactId>web-service</artifactId>
            <version>0.1.7-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>