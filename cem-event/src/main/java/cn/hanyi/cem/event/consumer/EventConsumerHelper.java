package cn.hanyi.cem.event.consumer;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.consumer.WorkerConsumerHelper;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.repository.CemEventRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 每一个队列都有自己独立的 帮助类
 */
@Slf4j
public abstract class EventConsumerHelper extends WorkerConsumerHelper<CemEvent, CemEventRepository, IEventConsumer<Object>> {

    @Autowired(required = false)
    private List<IEventConsumer<?>> eventConsumers;

    /**
     * 异步执行的并发数
     */
    private final Map<EventType, List<IEventConsumer<Object>>> allConsumerMap = new HashMap<>();

    @PostConstruct
    @SuppressWarnings("unchecked")
    public void init() {
        Optional.ofNullable(eventConsumers).ifPresent(list -> {
            list.forEach(consumer -> {
                consumer.types().forEach(type -> {
                    allConsumerMap.computeIfAbsent(type, i -> new ArrayList<>()).add((IEventConsumer<Object>) consumer);
                });
            });
        });
    }

    @Override
    public List<IEventConsumer<Object>> getAllConsumers(CemEvent entity) {
        return allConsumerMap.get(entity.getType());
    }

    @Override
    public Object parseParam(CemEvent entity) {
        return JsonHelper.toObject(entity.getContent(), entity.getType().paramClass);
    }
}
