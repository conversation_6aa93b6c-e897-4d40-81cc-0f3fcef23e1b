package cn.hanyi.cem.event.consumer.adminx;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventChannelDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.befun.task.mq.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import static cn.hanyi.cem.core.constant.EventType.*;

@Slf4j
@Component
public class ChannelEventConsumer implements IEventConsumer<EventChannelDto> {

    @Autowired
    private ITaskService taskService;

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Value("${adminx.channel-operation}")
    private String channelOperationUri;

    @Value("${adminx.channel-operation-key}")
    private String channelOperationKey;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public List<EventType> types() {
        return List.of(CHANNEL_CREATE, CHANNEL_CLOSE, CHANNEL_PAUSE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent event, EventChannelDto dto){

        log.info("adminx channel consumer: surveyId={}, channelId={}, type={}", dto.getSurveyId(), dto.getChannelId(), event.getType());
        var channel = surveyBaseEntityService.require(SurveyChannel.class, dto.getChannelId());
        var survey = surveyBaseEntityService.require(Survey.class, dto.getSurveyId());
        if(channel.getType() == ChannelType.SURVEY_PLUS && channel != null && survey != null) {
            SurveyChannelOperationDto channelOperationDto = SurveyChannelOperationDto.builder()
                    .type(event.getType())
                    .workerId(event.getId())
                    .surveyId(dto.getSurveyId())
                    .surveyTitle(survey.getTitle())
                    .surveyRealTitle(survey.getRealTitle())
                    .orgId(survey.getOrgId())
                    .channelId(channel.getId())
                    .channelName(channel.getName())
                    .channelConfigure(channel.getConfigure())
                    .channelStatus(channel.getStatus())
                    .build();

            taskService.addTask(channelOperationKey,channelOperationDto);
            return ConsumerStatus.SUCCESS;
        }
        return ConsumerStatus.CANCELED;
    }

}
