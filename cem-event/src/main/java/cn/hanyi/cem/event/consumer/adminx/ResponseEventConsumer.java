//package cn.hanyi.cem.event.consumer.adminx;
//
//import cn.hanyi.cem.core.constant.ConsumerStatus;
//import cn.hanyi.cem.core.constant.EventType;
//import cn.hanyi.cem.core.dto.event.EventResponseDto;
//import cn.hanyi.cem.core.entity.CemEvent;
//import cn.hanyi.cem.event.consumer.IEventConsumer;
//import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
//import cn.hanyi.survey.core.entity.Survey;
//import cn.hanyi.survey.core.entity.SurveyResponse;
//import cn.hanyi.survey.core.service.SurveyBaseEntityService;
//import lombok.extern.slf4j.Slf4j;
//import org.befun.core.utils.JsonHelper;
//import org.befun.task.mq.ITaskService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.http.*;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.List;
//
//import static cn.hanyi.cem.core.constant.EventType.*;
//
//@Slf4j
//@Component
//public class ResponseEventConsumer implements IEventConsumer<EventResponseDto> {
//
//    @Autowired
//    private ITaskService taskService;
//
//    @Autowired
//    private SurveyBaseEntityService surveyBaseEntityService;
//
//    @Override
//    public List<EventType> types() {
//        return List.of(RESPONSE_VIEW, RESPONSE_SUBMIT_FINAL);
//    }
//
//    @Value("${adminx.response-submit-key}")
//    private String responseSubmitKey;
//
//    @Value("${adminx.response-view-key}")
//    private String responseViewKey;
//
//    @Autowired
//    private StringRedisTemplate redisTemplate;
//
//    @Transactional
//    @Override
//    public ConsumerStatus consumer(CemEvent event, EventResponseDto dto) {
//
//        log.info("adminx response consumer: surveyId={}, responseId={}, type={}", dto.getSurveyId(), dto.getResponseId(), event.getType());
//        var response = surveyBaseEntityService.require(SurveyResponse.class, dto.getResponseId());
//        if(response.getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS) {
//            switch (event.getType()) {
//                case RESPONSE_VIEW:
//                    SurveyResponseViewDto responseViewDto = new SurveyResponseViewDto(event.getType(), event.getId(), dto.getSurveyId(), response);
//                    //redisTemplate.opsForList().leftPush(responseViewKey, JsonHelper.toJson(responseViewDto));
//                    taskService.addTask(responseViewKey,responseViewDto);
//                    break;
//                case RESPONSE_SUBMIT_FINAL:
//                    var survey = surveyBaseEntityService.require(Survey.class, dto.getSurveyId());
//                    var cells = response.getCells();
//                    SurveyResponseMessageDto responseMessageDto = new SurveyResponseMessageDto(survey, response, cells);
//                    //redisTemplate.opsForList().leftPush(responseSubmitKey, JsonHelper.toJson(responseMessageDto));
//                    taskService.addTask(responseSubmitKey,responseMessageDto);
//                    break;
//                default:
//            }
//            return ConsumerStatus.SUCCESS;
//        }
//        return ConsumerStatus.CANCELED;
//    }
//
//}
