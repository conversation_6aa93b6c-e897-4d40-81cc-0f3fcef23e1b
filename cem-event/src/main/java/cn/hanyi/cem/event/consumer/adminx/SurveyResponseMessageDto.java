//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.hanyi.cem.event.consumer.adminx;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.message.SurveyResponseCellMessageDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.utilis.QuestionsUtils;
import org.befun.core.dto.BaseDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.task.BaseTaskDetailDto;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class SurveyResponseMessageDto extends BaseTaskDetailDto {
    private Long surveyId;
    private Long orgId;
    private String surveyName;
    private String surveyCode;
    private Long responseId;
    private String clientId;
    private String externalUserId;
    private Long customerId;
    private Long departmentId;
    private Long sceneId;
    private String trackId;
    private String openid;
    private String departmentName;
    private String customerName;
    private String customerGender;
    private String departmentCode;
    private String groupCode;
    private String externalCompanyId;
    private Object defaultPa;
    private Object defaultPb;
    private Object defaultPc;
    private List<SurveyResponseCellMessageDto> data = new ArrayList();
    private Long startTime;
    private Long finishTime;
    private Integer durationInSeconds;
    private Long channelId;
    private Map<String, Object> parameters = new HashMap();
    private SurveyCollectorMethod collectorMethod;
    private ResponseStatus status;
    private List<String> tags;

    public SurveyResponseMessageDto(Survey survey, SurveyResponse response, Collection<SurveyResponseCell> cells) {
        this.collectorMethod = SurveyCollectorMethod.LINK;
        this.status = ResponseStatus.INIT;
        List<QuestionType> SKIP_QUESTIONS_TYPE = List.of(QuestionType.MARK, QuestionType.EMPTY, QuestionType.SEPARATOR, QuestionType.MEDIA, QuestionType.GROUP);
        this.surveyId = survey.getId();
        this.orgId = survey.getOrgId();
        this.surveyName = survey.getTitle();
        this.surveyCode = survey.getSurveyCode();
        this.responseId = response.getId();
        this.clientId = response.getClientId();
        this.customerId = response.getCustomerId();
        this.departmentId = response.getDepartmentId();
        this.externalUserId = response.getExternalUserId();
        this.trackId = response.getTrackId();
        this.sceneId = response.getSceneId();
        this.openid = response.getOpenid();
        this.departmentName = response.getDepartmentName();
        this.customerName = response.getCustomerName();
        this.customerGender = response.getCustomerGender();
        this.departmentCode = response.getDepartmentCode();
        this.externalCompanyId = response.getExternalCompanyId();
        this.defaultPa = response.getDefaultPa();
        this.defaultPb = response.getDefaultPb();
        this.defaultPc = response.getDefaultPc();
        this.parameters = response.getParameters();
        this.startTime = response.getCreateTime().getTime();
        this.finishTime = response.getFinishTime() == null ? 0L : response.getFinishTime().getTime();
        this.durationInSeconds = response.getDurationSeconds();
        this.channelId = response.getChannelId();
        this.collectorMethod = response.getCollectorMethod();
        this.status = response.getStatus();
        this.tags = response.getTags();
        List<SurveyResponseCell> mergeCellList = new ArrayList();
        QuestionsUtils.questionsFilterGroup(survey.getQuestions()).stream().filter((surveyQuestion) -> {
            return !SKIP_QUESTIONS_TYPE.contains(surveyQuestion.getType());
        }).forEach((surveyQuestion) -> {
            Optional<SurveyResponseCell> questionsCell = cells.stream().filter((c) -> {
                return c.getQuestionId().equals(surveyQuestion.getId());
            }).findFirst();
            SurveyResponseCell cell = questionsCell.isEmpty() ? new SurveyResponseCell(survey, surveyQuestion, response) : (SurveyResponseCell)questionsCell.get();
            mergeCellList.add(cell);
        });
        Map<Long, SurveyQuestion> questionIdMap = (Map)survey.getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        mergeCellList.forEach((cell) -> {
            this.data.add(new SurveyResponseCellMessageDto((SurveyQuestion)questionIdMap.get(cell.getQuestionId()), cell));
        });
    }


    public static SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder builder() {
        return new SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder();
    }

    public Long getSurveyId() {
        return this.surveyId;
    }

    public Long getOrgId() {
        return this.orgId;
    }

    public String getSurveyName() {
        return this.surveyName;
    }

    public String getSurveyCode() {
        return this.surveyCode;
    }

    public Long getResponseId() {
        return this.responseId;
    }

    public String getClientId() {
        return this.clientId;
    }

    public String getExternalUserId() {
        return this.externalUserId;
    }

    public Long getCustomerId() {
        return this.customerId;
    }

    public Long getDepartmentId() {
        return this.departmentId;
    }

    public Long getSceneId() {
        return this.sceneId;
    }

    public String getTrackId() {
        return this.trackId;
    }

    public String getOpenid() {
        return this.openid;
    }

    public String getDepartmentName() {
        return this.departmentName;
    }

    public String getCustomerName() {
        return this.customerName;
    }

    public String getCustomerGender() {
        return this.customerGender;
    }

    public String getDepartmentCode() {
        return this.departmentCode;
    }

    public String getGroupCode() {
        return this.groupCode;
    }

    public String getExternalCompanyId() {
        return this.externalCompanyId;
    }

    public Object getDefaultPa() {
        return this.defaultPa;
    }

    public Object getDefaultPb() {
        return this.defaultPb;
    }

    public Object getDefaultPc() {
        return this.defaultPc;
    }

    public List<SurveyResponseCellMessageDto> getData() {
        return this.data;
    }

    public Long getStartTime() {
        return this.startTime;
    }

    public Long getFinishTime() {
        return this.finishTime;
    }

    public Integer getDurationInSeconds() {
        return this.durationInSeconds;
    }

    public Long getChannelId() {
        return this.channelId;
    }

    public Map<String, Object> getParameters() {
        return this.parameters;
    }

    public SurveyCollectorMethod getCollectorMethod() {
        return this.collectorMethod;
    }

    public ResponseStatus getStatus() {
        return this.status;
    }

    public List<String> getTags() {
        return this.tags;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public void setSurveyName(String surveyName) {
        this.surveyName = surveyName;
    }

    public void setSurveyCode(String surveyCode) {
        this.surveyCode = surveyCode;
    }

    public void setResponseId(Long responseId) {
        this.responseId = responseId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public void setExternalUserId(String externalUserId) {
        this.externalUserId = externalUserId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public void setSceneId(Long sceneId) {
        this.sceneId = sceneId;
    }

    public void setTrackId(String trackId) {
        this.trackId = trackId;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public void setCustomerGender(String customerGender) {
        this.customerGender = customerGender;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public void setExternalCompanyId(String externalCompanyId) {
        this.externalCompanyId = externalCompanyId;
    }

    public void setDefaultPa(Object defaultPa) {
        this.defaultPa = defaultPa;
    }

    public void setDefaultPb(Object defaultPb) {
        this.defaultPb = defaultPb;
    }

    public void setDefaultPc(Object defaultPc) {
        this.defaultPc = defaultPc;
    }

    public void setData(List<SurveyResponseCellMessageDto> data) {
        this.data = data;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setFinishTime(Long finishTime) {
        this.finishTime = finishTime;
    }

    public void setDurationInSeconds(Integer durationInSeconds) {
        this.durationInSeconds = durationInSeconds;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public void setCollectorMethod(SurveyCollectorMethod collectorMethod) {
        this.collectorMethod = collectorMethod;
    }

    public void setStatus(ResponseStatus status) {
        this.status = status;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public SurveyResponseMessageDto() {
        this.collectorMethod = SurveyCollectorMethod.LINK;
        this.status = ResponseStatus.INIT;
    }

    public SurveyResponseMessageDto(Long surveyId, Long orgId, String surveyName, String surveyCode, Long responseId, String clientId, String externalUserId, Long customerId, Long departmentId, Long sceneId, String trackId, String openid, String departmentName, String customerName, String customerGender, String departmentCode, String groupCode, String externalCompanyId, Object defaultPa, Object defaultPb, Object defaultPc, List<SurveyResponseCellMessageDto> data, Long startTime, Long finishTime, Integer durationInSeconds, Long channelId, Map<String, Object> parameters, SurveyCollectorMethod collectorMethod, ResponseStatus status, List<String> tags) {
        this.collectorMethod = SurveyCollectorMethod.LINK;
        this.status = ResponseStatus.INIT;
        this.surveyId = surveyId;
        this.orgId = orgId;
        this.surveyName = surveyName;
        this.surveyCode = surveyCode;
        this.responseId = responseId;
        this.clientId = clientId;
        this.externalUserId = externalUserId;
        this.customerId = customerId;
        this.departmentId = departmentId;
        this.sceneId = sceneId;
        this.trackId = trackId;
        this.openid = openid;
        this.departmentName = departmentName;
        this.customerName = customerName;
        this.customerGender = customerGender;
        this.departmentCode = departmentCode;
        this.groupCode = groupCode;
        this.externalCompanyId = externalCompanyId;
        this.defaultPa = defaultPa;
        this.defaultPb = defaultPb;
        this.defaultPc = defaultPc;
        this.data = data;
        this.startTime = startTime;
        this.finishTime = finishTime;
        this.durationInSeconds = durationInSeconds;
        this.channelId = channelId;
        this.parameters = parameters;
        this.collectorMethod = collectorMethod;
        this.status = status;
        this.tags = tags;
    }

    public static class SurveyResponseMessageDtoBuilder {
        private Long surveyId;
        private Long orgId;
        private String surveyName;
        private String surveyCode;
        private Long responseId;
        private String clientId;
        private String externalUserId;
        private Long customerId;
        private Long departmentId;
        private Long sceneId;
        private String trackId;
        private String openid;
        private String departmentName;
        private String customerName;
        private String customerGender;
        private String departmentCode;
        private String groupCode;
        private String externalCompanyId;
        private Object defaultPa;
        private Object defaultPb;
        private Object defaultPc;
        private List<SurveyResponseCellMessageDto> data;
        private Long startTime;
        private Long finishTime;
        private Integer durationInSeconds;
        private Long channelId;
        private Map<String, Object> parameters;
        private SurveyCollectorMethod collectorMethod;
        private ResponseStatus status;
        private List<String> tags;

        SurveyResponseMessageDtoBuilder() {
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder surveyId(Long surveyId) {
            this.surveyId = surveyId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder orgId(Long orgId) {
            this.orgId = orgId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder surveyName(String surveyName) {
            this.surveyName = surveyName;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder surveyCode(String surveyCode) {
            this.surveyCode = surveyCode;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder responseId(Long responseId) {
            this.responseId = responseId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder externalUserId(String externalUserId) {
            this.externalUserId = externalUserId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder customerId(Long customerId) {
            this.customerId = customerId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder departmentId(Long departmentId) {
            this.departmentId = departmentId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder sceneId(Long sceneId) {
            this.sceneId = sceneId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder trackId(String trackId) {
            this.trackId = trackId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder openid(String openid) {
            this.openid = openid;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder departmentName(String departmentName) {
            this.departmentName = departmentName;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder customerName(String customerName) {
            this.customerName = customerName;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder customerGender(String customerGender) {
            this.customerGender = customerGender;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder departmentCode(String departmentCode) {
            this.departmentCode = departmentCode;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder groupCode(String groupCode) {
            this.groupCode = groupCode;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder externalCompanyId(String externalCompanyId) {
            this.externalCompanyId = externalCompanyId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder defaultPa(Object defaultPa) {
            this.defaultPa = defaultPa;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder defaultPb(Object defaultPb) {
            this.defaultPb = defaultPb;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder defaultPc(Object defaultPc) {
            this.defaultPc = defaultPc;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder data(List<SurveyResponseCellMessageDto> data) {
            this.data = data;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder startTime(Long startTime) {
            this.startTime = startTime;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder finishTime(Long finishTime) {
            this.finishTime = finishTime;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder durationInSeconds(Integer durationInSeconds) {
            this.durationInSeconds = durationInSeconds;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder channelId(Long channelId) {
            this.channelId = channelId;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder parameters(Map<String, Object> parameters) {
            this.parameters = parameters;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder collectorMethod(SurveyCollectorMethod collectorMethod) {
            this.collectorMethod = collectorMethod;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder status(ResponseStatus status) {
            this.status = status;
            return this;
        }

        public SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder tags(List<String> tags) {
            this.tags = tags;
            return this;
        }

        public SurveyResponseMessageDto build() {
            return new SurveyResponseMessageDto(this.surveyId, this.orgId, this.surveyName, this.surveyCode, this.responseId, this.clientId, this.externalUserId, this.customerId, this.departmentId, this.sceneId, this.trackId, this.openid, this.departmentName, this.customerName, this.customerGender, this.departmentCode, this.groupCode, this.externalCompanyId, this.defaultPa, this.defaultPb, this.defaultPc, this.data, this.startTime, this.finishTime, this.durationInSeconds, this.channelId, this.parameters, this.collectorMethod, this.status, this.tags);
        }

        public String toString() {
            return "SurveyResponseMessageDto.SurveyResponseMessageDtoBuilder(surveyId=" + this.surveyId + ", orgId=" + this.orgId + ", surveyName=" + this.surveyName + ", surveyCode=" + this.surveyCode + ", responseId=" + this.responseId + ", clientId=" + this.clientId + ", externalUserId=" + this.externalUserId + ", customerId=" + this.customerId + ", departmentId=" + this.departmentId + ", sceneId=" + this.sceneId + ", trackId=" + this.trackId + ", openid=" + this.openid + ", departmentName=" + this.departmentName + ", customerName=" + this.customerName + ", customerGender=" + this.customerGender + ", departmentCode=" + this.departmentCode + ", groupCode=" + this.groupCode + ", externalCompanyId=" + this.externalCompanyId + ", defaultPa=" + this.defaultPa + ", defaultPb=" + this.defaultPb + ", defaultPc=" + this.defaultPc + ", data=" + this.data + ", startTime=" + this.startTime + ", finishTime=" + this.finishTime + ", durationInSeconds=" + this.durationInSeconds + ", channelId=" + this.channelId + ", parameters=" + this.parameters + ", collectorMethod=" + this.collectorMethod + ", status=" + this.status + ", tags=" + this.tags + ")";
        }
    }
}
