package cn.hanyi.cem.event.consumer.adminx;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.message.QuestionsItemDto;
import cn.hanyi.survey.core.dto.message.QuestionsItemsDto;
import cn.hanyi.survey.core.dto.message.SurveyResponseCellMessageDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.utilis.QuestionsUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;
import org.befun.core.entity.BaseEntity;
import org.befun.core.utils.JsonHelper;
import org.befun.task.BaseTaskDetailDto;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurveyResponseSubmitDto extends BaseTaskDetailDto {
    private EventType type;
    private Long workerId;
    private Long surveyId;
    private Long orgId;
    private String surveyName;
    private String surveyCode;
    private Long responseId;
    private String clientId;
    private String externalUserId;
    private Long customerId;
    private Long departmentId;
    private Long sceneId;
    private String trackId;
    private String openid;
    private String departmentName;
    private String customerName;
    private String customerGender;
    private String departmentCode;
    private String groupCode;       // 1.8.3 新增客户组编号
    private String externalCompanyId;
    private Object defaultPa;
    private Object defaultPb;
    private Object defaultPc;
    private List<SurveyResponseCellMessageDto> data = new ArrayList<>();
    private Long startTime;
    private Long finishTime;
    private Integer durationInSeconds;
    private Long channelId;
    private Map<String, Object> parameters = new HashMap<>();
    private SurveyCollectorMethod collectorMethod = SurveyCollectorMethod.LINK;
    private ResponseStatus status = ResponseStatus.INIT;
    private List<String> tags;

    public SurveyResponseSubmitDto(EventType type, Long workerId, Survey survey, SurveyResponse response, Collection<SurveyResponseCell> cells) {
        //不推送题型
        List<QuestionType> SKIP_QUESTIONS_TYPE = List.of(
                QuestionType.MARK,
                QuestionType.EMPTY,
                QuestionType.SEPARATOR,
                QuestionType.MEDIA,
                QuestionType.GROUP
        );
        //worker
        this.type = type;
        this.workerId = workerId;

        // survey
        this.surveyId = survey.getId();
        this.orgId = survey.getOrgId();
        this.surveyName = survey.getTitle();
        this.surveyCode = survey.getSurveyCode();

        // response
        this.responseId = response.getId();
        this.clientId = response.getClientId();
        this.customerId = response.getCustomerId();
        this.departmentId = response.getDepartmentId();
        this.externalUserId = response.getExternalUserId();
        this.trackId = response.getTrackId();
        this.sceneId = response.getSceneId();
        this.openid = response.getOpenid();
        this.departmentName = response.getDepartmentName();
        this.customerName = response.getCustomerName();
        this.customerGender = response.getCustomerGender();
        this.departmentCode = response.getDepartmentCode();
        this.externalCompanyId = response.getExternalCompanyId();
        this.defaultPa = response.getDefaultPa();
        this.defaultPb = response.getDefaultPb();
        this.defaultPc = response.getDefaultPc();
        this.parameters = response.getParameters();
        this.startTime = response.getCreateTime().getTime();
        this.finishTime = response.getFinishTime() == null ? 0l : response.getFinishTime().getTime();
        this.durationInSeconds = response.getDurationSeconds();
        this.channelId = response.getChannelId();
        this.collectorMethod = response.getCollectorMethod();
        this.status = response.getStatus();
        this.tags = response.getTags();

        // data
        List<SurveyResponseCell> mergeCellList = new ArrayList<>();
        QuestionsUtils.questionsFilterGroup(survey.getQuestions()).stream()
                .filter(surveyQuestion -> !SKIP_QUESTIONS_TYPE.contains(surveyQuestion.getType()))
                .forEach(surveyQuestion -> {
                    Optional<SurveyResponseCell> questionsCell = cells.stream().filter(c ->  c.getQuestionId().equals(surveyQuestion.getId())).findFirst();
                    SurveyResponseCell cell = questionsCell.isEmpty() ? new SurveyResponseCell(survey, surveyQuestion, response) : questionsCell.get();
                    mergeCellList.add(cell);

                });

        Map<Long, SurveyQuestion> questionIdMap = survey.getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        mergeCellList.forEach(cell -> {
            data.add(new SurveyResponseCellMessageDto(questionIdMap.get(cell.getQuestionId()),cell));
        });
    }


    /**
     * 构建用于生成sel的数据
     *
     * @return
     */
    public Map<String, Object> convertData() {
        HashMap content = new HashMap();
        ObjectMapper objectMapper = new ObjectMapper();

        data.stream().filter(q -> q.getValue() != null).forEach(questionsItems -> {

            // 单选和多选需要选中`其他`预警
            if (questionsItems.getType() == QuestionType.SINGLE_CHOICE || questionsItems.getType() == QuestionType.COMBOBOX) {
                QuestionsItemsDto questionsItemsDto = objectMapper.convertValue(
                        questionsItems.getQuestionsItems().get(questionsItems.getQuestionsItems().keySet().iterator().next()),
                        QuestionsItemsDto.class
                );
                // 如果value不在item中，说明选择了其他
                Optional<QuestionsItemDto> others = questionsItemsDto.getItems().stream().filter(item -> item.getName().equals(questionsItemsDto.getValue())).findFirst();
                if (others.isEmpty()) {
                    // 使用others是作为预警规则选中'other'
                    String other = "other";
                    questionsItemsDto.setValue(other);
                    questionsItemsDto.getItems().add(new QuestionsItemDto(other, other));

                }
            }

            if (questionsItems.getType() == QuestionType.MULTIPLE_CHOICES) {
                QuestionsItemsDto questionsItemsDto = objectMapper.convertValue(
                        questionsItems.getQuestionsItems().get(questionsItems.getQuestionsItems().keySet().iterator().next()),
                        QuestionsItemsDto.class);
                // 如果value不在item中，说明选择了其他
                Optional others = ((List) questionsItemsDto.getValue()).stream().filter(item ->
                        !questionsItemsDto.getItems().stream().map(QuestionsItemDto::getName).collect(Collectors.toList()).contains(item)
                ).findFirst();
                if (!others.isEmpty()) {
                    // 使用others是作为预警规则选中'other'
                    String other = "other";
                    Collections.replaceAll((List) questionsItemsDto.getValue(), others.get(), other);
                    questionsItemsDto.getItems().add(new QuestionsItemDto(other, other));
                }
            }
            content.put(
                    questionsItems.getQuestionsItems().keySet().iterator().next(),
                    JsonHelper.toMap(questionsItems.getQuestionsItems().values().iterator().next())
            );
        });
        return content;
    }
}
