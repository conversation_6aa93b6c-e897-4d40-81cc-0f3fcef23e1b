package cn.hanyi.cem.event.consumer.adminx;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import lombok.*;
import org.befun.task.BaseTaskDetailDto;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/7/4 17:54
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurveyResponseViewDto extends BaseTaskDetailDto {
    private EventType type;
    private Long workerId;
    private Long surveyId;
    private Long responseId;
    private String clientId;
    private String externalUserId;
    private String trackId;
    private String openid;
    private String customerName;
    private SurveyCollectorMethod collectorMethod;
    private ResponseStatus status;


    public SurveyResponseViewDto(EventType type, Long workerId, Long surveyId, SurveyResponse response) {
        this.type = type;
        this.workerId = workerId;
        this.surveyId = surveyId;
        this.responseId = response.getId();
        this.clientId = response.getClientId();
        this.externalUserId = response.getExternalUserId();
        this.trackId = response.getTrackId();
        this.openid = response.getOpenid();
        this.customerName = response.getCustomerName();
        this.collectorMethod = response.getCollectorMethod();
        this.status = response.getStatus();
    }
}
