package cn.hanyi.cem.event.consumer.adminxchannel;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.notifyconfig.NotifyConfigBuilder;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.task.BaseTaskDetailDto;
import org.befun.task.mq.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.function.BiFunction;

@Slf4j
public abstract class AdminxChannelOrderConsumer<C, P> extends NotifyConfigBuilder<C> implements IEventConsumer<P> {

    @Autowired
    protected AdminxChannelProperties properties;
    @Autowired
    private ITaskService taskService;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private SurveyChannelRepository surveyChannelRepository;

    public ConsumerStatus checkSurveyAndChannel(Long surveyId, Long channelId, CemEvent event,
                                                BiFunction<SimpleSurvey, SurveyChannel, ConsumerStatus> consumer) {
        SimpleSurvey survey;
        if (surveyId == null || surveyId <= 0 || (survey = surveyRepository.findSimpleById(surveyId)) == null) {
            event.setResponse("问卷不存在");
            return ConsumerStatus.FAILED;
        }
        SurveyChannel channel;
        if (channelId == null || channelId <= 0 || (channel = surveyChannelRepository.findById(channelId).orElse(null)) == null) {
            event.setResponse("渠道不存在");
            return ConsumerStatus.FAILED;
        }
        if (channel.getType() != ChannelType.SURVEY_PLUS) {
            event.setResponse(String.format("渠道类型错误，当前渠道为%s，不是调研家社区", channel.getType()));
            return ConsumerStatus.FAILED;
        }
        return consumer.apply(survey, channel);
    }

    protected void addToAdminx(String key, BaseTaskDetailDto dto) {
        taskService.addTask(key, dto);
    }
}
