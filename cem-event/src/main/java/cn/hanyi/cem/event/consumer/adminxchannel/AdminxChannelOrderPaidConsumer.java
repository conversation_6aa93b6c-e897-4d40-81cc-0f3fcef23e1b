package cn.hanyi.cem.event.consumer.adminxchannel;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventAdminxChannelOrderPaid;
import cn.hanyi.cem.core.entity.CemEvent;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.RechargeStatus;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class AdminxChannelOrderPaidConsumer extends AdminxChannelOrderConsumer<AdminxChannelProperties.AdminxChannelOrderPaid, EventAdminxChannelOrderPaid> {

    @Autowired
    private OrganizationOrderService organizationOrderService;
    @Autowired
    private OrganizationRechargeService organizationRechargeService;
    @Value("${adminx.order-pay-key:adminx-order-pay}")
    private String orderPayKey;

    @Override
    public List<EventType> types() {
        return List.of(EventType.ADMINX_CHANNEL_ORDER_PAID);
    }

    @Override
    protected AdminxChannelProperties.AdminxChannelOrderPaid getConfig() {
        return properties.getPaid();
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventAdminxChannelOrderPaid param) {
        //         订单支付通知
        //        >跟进人：${contacts}
        //        >问卷ID：${surveyId}
        //        >问卷标题：${surveyTitle}
        //        >渠道ID：${channelId}
        //        >订单总价：${amount}
        //        >支付时间：${paidTime}
        //        >备注：请在半个小时内处理！[点击查看](${adminxUrl})
        return checkSurveyAndChannel(param.getSurveyId(), param.getChannelId(), entity, (survey, channel) -> {
            OrganizationOrder order = organizationOrderService.get(channel.getOrderId());
            if (order != null && order.getStatus() == OrderStatus.success) {
                Map<String, Object> params = new HashMap<>();
                if (order.getAmount() != null) {
                    params.put("amount", (order.getAmount() / 100.00) + "元");
                } else {
                    return ConsumerStatus.FAILED;
                }
                params.put("contacts", getConfig().getContacts());
                params.put("surveyId", param.getSurveyId());
                params.put("surveyTitle", survey.getTitle());
                params.put("channelId", param.getChannelId());
                OrganizationRecharge recharge = organizationRechargeService.get(order.getRechargeId());
                if (recharge != null && recharge.getStatus() == RechargeStatus.success) {
                    params.put("paidTime", DateHelper.formatDateTime(recharge.getPayTime()));
                } else {
                    params.put("paidTime", DateHelper.formatDateTime(order.getCreateTime()));
                }
                sendWeWorkBot(params);
                if (recharge != null) {
                    addToAdminx(orderPayKey, new AdminxChannelOrderPayDto(
                            entity.getOrgId(),
                            entity.getUserId(),
                            survey.getId(),
                            channel.getId(),
                            recharge.getId(),
                            null,
                            "paid")
                    );
                }
                return ConsumerStatus.SUCCESS;
            }
            return ConsumerStatus.FAILED;
        });
    }

}
