package cn.hanyi.cem.event.consumer.adminxchannel;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventAdminxChannelOrderQuoted;
import cn.hanyi.cem.core.entity.CemEvent;
import org.befun.auth.constant.AppType;
import org.befun.auth.service.UserService;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class AdminxChannelOrderQuotedConsumer extends AdminxChannelOrderConsumer<AdminxChannelProperties.AdminxChannelOrderCommon, EventAdminxChannelOrderQuoted> {

    @Autowired
    private UserService userService;

    @Override
    public List<EventType> types() {
        return List.of(EventType.ADMINX_CHANNEL_ORDER_QUOTED);
    }

    @Override
    protected AdminxChannelProperties.AdminxChannelOrderCommon getConfig() {
        return properties.getQuoted();
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventAdminxChannelOrderQuoted param) {
        // 站内信
        // 样本报价：您提交的样本订单有最新的处理消息了，点击查看
        // 短信
        // 短信签名：【调研家】
        // 短信内容：您提交的样本服务订单已报价，请尽快登录系统查看！
        // 公众号
        return checkSurveyAndChannel(param.getSurveyId(), param.getChannelId(), entity, (survey, channel) -> {
            Map<String, Object> params = new HashMap<>();
            params.put("surveyId", survey.getId());
            params.put("surveyTitle", survey.getTitle());
            params.put("channelId", channel.getId());
            params.put("notifyTime", DateHelper.formatDateTime(new Date()));
            params.putAll(getConfig().getParams());
            notify(entity.getOrgId(), channel.getUserId(), getConfig(), params, AppType.surveyplus);
            return ConsumerStatus.SUCCESS;
        });
    }

}
