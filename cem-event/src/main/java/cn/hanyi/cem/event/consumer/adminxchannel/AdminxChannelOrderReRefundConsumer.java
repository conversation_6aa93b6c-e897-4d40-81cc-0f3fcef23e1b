package cn.hanyi.cem.event.consumer.adminxchannel;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventAdminxChannelOrderRefund;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.entity.OrganizationOrderRefund;
import org.befun.auth.pay.repository.OrganizationOrderRefundRepository;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class AdminxChannelOrderReRefundConsumer extends AdminxChannelOrderConsumer<AdminxChannelProperties.AdminxChannelOrderCommon, EventAdminxChannelOrderRefund> {

    @Autowired
    private OrganizationOrderService organizationOrderService;
    @Autowired
    protected AdminxChannelOrderRefundConsumer adminxChannelOrderRefundConsumer;
    @Autowired
    protected OrganizationOrderRefundRepository organizationOrderRefundRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.ADMINX_CHANNEL_ORDER_RE_REFUND);
    }

    @Override
    protected AdminxChannelProperties.AdminxChannelOrderCommon getConfig() {
        return null;
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventAdminxChannelOrderRefund param) {
        if (param.getRefundAmount() == null) {
            entity.setResponse("重新退款金额不能为空");
            return ConsumerStatus.FAILED;
        }
        return checkSurveyAndChannel(param.getSurveyId(), param.getChannelId(), entity, (survey, channel) -> {
            orderReRefund(entity, param.getRefundAmount().intValue(), survey, channel);
            return ConsumerStatus.SUCCESS;
        });
    }

    public void orderReRefund(CemEvent entity, Integer refundAmount, SimpleSurvey survey, SurveyChannel channel) {
        OrganizationOrder order = organizationOrderService.get(channel.getOrderId());
        if (order != null && order.getStatus() == OrderStatus.refund_failure) {
            try {
                List<OrganizationOrderRefund> refunds = organizationOrderRefundRepository.findByOrderId(order.getId());
                if (CollectionUtils.isNotEmpty(refunds)) {
                    // 有退款单，判断退款金额是否一致
                    OrganizationOrderRefund refund = refunds.get(0);
                    if (refundAmount.equals(refund.getAmountRefund())) {
                        // 使用此退款单进行退款
                        Boolean result = organizationOrderService.reRefundOrder(order.getOrgId(), 0L, OrderType.order_adminx_channel, refund.getId());
                        adminxChannelOrderRefundConsumer.refundResult(result, entity.getOrgId(), entity.getUserId(), survey, channel, order, refundAmount);
                    }
                } else {
                    // 重新发起一次退款
                    adminxChannelOrderRefundConsumer.orderRefund(entity, refundAmount, survey, channel);
                }
            } catch (Throwable e) {
                log.error("{} order re refund error", getClass().getName(), e);
            }
        }
    }

}
