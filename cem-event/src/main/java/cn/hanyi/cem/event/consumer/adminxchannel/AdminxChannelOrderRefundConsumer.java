package cn.hanyi.cem.event.consumer.adminxchannel;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventAdminxChannelOrderRefund;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.constant.AppType;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.dto.order.AdminxChannelOrderRefundRequestDto;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.entity.OrganizationOrderRefund;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.auth.pay.repository.OrganizationOrderRefundRepository;
import org.befun.auth.pay.repository.OrganizationRechargeRefundRepository;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AdminxChannelOrderRefundConsumer extends AdminxChannelOrderConsumer<AdminxChannelProperties.AdminxChannelOrderCommon, EventAdminxChannelOrderRefund> {

    @Autowired
    private OrganizationOrderService organizationOrderService;
    @Value("${adminx.order-pay-key:adminx-order-pay}")
    private String orderPayKey;
    @Autowired
    protected OrganizationOrderRefundRepository organizationOrderRefundRepository;
    @Autowired
    protected OrganizationRechargeRefundRepository organizationRechargeRefundRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.ADMINX_CHANNEL_ORDER_REFUND);
    }

    @Override
    protected AdminxChannelProperties.AdminxChannelOrderCommon getConfig() {
        return null;
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventAdminxChannelOrderRefund param) {
        if (param.getRefundAmount() == null) {
            entity.setResponse("退款金额不能为空");
            return ConsumerStatus.FAILED;
        }
        return checkSurveyAndChannel(param.getSurveyId(), param.getChannelId(), entity, (survey, channel) -> {
            orderRefund(entity, param.getRefundAmount().intValue(), survey, channel);
            return ConsumerStatus.SUCCESS;
        });
    }

    public void orderRefund(CemEvent entity, Integer refundAmount, SimpleSurvey survey, SurveyChannel channel) {
        OrganizationOrder order = organizationOrderService.get(channel.getOrderId());
        if (order != null && (order.getStatus() == OrderStatus.success || order.getStatus() == OrderStatus.refund_failure)) {
            Boolean result = null;
            try {
                refundAmount = refundAmount == null ? order.getAmount() : refundAmount;
                AdminxChannelOrderRefundRequestDto requestDto = new AdminxChannelOrderRefundRequestDto(
                        channel.getOrderId(),
                        channel.getId(),
                        refundAmount,
                        channel.getId().toString(),
                        "样本返还：" + survey.getTitle());
                result = organizationOrderService.refundOrder(order.getOrgId(), 0L, OrderType.order_adminx_channel, requestDto);
            } catch (Throwable e) {
                log.error("{} order refund error", getClass().getName(), e);
            }
            if (!refundResult(result, entity.getOrgId(), channel.getUserId(), survey, channel, order, refundAmount)) {
                // 等待第三官方渠道退款
                // 在退款结束的事件中处理
                // RechargeRefundCompletedEventConsumer
                entity.setResponse(String.format("等待第三官方渠道%s退款", order.getPayType()));
            }
        }
    }

    public boolean refundResult(Boolean result, Long orgId, Long userId, SimpleSurvey survey, SurveyChannel channel, OrganizationOrder order, Integer refundAmount) {
        if (result == null) {
            order.setStatus(OrderStatus.refund_failure);
            organizationOrderService.save(order);
            notifyRefundFailure(orgId, channel.getUserId(), survey, channel, order, refundAmount);
            return true;
        } else if (result) {
            notifyRefundSuccess(orgId, channel.getUserId(), survey, channel, order, refundAmount);
            return true;
        }
        return false;
    }

    public void notifyRefundSuccess(Long orgId, Long userId, SimpleSurvey survey, SurveyChannel channel, OrganizationOrder order, Integer refundAmount) {

        // 退款成功
        // 站内信，退款通知：您支付的样本订单金额已原路退回，请注意查看。
        // 公众号
        // 短信
        AdminxChannelProperties.AdminxChannelOrderCommon config = properties.getRefundSuccess();
        Map<String, Object> params = new HashMap<>();
        params.put("surveyId", survey.getId());
        params.put("surveyTitle", survey.getTitle());
        params.put("channelId", channel.getId());
        params.put("refundNo", order.getId());
        params.put("refundAmount", formatRefundAmount(refundAmount, channel));
        params.put("refundTime", DateHelper.formatDateTime(new Date()));
        params.put("notifyTime", DateHelper.formatDateTime(new Date()));
        params.putAll(config.getParams());
        notify(orgId, userId, config, params, AppType.surveyplus);
        List<OrganizationOrderRefund> refunds = organizationOrderRefundRepository.findByOrderId(order.getId());
        if (CollectionUtils.isNotEmpty(refunds)) {
            OrganizationOrderRefund orderRefund = refunds.get(0);
            OrganizationRechargeRefund refund = organizationRechargeRefundRepository.findFirstByOrgIdAndOrderIdAndOrderRefundIdAndRechargeId(
                    order.getOrgId(),
                    order.getId(),
                    orderRefund.getId(),
                    order.getRechargeId()
            );
            if (refund != null) {
                addToAdminx(orderPayKey, new AdminxChannelOrderPayDto(
                        orgId,
                        userId,
                        survey.getId(),
                        channel.getId(),
                        null,
                        refund.getId(),
                        "refund")
                );
            }
        }
    }


    public void notifyRefundFailure(Long orgId, Long userId, SimpleSurvey survey, SurveyChannel channel, OrganizationOrder order, Integer refundAmount) {
        // 退款失败
        // 站内信，退款通知：您支付的样本订单金额退款失败，请联系人工处理。
        // 公众号
        // 短信
        AdminxChannelProperties.AdminxChannelOrderCommon config = properties.getRefundFailure();
        Map<String, Object> params = new HashMap<>();
        params.put("surveyId", survey.getId());
        params.put("surveyTitle", survey.getTitle());
        params.put("channelId", channel.getId());
        params.put("refundNo", order.getId());
        params.put("refundAmount", formatRefundAmount(refundAmount, channel));
        params.put("refundTime", DateHelper.formatDateTime(new Date()));
        params.put("notifyTime", DateHelper.formatDateTime(new Date()));
        params.putAll(config.getParams());
        notify(orgId, userId, config, params, AppType.surveyplus);
    }

    private String formatRefundAmount(Integer refundAmount, SurveyChannel channel) {
        if (refundAmount == null) {
            Map<String, Object> refundInfo = JsonHelper.toMap(channel.getOrderRefund());
            if (refundInfo != null) {
                Object x = refundInfo.get("refundAmount");
                if (x instanceof Number) {
                    return printAmount(((Number) x).intValue());
                }
            }
        }
        return printAmount(refundAmount);
    }

    private String printAmount(Integer amount) {
        if (amount == null) {
            return "0.00";
        } else {
            return amount * 1.0 / 100 + "";
        }
    }
}
