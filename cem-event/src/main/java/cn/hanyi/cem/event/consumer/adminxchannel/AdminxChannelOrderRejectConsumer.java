package cn.hanyi.cem.event.consumer.adminxchannel;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventAdminxChannelOrderReject;
import cn.hanyi.cem.core.entity.CemEvent;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.AppType;
import org.befun.core.utils.DateHelper;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AdminxChannelOrderRejectConsumer extends AdminxChannelOrderConsumer<AdminxChannelProperties.AdminxChannelOrderCommon, EventAdminxChannelOrderReject> {

    @Override
    public List<EventType> types() {
        return List.of(EventType.ADMINX_CHANNEL_ORDER_REJECT);
    }

    @Override
    protected AdminxChannelProperties.AdminxChannelOrderCommon getConfig() {
        return properties.getReject();
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventAdminxChannelOrderReject param) {
        return checkSurveyAndChannel(param.getSurveyId(), param.getChannelId(), entity, (survey, channel) -> {
            // 1 驳回
            // 站内信，样本驳回：您提交的样本订单有最新的处理消息了，点击查看
            // 公众号
            // 短信
            Map<String, Object> params = new HashMap<>();
            params.put("surveyId", survey.getId());
            params.put("surveyTitle", survey.getTitle());
            params.put("channelId", channel.getId());
            params.put("notifyTime", DateHelper.formatDateTime(new Date()));
            params.putAll(getConfig().getParams());
            notify(entity.getOrgId(), channel.getUserId(), getConfig(), params, AppType.surveyplus);
            return ConsumerStatus.SUCCESS;
        });
    }

}
