package cn.hanyi.cem.event.consumer.adminxchannel;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventAdminxChannelOrderRequest;
import cn.hanyi.cem.core.entity.CemEvent;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class AdminxChannelOrderRequestConsumer extends AdminxChannelOrderConsumer<AdminxChannelProperties.AdminxChannelOrderRequest, EventAdminxChannelOrderRequest> {

    @Override
    public List<EventType> types() {
        return List.of(EventType.ADMINX_CHANNEL_ORDER_REQUEST);
    }

    @Override
    protected AdminxChannelProperties.AdminxChannelOrderRequest getConfig() {
        return properties.getRequest();
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventAdminxChannelOrderRequest param) {
        // 订单提交通知
        // >跟进人：${contacts}
        // >问卷ID：${surveyId}
        // >问卷标题：${surveyTitle}
        // >渠道ID：${channelId}
        // >订单总价：${amount}
        // >提交时间：${requestTime}
        // >备注：请在半个小时内处理！[点击查看](${adminxUrl})
        return checkSurveyAndChannel(param.getSurveyId(), param.getChannelId(), entity, (survey, channel) -> {
            Map<String, Object> params = new HashMap<>();
            if ("manual".equals(channel.getOrderPayType())) {
                params.put("amount", "待报价");
            } else if ("standard".equals(channel.getOrderPayType()) && channel.getOrderAmount() != null) {
                return ConsumerStatus.CANCELED;
//                params.put("amount", (channel.getOrderAmount() / 100.00) + "元");
            } else {
                return ConsumerStatus.FAILED;
            }
            params.put("contacts", param.getContacts());
            params.put("surveyId", param.getSurveyId());
            params.put("surveyTitle", survey.getTitle());
            params.put("channelId", param.getChannelId());
            params.put("requestTime", param.getRequestTime());
            sendWeWorkBot(params);
            return ConsumerStatus.SUCCESS;
        });
    }

}
