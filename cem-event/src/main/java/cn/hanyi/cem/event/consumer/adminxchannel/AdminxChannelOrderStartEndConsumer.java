package cn.hanyi.cem.event.consumer.adminxchannel;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventAdminxChannelOrderStartEnd;
import cn.hanyi.cem.core.entity.CemEvent;
import org.befun.auth.constant.AppType;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.auth.service.UserService;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class AdminxChannelOrderStartEndConsumer extends AdminxChannelOrderConsumer<AdminxChannelProperties.AdminxChannelOrderCommon, EventAdminxChannelOrderStartEnd> {

    @Autowired
    private UserService userService;
    @Autowired
    private OrganizationOrderService organizationOrderService;

    @Override
    public List<EventType> types() {
        return List.of(EventType.ADMINX_CHANNEL_ORDER_START_END);
    }

    @Override
    protected AdminxChannelProperties.AdminxChannelOrderCommon getConfig() {
        return properties.getStart();
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventAdminxChannelOrderStartEnd param) {
        return checkSurveyAndChannel(param.getSurveyId(), param.getChannelId(), entity, (survey, channel) -> {
            if (param.isStart()) {
                // 1 开始
                // 站内信，样本开始：您提交的样本订单有最新的处理消息了，点击查看
                // 公众号
                // 短信
                Map<String, Object> params = new HashMap<>();
                params.put("surveyId", survey.getId());
                params.put("surveyTitle", survey.getTitle());
                params.put("channelId", channel.getId());
                params.put("notifyTime", DateHelper.formatDateTime(new Date()));
                params.putAll(properties.getStart().getParams());
                notify(entity.getOrgId(), channel.getUserId(), properties.getStart(), params, AppType.surveyplus);
            } else if (param.isEnd()) {
                // 2 结束
                // 站内信，样本结束：您提交的样本订单有最新的处理消息了，点击查看
                // 公众号
                // 短信
                Map<String, Object> params = new HashMap<>();
                params.put("surveyId", survey.getId());
                params.put("surveyTitle", survey.getTitle());
                params.put("channelId", channel.getId());
                params.put("notifyTime", DateHelper.formatDateTime(new Date()));
                params.putAll(properties.getEnd().getParams());
                notify(entity.getOrgId(), channel.getUserId(), properties.getEnd(), params, AppType.surveyplus);
            }
            return ConsumerStatus.SUCCESS;
        });
    }


}
