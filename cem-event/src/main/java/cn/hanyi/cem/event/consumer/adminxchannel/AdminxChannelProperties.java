package cn.hanyi.cem.event.consumer.adminxchannel;

import cn.hanyi.cem.event.consumer.notifyconfig.InboxMessageConfig;
import cn.hanyi.cem.event.consumer.notifyconfig.NotifySmsConfig;
import cn.hanyi.cem.event.consumer.notifyconfig.NotifyWeChatMpConfig;
import cn.hanyi.cem.event.consumer.notifyconfig.WeWorkBotConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "worker.adminx-channel")
public class AdminxChannelProperties {

    private AdminxChannelOrderRequest request = new AdminxChannelOrderRequest();
    private AdminxChannelOrderPaid paid = new AdminxChannelOrderPaid();
    private AdminxChannelOrderCommon quoted = new AdminxChannelOrderCommon();
    private AdminxChannelOrderCommon reject = new AdminxChannelOrderCommon();
    private AdminxChannelOrderRefundResult refundSuccess = new AdminxChannelOrderRefundResult();
    private AdminxChannelOrderRefundResult refundFailure = new AdminxChannelOrderRefundResult();
    private AdminxChannelOrderCommon start = new AdminxChannelOrderCommon();
    private AdminxChannelOrderCommon end = new AdminxChannelOrderCommon();

    @Getter
    @Setter
    public static class AdminxChannelOrderRequest implements WeWorkBotConfig {
        private String notifyUrl;
        private String adminxUrl;
        private String template;
    }

    @Getter
    @Setter
    public static class AdminxChannelOrderPaid implements WeWorkBotConfig {
        private String contacts;
        private String notifyUrl;
        private String adminxUrl;
        private String template;
    }

    @Getter
    @Setter
    public static class AdminxChannelOrderCommon implements InboxMessageConfig, NotifySmsConfig, NotifyWeChatMpConfig {
        private String smsTemplateName;
        private String wechatMpTemplateName;
        private String inboxMessageTemplateName;
        private Map<String, String> params = new HashMap<>();
    }

    @Getter
    @Setter
    public static class AdminxChannelOrderRefundResult extends AdminxChannelOrderCommon implements WeWorkBotConfig {
        private String notifyUrl;
        private String adminxUrl;
        private String template;
    }
}
