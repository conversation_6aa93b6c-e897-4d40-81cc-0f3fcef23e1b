package cn.hanyi.cem.event.consumer.auth;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResourcePermissionUserChangeDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.properties.JourneyProperties;
import cn.hanyi.ctm.service.JourneyMapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.entity.Organization;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.UserService;
import org.befun.extension.service.MailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.hanyi.cem.core.constant.EventType.RESOURCE_PERMISSION_ADD_USER;
import static cn.hanyi.cem.core.constant.EventType.RESOURCE_PERMISSION_REMOVE_USER;

/**
 * 数据权限变化时，通知对应的用户
 */
@Slf4j
@Component
public class ResourcePermissionChangeUserEventConsumer implements IEventConsumer<EventResourcePermissionUserChangeDto> {

    @Autowired
    private JourneyProperties journeyProperties;
    @Autowired
    private MailService mailService;
    @Autowired
    private UserService userService;
    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private OrganizationService organizationService;

    @Override
    public List<EventType> types() {
        return List.of(RESOURCE_PERMISSION_ADD_USER, RESOURCE_PERMISSION_REMOVE_USER);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventResourcePermissionUserChangeDto param) {
        if (ResourcePermissionType.JOURNEY.name().equalsIgnoreCase(param.getResourceType())) {
            Organization org = organizationService.get(entity.getOrgId());
            SimpleUser from = userService.getSimple(entity.getUserId()).orElse(null);
            JourneyMap journeyMap = journeyMapService.get(param.getResourceId());
            if (org != null && from != null && journeyMap != null) {
                param.getTargetUserIds().forEach(targetUserId -> {
                    userService.getSimple(targetUserId).ifPresent(target -> {
                        if (entity.getType() == RESOURCE_PERMISSION_ADD_USER) {
                            notifyAddUser(org, target, from, journeyMap);
                        } else if (entity.getType() == RESOURCE_PERMISSION_REMOVE_USER) {
                            notifyRemoveUser(target, journeyMap);
                        }
                    });
                });
            }
        }
        return ConsumerStatus.SUCCESS;
    }

    /**
     * add 变量
     * targetTruename
     * orgName
     * fromTrueName
     * journeyName
     * journeyUrl
     */
    public void notifyAddUser(Organization org, SimpleUser target, SimpleUser from, JourneyMap journeyMap) {
        try {
            if (StringUtils.isEmpty(target.getEmail())) {
                log.info("加入{}旅程的邮件发送失败，{}未设置邮箱", journeyMap.getTitle(), target.getTruename());
                return;
            }
            Map<String, Object> params = new HashMap<>();
            params.put("targetTruename", target.getTruename());
            params.put("orgName", org.getName());
            params.put("fromTrueName", from.getTruename());
            params.put("journeyName", journeyMap.getTitle());
            params.put("journeyUrl", journeyProperties.getJourneyUrl() + journeyMap.getId());
            String templateName = journeyProperties.getEmailTemplateAdd();
            mailService.sendMessageByTemplate(templateName, target.getEmail(), params);
            log.info("加入{}旅程的邮件发送成功，email={}", journeyMap.getTitle(), target.getEmail());
        } catch (RuntimeException e) {
            log.info("加入{}旅程的邮件发送失败，email={}", journeyMap.getTitle(), target.getEmail());
            log.error(e.getMessage());
        }
    }

    /**
     * remove 变量
     * targetTruename
     * journeyName
     */
    public void notifyRemoveUser(SimpleUser target, JourneyMap journeyMap) {
        try {
            if (StringUtils.isEmpty(target.getEmail())) {
                log.info("移出{}旅程的邮件发送失败，{}未设置邮箱", journeyMap.getTitle(), target.getTruename());
                return;
            }
            Map<String, Object> params = new HashMap<>();
            params.put("targetTruename", target.getTruename());
            params.put("journeyName", journeyMap.getTitle());
            String templateName = journeyProperties.getEmailTemplateRemove();
            mailService.sendMessageByTemplate(templateName, target.getEmail(), params);
            log.info("移出{}旅程的邮件发送成功，email={}", journeyMap.getTitle(), target.getEmail());
        } catch (RuntimeException e) {
            log.info("移出{}旅程的邮件发送失败，email={}", journeyMap.getTitle(), target.getEmail());
            log.error(e.getMessage());
        }
    }

}
