package cn.hanyi.cem.event.consumer.auth;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventUserDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static cn.hanyi.cem.core.constant.EventType.USER_DELETE;

@Slf4j
@Component
public class UserDeleteEventConsumer implements IEventConsumer<EventUserDto> {

    @Autowired
    private UserService userService;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<EventType> types() {
        return List.of(USER_DELETE);
    }

    @Override
    @Transactional
    public ConsumerStatus consumer(CemEvent entity, EventUserDto param) {
        SimpleUser admin = userService.getAdminUser(param.getOrgId());
        if (admin != null) {
            resetSurveyOwner(param.getOrgId(), param.getUserId(), admin);
            resetSurveyGroupOwner(param.getOrgId(), param.getUserId(), admin);
        }
        return ConsumerStatus.SUCCESS;
    }

    private void resetSurveyOwner(Long orgId, Long userId, SimpleUser admin) {
        String sql = "update survey set user_id=? where org_id=? and user_id=?";
        jdbcTemplate.update(sql, admin.getId(), orgId, userId);
        log.info("重置用户{}的问卷所有权为管理员{}", userId, admin.getId());

    }

    private void resetSurveyGroupOwner(Long orgId, Long userId, SimpleUser admin) {
        String sql = "update survey_group set user_id=? where org_id=? and user_id=?";
        jdbcTemplate.update(sql, admin.getId(), orgId, userId);
        log.info("重置用户{}的问卷文件夹所有权为管理员{}", userId, admin.getId());

    }

}
