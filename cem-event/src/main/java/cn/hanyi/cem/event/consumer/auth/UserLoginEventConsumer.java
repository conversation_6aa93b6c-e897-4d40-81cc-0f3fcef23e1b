package cn.hanyi.cem.event.consumer.auth;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventUserLoginDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.OrganizationStatService;
import org.befun.extension.systemupdate.SystemUpdateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.hanyi.cem.core.constant.EventType.USER_LOGIN;

@Slf4j
@Component
public class UserLoginEventConsumer implements IEventConsumer<EventUserLoginDto> {

    @Autowired
    private SystemUpdateHelper systemUpdateHelper;
    @Autowired
    private OrganizationStatService organizationStatService;

    @Override
    public List<EventType> types() {
        return List.of(USER_LOGIN);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventUserLoginDto param) {
        systemUpdateHelper.userLogin(param.getOrgId(), param.getUserId());
        organizationStatService.updateLastLogin(param.getOrgId(), param.getUserId());
        return ConsumerStatus.SUCCESS;
    }

}
