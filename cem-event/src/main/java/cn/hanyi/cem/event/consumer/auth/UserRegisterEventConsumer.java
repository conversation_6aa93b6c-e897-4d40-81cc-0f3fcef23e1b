package cn.hanyi.cem.event.consumer.auth;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.event.EventUserRegisterDto;
import cn.hanyi.cem.core.dto.task.TaskOrgChangeDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.constant.AppType;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.User;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.RoleService;
import org.befun.auth.service.UserService;
import org.befun.core.utils.JsonHelper;
import org.befun.webservice.CmsProperty;
import org.befun.webservice.dto.CMS.CmsRegisterDto;
import org.befun.webservice.dto.fenxiangxiaoke.RegisterCustomerInfoDto;
import org.befun.webservice.service.CmsRegisterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.xml.datatype.DatatypeConfigurationException;
import java.util.List;
import java.util.Optional;

import static cn.hanyi.cem.core.constant.EventType.USER_REGISTER;

@Slf4j
@Component
public class UserRegisterEventConsumer implements IEventConsumer<EventUserRegisterDto> {

    private static final String TOPIC_USER_REGISTER = "queuing-user-create";
    private static final String TOPIC_USER_REGISTER_SYNC = "queuing-user-create-sync";

    @Autowired
    private CmsRegisterService cmsRegisterService;

    @Autowired
    private CmsProperty cmsProperty;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Override
    public List<EventType> types() {
        return List.of(USER_REGISTER);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventUserRegisterDto param) {
        log.info("user:{} register", param.getUserId());
        try {
            var user = userService.require(param.getUserId());
            var registerCustomerInfoDto =
                    param.getCustomer() == null
                            ? new RegisterCustomerInfoDto()
                            : JsonHelper.toObject(param.getCustomer(), RegisterCustomerInfoDto.class);

//            try {
//                notifyRegisterEvent(user, param, registerCustomerInfoDto);
//            } catch (Exception e) {
//                log.error("notifyRegisterEvent error", e);
//            }

//            try {
//                notifySyncRegisterEvent(user, param);
//            } catch (Exception e) {
//                log.error("notifySyncRegisterEvent error", e);
//            }

            try {
                pushRegisterInfo(user, param, registerCustomerInfoDto);
            } catch (Exception e) {
                log.error("pushRegisterInfo error", e);
            }

            try {
                taskProducerHelper.addTaskFromEvent(entity, TaskType.ORG_CHANGE_NOTIFY, new TaskOrgChangeDto(entity.getOrgId()), null);
            } catch (Exception e) {
                log.error("org change notify error", e);
            }

        } catch (Exception e) {
            log.error("user register error", e);
        }


        return ConsumerStatus.SUCCESS;
    }


    public void pushRegisterInfo(User user, EventUserRegisterDto param, RegisterCustomerInfoDto registerCustomerInfoDto) throws DatatypeConfigurationException {
        AppType appType = parseUserAppType(user);
        String url = null;
        switch (appType) {
            case surveyplus:
                if (cmsProperty.getSurveyplus().getEnable()) {
                    url = cmsProperty.getSurveyplus().getUrl();
                }
                break;
            default:
                if (cmsProperty.getXmplus().getEnable()) {
                    url = cmsProperty.getXmplus().getUrl();
                }
                break;
        }
        log.info("push register info to url: {}", url);
        if (cmsRegisterService != null && url != null) {
            log.info("push register url:{} info to email: {}", url, user.getEmail());
            cmsRegisterService.pushData(CmsRegisterDto.builder()
                    .telephone(user.getMobile())
                    .email(user.getEmail())
                    .name(registerCustomerInfoDto.getName())
                    .company(param.getCompanyName())
                    .ip(registerCustomerInfoDto.getField_ip__c())
                    .country(registerCustomerInfoDto.getField_country__c())
                    .province(registerCustomerInfoDto.getField_province__c())
                    .city(registerCustomerInfoDto.getField_city__c())
                    .build(), url);
        }
    }


    private AppType parseUserAppType(User user) {
        Optional<Organization> organization = organizationService.getById(user.getOrgId());
        if (organization.isPresent()) {
            // 注册的用户只有一个appType
            List<AppType> appType = organizationService.parseOrgAppTypes(organization.get());
            return CollectionUtils.isNotEmpty(appType) ? appType.get(0) : null;
        }
        return null;
    }
}
