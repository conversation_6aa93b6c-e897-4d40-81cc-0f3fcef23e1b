package cn.hanyi.cem.event.consumer.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

/**
 * <AUTHOR>
 * 用于推送所有消息的DTO
 * 需要执行好类型
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class SyncCloudDto extends BaseDTO {

    /**
     * 消息类型
     */
    private SyncCloudType type;

    /**
     * 消息内容
     */
    private Object message;



}
