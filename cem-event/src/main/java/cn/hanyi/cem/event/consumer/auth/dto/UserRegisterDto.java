package cn.hanyi.cem.event.consumer.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.webservice.dto.fenxiangxiaoke.RegisterCustomerInfoDto;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
public class UserRegisterDto extends BaseDTO {

    private String userName;
    private String companyName;
    private String trueName;
    private String password;
    private String email;
    private Long orgId;
    private List<Long> roleId;
    private Long departmentId;
    private List<Long> departmentIds;
    private String mobile;
    private int status;
    private String availableSystems;
    private RegisterCustomerInfoDto customer;


}
