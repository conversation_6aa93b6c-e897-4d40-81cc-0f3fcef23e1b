//package cn.hanyi.cem.event.consumer.auth.asyncsystemupdate;
//
//import lombok.extern.slf4j.Slf4j;
//import org.befun.extension.systemupdate.SystemUpdateOrgJob;
//import org.befun.extension.systemupdate.SystemVersion;
//import org.befun.extension.systemupdate.SystemVersions;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//public class SystemUpdateOrgJob_1_8_7_Test1 implements SystemUpdateOrgJob {
//
//    @Override
//    public SystemVersion systemVersion() {
//        return SystemVersions.V_1_8_7;
//    }
//
//    @Override
//    public void triggerOrgJob(Long aLong) {
//        log.info("开始执行企业信息升级，{}", jobKey());
//    }
//}
