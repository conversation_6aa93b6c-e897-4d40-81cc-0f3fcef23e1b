//package cn.hanyi.cem.event.consumer.auth.asyncsystemupdate;
//
//import lombok.extern.slf4j.Slf4j;
//import org.befun.extension.systemupdate.SystemUpdateUserJob;
//import org.befun.extension.systemupdate.SystemVersion;
//import org.befun.extension.systemupdate.SystemVersions;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//public class SystemUpdateUserJob_1_8_7_Test1 implements SystemUpdateUserJob {
//
//    @Override
//    public SystemVersion systemVersion() {
//        return SystemVersions.V_1_8_7;
//    }
//
//    @Override
//    public void triggerUserJob(Long aLong, Long aLong1) {
//        log.info("开始执行用户信息升级，{}", jobKey());
//    }
//}
