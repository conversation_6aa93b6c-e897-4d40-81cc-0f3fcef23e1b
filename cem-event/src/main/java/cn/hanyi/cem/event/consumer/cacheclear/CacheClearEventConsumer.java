package cn.hanyi.cem.event.consumer.cacheclear;

import cn.hanyi.cem.event.consumer.response.CustomerConsumer;
import cn.hanyi.ctm.service.stat.CacheStatIndicatorService;
import cn.hanyi.survey.client.cache.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public class CacheClearEventConsumer {

    @Autowired
    private SurveyCacheHelper surveyCacheHelper;
    @Autowired
    private ResponseCacheHelper responseCacheHelper;
    @Autowired
    private LimitSurveyCacheHelper limitSurveyCacheHelper;
    @Autowired
    private LimitChannelCacheHelper limitChannelCacheHelper;
    @Autowired
    private LimitMobileCacheHelper limitMobileCacheHelper;
    @Autowired
    private CacheStatIndicatorService cacheStatIndicatorService;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private CustomerConsumer customerConsumer;

    @Getter
    @Setter
    public static class EventResponse {
        public Long id;
        public Long channelId;
        public String clientId;
        public String ip;
        public String openid;
        public Date finishTime;
        public Long customerId;
    }

    private Optional<EventResponse> getResponse(Long responseId) {
        if (responseId != null && responseId > 0) {
            String sql = "select id, channel_id channelId, client_id clientId, ip, openid, finish_time finishTime, c_id customerId from survey_response where id=" + responseId;
            List<EventResponse> list = nativeSqlHelper.queryListObject(sql, EventResponse.class);
            if (CollectionUtils.isNotEmpty(list)) {
                return Optional.of(list.get(0));
            }
        }
        return Optional.empty();
    }

    public void surveyStop(Long surveyId) {
        surveyCacheHelper.stopSurvey(surveyId);
    }

    public void surveyPublish(Long surveyId) {
        surveyCacheHelper.publishSurvey(surveyId);
        limitMobileCacheHelper.removeAllSurveyResponse(surveyId);
    }

    public void surveyDelete(Long surveyId) {
        // 答卷信息等到过期时间了，自动删除
        surveyCacheHelper.deleteSurvey(surveyId);
        cacheStatIndicatorService.clearBySurvey(surveyId);
    }

    public void responseDelete(Long surveyId, Long responseId) {
        getResponse(responseId).ifPresent(response -> {
            // 删除答卷id，答题id
            responseCacheHelper.removeResponse(surveyId, response.clientId);
            // 删除问卷限制clientId，ip
            limitSurveyCacheHelper.removeResponse(surveyId, responseId, response.clientId, response.ip);
            // 删除渠道限制ip，openid
            limitChannelCacheHelper.removeResponse(surveyId, response.channelId, response.ip, response.openid);
            cacheStatIndicatorService.clearByResponse(surveyId, response.finishTime);
            if (Optional.ofNullable(response.customerId).orElse(0L) > 0) {
                customerConsumer.addSyncCustomer(response.customerId);
            }
        });
    }

    public void responseDeleteBySurvey(Long surveyId) {
        // 清空问卷所有答卷
        limitSurveyCacheHelper.removeAllSurveyResponse(surveyId);
        limitChannelCacheHelper.removeAllSurveyResponse(surveyId);
        limitMobileCacheHelper.removeAllSurveyResponse(surveyId);
        responseCacheHelper.removeAllSurveyResponse(surveyId);
        cacheStatIndicatorService.clearBySurvey(surveyId);
    }

    public void responseDeleteByChannel(Long surveyId, Long channelId) {
        // 清空渠道所有答卷，这里要找到所有渠道的答卷很麻烦，所以直接把清空所有答卷
        limitSurveyCacheHelper.removeAllSurveyResponse(surveyId);
        limitMobileCacheHelper.removeAllSurveyResponse(surveyId);
        limitChannelCacheHelper.removeAllChannelResponse(surveyId, channelId);
        responseCacheHelper.removeAllSurveyResponse(surveyId);
        cacheStatIndicatorService.clearBySurvey(surveyId);
    }

    public void responseChangeStatus(Long surveyId, Long responseId, String status) {
        getResponse(responseId).ifPresent(response -> {
            limitSurveyCacheHelper.updateResponseStatus(surveyId, responseId, status);
            cacheStatIndicatorService.clearByResponse(surveyId, response.finishTime);
        });
    }
}
