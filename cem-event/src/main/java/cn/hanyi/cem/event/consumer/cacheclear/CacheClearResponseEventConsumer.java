package cn.hanyi.cem.event.consumer.cacheclear;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.survey.core.constant.ResponseStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CacheClearResponseEventConsumer extends CacheClearEventConsumer implements IEventConsumer<EventResponseDto> {

    @Override
    public List<EventType> types() {
        return List.of(
                EventType.RESPONSE_DELETE,
                EventType.RESPONSE_DELETE_BY_SURVEY,
                EventType.RESPONSE_DELETE_BY_CHANNEL,
                EventType.RESPONSE_INVALID,
                EventType.RESPONSE_RECOVER
        );
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventResponseDto param) {
        if (entity.getType() == EventType.RESPONSE_DELETE) {
            responseDelete(param.getSurveyId(), param.getResponseId());
        } else if (entity.getType() == EventType.RESPONSE_DELETE_BY_SURVEY) {
            responseDeleteBySurvey(param.getSurveyId());
        } else if (entity.getType() == EventType.RESPONSE_DELETE_BY_CHANNEL) {
            responseDeleteByChannel(param.getSurveyId(), param.getChannelId());
        } else if (entity.getType() == EventType.RESPONSE_INVALID) {
            responseChangeStatus(param.getSurveyId(), param.getResponseId(), ResponseStatus.INVALID.name());
        } else if (entity.getType() == EventType.RESPONSE_RECOVER) {
            responseChangeStatus(param.getSurveyId(), param.getResponseId(), ResponseStatus.FINAL_SUBMIT.name());
        }
        return ConsumerStatus.SUCCESS;
    }

}
