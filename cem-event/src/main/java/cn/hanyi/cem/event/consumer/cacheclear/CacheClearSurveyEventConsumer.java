package cn.hanyi.cem.event.consumer.cacheclear;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSurveyDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CacheClearSurveyEventConsumer extends CacheClearEventConsumer implements IEventConsumer<EventSurveyDto> {

    @Override
    public List<EventType> types() {
        return List.of(EventType.SURVEY_DISABLE, EventType.SURVEY_ENABLE, EventType.SURVEY_DELETE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventSurveyDto param) {
        if (entity.getType() == EventType.SURVEY_DELETE) {
            surveyDelete(param.getSurveyId());
        } else if (entity.getType() == EventType.SURVEY_DISABLE) {
            surveyStop(param.getSurveyId());
        } else if (entity.getType() == EventType.SURVEY_ENABLE) {
            surveyPublish(param.getSurveyId());
        }
        return ConsumerStatus.SUCCESS;
    }

}
