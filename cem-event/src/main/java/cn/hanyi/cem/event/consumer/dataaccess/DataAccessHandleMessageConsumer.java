package cn.hanyi.cem.event.consumer.dataaccess;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventDataAccessHandleMessageDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.sendmanage.SendMangeDataAccessHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class DataAccessHandleMessageConsumer implements IEventConsumer<EventDataAccessHandleMessageDto> {

    @Autowired
    private SendMangeDataAccessHelper sendMangeDataAccessHelper;

    @Override
    public List<EventType> types() {
        return List.of(EventType.DATA_ACCESS_HANDLE_MESSAGE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventDataAccessHandleMessageDto param) {
        sendMangeDataAccessHelper.sendByDataAccess(entity.getOrgId(), param.getDataAccessCellId());
        return ConsumerStatus.SUCCESS;
    }

}
