package cn.hanyi.cem.event.consumer.journey;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.utils.ModelUtils;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.ExperienceIndicatorRepository;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.CellTextLabel;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/***
 * 客户旅程体验指标中文本题情感百分比
 */
@Component
@Slf4j
@Order(1000)
public class SentimentConsumer implements IEventConsumer<EventResponseDto> {

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Autowired
    private ExperienceIndicatorRepository experienceIndicatorRepository;

    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;
    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;

    @Autowired
    private ModelUtils modelUtils;

    @Override
    public List<EventType> types() {
        return List.of(EventType.RESPONSE_SUBMIT_FINAL);
    }

    // 需要使用事务来懒加载数据
    @Transactional
    @Override
    public ConsumerStatus consumer(CemEvent event, EventResponseDto dto) {
        log.info("update cell flag: surveyId={}, responseId={}, type={}", dto.getSurveyId(), dto.getResponseId(), event.getType());
        if (!dto.isFinalSubmit()) {
            return ConsumerStatus.CANCELED;
        }

        var survey = surveyBaseEntityService.require(Survey.class, dto.getSurveyId());
        survey.getQuestions().stream().filter(question ->
                question.getType() == QuestionType.TEXT
                        && CollectionUtils.isNotEmpty(experienceIndicatorRepository.findAllBySidAndQid(dto.getSurveyId().toString(), question.getId().toString()))
        ).forEach(question -> {
            surveyResponseCellRepository.findAllBySurveyIdAndResponseId(dto.getSurveyId(), dto.getResponseId())
                    .stream()
                    .filter(cell -> Objects.equals(cell.getQuestionId(), question.getId()) && (cell.getTextLabel() == null || JsonHelper.toObject(cell.getTextLabel(), CellTextLabel.class).getSentiment() == null))
                    .forEach(modelUtils::updateCellSentiment);
        });

        return ConsumerStatus.SUCCESS;
    }


}
