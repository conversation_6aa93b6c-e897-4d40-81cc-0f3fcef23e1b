package cn.hanyi.cem.event.consumer.journey;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventIndicatorCreateDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.utils.ModelUtils;
import cn.hanyi.ctm.repository.ExperienceIndicatorRepository;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.CellTextLabel;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/***
 * 客户旅程体验指标中文本题情感百分比
 */
@Component
@Slf4j
public class UpdateCellTextConsumer implements IEventConsumer<EventIndicatorCreateDto> {

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Autowired
    private ExperienceIndicatorRepository experienceIndicatorRepository;

    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;

    @Autowired
    private ModelUtils modelUtils;

    @Override
    public List<EventType> types() {
        return List.of(EventType.JOURNEY_INDICATOR_CREATE);
    }

    // 需要使用事务来懒加载数据
    @Transactional
    @Override
    public ConsumerStatus consumer(CemEvent event, EventIndicatorCreateDto dto) {
        log.info("journey update cell text: journeyMapId={}, componentId={}, indicatorId={}", dto.getJourneyMapId(), dto.getComponentId(), dto.getIndicatorId());

        experienceIndicatorRepository.findById(dto.getIndicatorId()).ifPresent(indicator -> {
            indicator.getSids().forEach(surveyId -> {
                surveyResponseCellRepository.findAllBySurveyIdAndQuestionIdIn(surveyId, indicator.getQids())
                        .stream().filter(cell -> cell.getType() == QuestionType.TEXT && (cell.getTextLabel() == null || JsonHelper.toObject(cell.getTextLabel(), CellTextLabel.class).getSentiment() == null))
                        .forEach(modelUtils::updateCellSentiment);
            });
        });

        return ConsumerStatus.SUCCESS;
    }


}
