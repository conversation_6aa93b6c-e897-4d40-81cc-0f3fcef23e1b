package cn.hanyi.cem.event.consumer.lottery;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSurveyLotteryDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.survey.core.constant.lottery.LotteryType;
import org.befun.auth.pay.service.order.RedPacketOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SurveyLotteryEventConsumer implements IEventConsumer<EventSurveyLotteryDto> {

    @Autowired
    private RedPacketOrder redPacketOrder;

    @Override
    public List<EventType> types() {
        return List.of(EventType.SURVEY_LOTTERY_CLOSE, EventType.SURVEY_LOTTERY_DELETE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventSurveyLotteryDto param) {
        if (LotteryType.WECHAT.name().equals(param.getLotteryType())) {
            redPacketOrder.cancelOldOrderBySourceId(entity.getOrgId(), param.getLotteryId(), false);
        }
        return ConsumerStatus.SUCCESS;
    }

}
