package cn.hanyi.cem.event.consumer.notifyconfig;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AppType;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.ThirdPartyUser;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.SystemNotificationService;
import org.befun.auth.service.ThirdPartyUserService;
import org.befun.core.utils.RegHelper;
import org.befun.extension.service.InboxMessageService;
import org.befun.extension.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class NotifyConfigBuilder<C> {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ThirdPartyUserService thirdPartyUserService;
    @Autowired
    private InboxMessageService inboxMessageService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private SystemNotificationService systemNotificationService;

    protected abstract C getConfig();

    public <X> void notify(Long orgId, Long userId, X c, Map<String, Object> params, AppType defaultAppType) {
        Map<String, List<NotificationType>> map = new HashMap<>();
        if (c instanceof InboxMessageConfig) {
            InboxMessageConfig config = (InboxMessageConfig) c;
            if (StringUtils.isNotEmpty(config.getInboxMessageTemplateName())) {
                map.computeIfAbsent(config.getInboxMessageTemplateName(), i -> new ArrayList<>()).add(NotificationType.INBOX_MESSAGE);
            }
        }
        if (c instanceof NotifySmsConfig) {
            NotifySmsConfig config = (NotifySmsConfig) c;
            if (StringUtils.isNotEmpty(config.getSmsTemplateName())) {
                map.computeIfAbsent(config.getSmsTemplateName(), i -> new ArrayList<>()).add(NotificationType.SMS);
            }
        }
        if (c instanceof NotifyWeChatMpConfig) {
            NotifyWeChatMpConfig config = (NotifyWeChatMpConfig) c;
            if (StringUtils.isNotEmpty(config.getWechatMpTemplateName())) {
                map.computeIfAbsent(config.getWechatMpTemplateName(), i -> new ArrayList<>()).add(NotificationType.WECHAT);
            }
        }
        String app = getAppByOrg(orgId, userId, defaultAppType);
        map.forEach((k, v) -> {
            systemNotificationService.notifyToUser(app, userId, v.toArray(new NotificationType[0]), k, params);
        });
        if (c instanceof WeWorkBotConfig) {
            WeWorkBotConfig config = (WeWorkBotConfig) c;
            sendWeWorkBot(config, params);
        }
    }

    //******************************************************************************************
    //********************************** inbox message *****************************************
    //******************************************************************************************
    public void sendInboxMessage(Long orgId, Long userId, Map<String, Object> params) {
        sendInboxMessage(orgId, userId, getInboxMessageConfig(), params);
    }

    public void sendInboxMessage(Long orgId, Long userId, InboxMessageConfig config, Map<String, Object> params) {
        if (config == null || orgId == null || userId == null) {
            return;
        }
        try {
            systemNotificationService.notifyToUser(AppType.cem.name(), userId, new NotificationType[]{NotificationType.INBOX_MESSAGE}, config.getInboxMessageTemplateName(), params);
        } catch (Throwable e) {
            log.error("{} notify inbox message error", getClass().getName(), e);
        }
    }

    private InboxMessageConfig getInboxMessageConfig() {
        C c = getConfig();
        if (c instanceof InboxMessageConfig) {
            InboxMessageConfig config = (InboxMessageConfig) c;
            if (StringUtils.isNotEmpty(config.getInboxMessageTemplateName())) {
                return config;
            }
        }
        return null;
    }

    //******************************************************************************************
    //*************************************** sms **********************************************
    //******************************************************************************************
    public void sendSms(Long orgId, Long userId, Map<String, Object> params) {
        sendSms(orgId, userId, getNotifySmsConfig(), params);
    }

    public void sendSms(Long orgId, Long userId, NotifySmsConfig config, Map<String, Object> params) {
        if (config == null || StringUtils.isEmpty(config.getSmsTemplateName())) {
            return;
        }
        try {
            systemNotificationService.notifyToUser(AppType.cem.name(), userId, new NotificationType[]{NotificationType.SMS}, config.getSmsTemplateName(), params);
        } catch (Throwable e) {
            log.error("{} notify inbox message error", getClass().getName(), e);
        }
    }

    public void sendSms(String mobile, Map<String, Object> params) {
        sendSms(getNotifySmsConfig(), mobile, params);
    }

    public void sendSms(NotifySmsConfig config, String mobile, Map<String, Object> params) {
        if (config == null || StringUtils.isEmpty(config.getSmsTemplateName()) || !RegHelper.isMobile(mobile)) {
            return;
        }
        try {
            smsService.sendMessageByTemplate2(config.getSmsTemplateName(), mobile, params);
        } catch (Throwable e) {
            log.error("{} notify sms error", getClass().getName(), e);
        }
    }

    private NotifySmsConfig getNotifySmsConfig() {
        C c = getConfig();
        if (c instanceof NotifySmsConfig) {
            NotifySmsConfig config = (NotifySmsConfig) c;
            if (StringUtils.isNotEmpty(config.getSmsTemplateName())) {
                return config;
            }
        }
        return null;
    }

    //******************************************************************************************
    //************************************ wework bot ******************************************
    //******************************************************************************************
    public void sendWeWorkBot(Map<String, Object> params) {
        sendWeWorkBot(getWeWorkBotConfig(), params);
    }

    public void sendWeWorkBot(WeWorkBotConfig config, Map<String, Object> params) {
        if (config == null) {
            return;
        }
        params.put("adminxUrl", config.getAdminxUrl());
        WeWorkBotMarkdownBody.buildBody(config.getTemplate(), params).send(config.getNotifyUrl());
    }

    private WeWorkBotConfig getWeWorkBotConfig() {
        C c = getConfig();
        if (c instanceof WeWorkBotConfig) {
            WeWorkBotConfig config = (WeWorkBotConfig) c;
            if (StringUtils.isNotEmpty(config.getTemplate()) && StringUtils.isNotEmpty(config.getNotifyUrl())) {
                return config;
            }
        }
        return null;
    }

    //******************************************************************************************
    //************************************ wechat mp *******************************************
    //******************************************************************************************
    public void sendWechatMp(Long orgId, Long userId, Map<String, Object> params) {
        sendWechatMp(orgId, userId, getWeChatMpConfig(), params, AppType.cem);
    }

    public void sendWechatMp(Long orgId, Long userId, Map<String, Object> params, AppType defaultAppType) {
        sendWechatMp(orgId, userId, getWeChatMpConfig(), params, defaultAppType);
    }

    public void sendWechatMp(Long orgId, Long userId, NotifyWeChatMpConfig config, Map<String, Object> params, AppType defaultAppType) {
        if (orgId == null || userId == null || config == null) {
            return;
        }
        String app = getAppByOrg(orgId, userId, defaultAppType);
        if (app != null) {
            try {
                systemNotificationService.notifyToUser(app, userId, new NotificationType[]{NotificationType.WECHAT}, config.getWechatMpTemplateName(), params);
            } catch (Throwable e) {
                log.error("{} notify wechatMp error", getClass().getName(), e);
            }
        }
    }

    private NotifyWeChatMpConfig getWeChatMpConfig() {
        C c = getConfig();
        if (c instanceof NotifyWeChatMpConfig) {
            NotifyWeChatMpConfig config = (NotifyWeChatMpConfig) c;
            if (StringUtils.isNotEmpty(config.getWechatMpTemplateName())) {
                return config;
            }
        }
        return null;
    }

    private String getAppByOrg(long orgId, long userId, AppType defaultAppType) {
        Organization org = organizationService.get(orgId);
        if (org != null) {
            List<AppType> types = organizationService.parseOrgAppTypes(org);
            if (types != null && !types.isEmpty()) {
                if (types.size() == 1) {
                    return types.get(0).name();
                } else {
                    // 优先用 defaultAppType 对应的公众号，并且同时绑定了 微信用户
                    if (defaultAppType != null && types.contains(defaultAppType)) {
                        ThirdPartyUser thirdPartyUser = thirdPartyUserService.getByUserSourceApp(userId, "wechat_mp", defaultAppType.name());
                        if (thirdPartyUser != null) {
                            return defaultAppType.name();
                        }
                    }
                    // 然后在找那个公众号绑定了微信用户，就用哪个
                    for (AppType type : types) {
                        if (type == defaultAppType) {
                            continue;
                        }
                        ThirdPartyUser thirdPartyUser = thirdPartyUserService.getByUserSourceApp(userId, "wechat_mp", type.name());
                        if (thirdPartyUser != null) {
                            return type.name();
                        }
                    }
                }
            }
        }
        return AppType.cem.name();
    }
}
