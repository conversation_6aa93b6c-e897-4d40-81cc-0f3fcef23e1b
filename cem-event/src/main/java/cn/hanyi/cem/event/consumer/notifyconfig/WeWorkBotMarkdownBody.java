package cn.hanyi.cem.event.consumer.notifyconfig;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.JsonHelper;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Getter
@Setter
public class WeWorkBotMarkdownBody {
    private String msgtype = "markdown";
    private Map<String, String> markdown = new HashMap<>();

    public static WeWorkBotMarkdownBody buildBody(String template, Map<String, Object> parameters) {
        String content = TemplateEngine.renderTextTemplate(template, parameters);
        WeWorkBotMarkdownBody notify = new WeWorkBotMarkdownBody();
        notify.markdown.put("content", content);
        return notify;
    }

    public String send(String url) {
        try {
            String body = JsonHelper.toJson(this);
            String response = Request.Post(url).bodyString(body, ContentType.APPLICATION_JSON)
                    .connectTimeout(1000 * 30)
                    .socketTimeout(1000 * 30)
                    .execute()
                    .returnContent()
                    .asString();
            log.info("we work bot send resp:{}", response);
            return response;
        } catch (Exception e) {
            log.info("we work bot send error", e);
        }
        return null;
    }
}