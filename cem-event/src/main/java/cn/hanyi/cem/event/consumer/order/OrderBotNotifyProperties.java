package cn.hanyi.cem.event.consumer.order;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "worker.event.order")
public class OrderBotNotifyProperties {

    private OrderVersionNotify version = new OrderVersionNotify();

    @Setter
    @Getter
    public static class OrderVersionNotify {
        private String notifyUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4443c796-a4fd-46d3-aeaf-629a9d050830";
        private String adminxUrl = "https://adminx.xmplus.cn";
        private String successContent = "\uD83C\uDF89体验家版本购买通知\n" +
                ">企业代码：${orgCode}\n" +
                ">企业名称：${orgName}\n" +
                ">购买版本：${version}(${type})\n" +
                ">实付金额：${amount}元\n" +
                ">版本结束日期：${endDate}\n" +
                ">提交时间：${orderTime}\n" +
                ">备注：订单交易成功";
        private String failureContent = "\uD83D\uDE32体验家版本购买通知\n" +
                ">企业代码：${orgCode}\n" +
                ">企业名称：${orgName}\n" +
                ">购买版本：${version}(${type})\n" +
                ">实付金额：${amount}元\n" +
                ">版本结束日期：${endDate}\n" +
                ">提交时间：${orderTime}\n" +
                ">备注：订单交易异常，版本号需人工变更[点击查看](${adminxUrl})";
    }
}
