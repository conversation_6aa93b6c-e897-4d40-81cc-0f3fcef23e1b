package cn.hanyi.cem.event.consumer.order;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventAdminxChannelOrderPaid;
import cn.hanyi.cem.core.dto.event.EventOrderDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.EventProducerHelper;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.notifyconfig.WeWorkBotMarkdownBody;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.OrgVersionRecordStatus;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.OrganizationVersionRecord;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.RechargeStatus;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.auth.pay.service.order.VersionOrder;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.OrganizationVersionRecordService;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class OrderEventConsumer implements IEventConsumer<EventOrderDto> {

    @Autowired
    private OrganizationOrderService organizationOrderService;
    @Autowired
    private OrderBotNotifyProperties orderBotNotifyProperties;
    @Autowired
    private OrganizationRechargeService organizationRechargeService;
    @Autowired
    private OrganizationVersionRecordService organizationVersionRecordService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private EventProducerHelper eventProducerHelper;
    @Autowired
    private VersionOrder versionOrder;
    @Autowired
    private SurveyChannelRepository surveyChannelRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.ORDER_COMPLETED);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventOrderDto param) {
        OrganizationOrder order = organizationOrderService.get(param.getOrderId());
        if (order != null) {
            if (order.getType() == OrderType.order_sms) {
                // ignore
            } else if (order.getType() == OrderType.order_red_packet) {
                // ignore
            } else if (order.getType() == OrderType.order_adminx_channel) {
                adminxChannelOrderApply(entity.getOrgId(), entity.getUserId(), order);
            } else if (order.getType() == OrderType.order_org_version) {
                versionOrderApply(order);
            }
        }
        return ConsumerStatus.SUCCESS;
    }

    private void adminxChannelOrderApply(Long orgId, Long userId, OrganizationOrder order) {
        if (order.getSourceId() == null || order.getSourceId() <= 0) {
            return;
        }
        SurveyChannel channel = surveyChannelRepository.findById(order.getSourceId()).orElse(null);
        if (channel == null) {
            return;
        }
        eventProducerHelper.addEvent(orgId, userId, EventType.ADMINX_CHANNEL_ORDER_PAID, new EventAdminxChannelOrderPaid(channel.getSid(), channel.getId()));
    }

    private void versionOrderApply(OrganizationOrder order) {
        OrgVersionRecordStatus status;
        try {
            status = versionOrder.callback(order);
        } catch (Throwable e) {
            status = OrgVersionRecordStatus.failure;
            log.error("企业版本购买订单处理失败", e);
        }
        if (status != null) {
            if (status == OrgVersionRecordStatus.success) {
                // 成功通知
                versionOrderBotNotify(true, order);
            } else if (status == OrgVersionRecordStatus.failure) {
                // 失败通知(支付成功)
                RechargeStatus rechargeStatus = order.getRechargeId() == null ? null : organizationRechargeService.rechargeStatus0(order.getRechargeId());
                if (rechargeStatus == RechargeStatus.success) {
                    versionOrderBotNotify(false, order);
                }
            }
        }
    }

    private void versionOrderBotNotify(boolean success, OrganizationOrder order) {
        Organization org = organizationService.get(order.getOrgId());
        OrganizationVersionRecord record = organizationVersionRecordService.get(order.getSourceId());
        OrderBotNotifyProperties.OrderVersionNotify notify = orderBotNotifyProperties.getVersion();
        if (org != null && record != null) {
            Map<String, Object> params = new HashMap<>();
            params.put("orgCode", org.getCode());
            params.put("orgName", org.getName());
            params.put("version", record.getVersion().getLabel());
            params.put("type", "upgrade".equals(record.getType()) ? "升级" : "renew".equals(record.getType()) ? "续费" : "");
            params.put("amount", record.getCostAmount() == null ? "" : record.getCostAmount() / 100.00);
            params.put("orderTime", DateHelper.formatDateTime(order.getCreateTime()));
            params.put("endDate", DateHelper.formatDate(record.getEndDate()));
            params.put("adminxUrl", notify.getAdminxUrl());
            String template = success ? notify.getSuccessContent() : notify.getFailureContent();
            WeWorkBotMarkdownBody.buildBody(template, params).send(notify.getNotifyUrl());
        }
    }

}
