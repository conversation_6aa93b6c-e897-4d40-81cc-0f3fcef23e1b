package cn.hanyi.cem.event.consumer.question;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventQuestionDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.constant.ExperienceInteractionStatus;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.constant.event.EventSurveyStatus;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.ExperienceIndicatorRepository;
import cn.hanyi.ctm.repository.ExperienceInteractionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Slf4j
public class QuestionDeleteEventConsumer implements IEventConsumer<EventQuestionDto> {
    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;

    @Autowired
    private ExperienceIndicatorRepository experienceIndicatorRepository;

    @Autowired
    private ExperienceInteractionRepository experienceInteractionRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.QUESTION_DELETE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent event, EventQuestionDto dto) {
        updateWarningRule(dto);
        updateExperienceIndicator(dto);

        return ConsumerStatus.SUCCESS;
    }

    /**
     * 问题删除后修改预警规则状态
     * @param dto
     */
    private void updateWarningRule(EventQuestionDto dto){
        try{
            eventMonitorRulesRepository.findBySurveyId(dto.getSurveyId())
                    .ifPresent(rules -> rules.forEach(rule -> {
                        rule.setSurveyStatus(EventSurveyStatus.QUESTION);
                        rule.setStatus(EventMonitorStatus.CLOSE);
                        if (rule.getQuestionIds().contains(dto.getQuestionId())) {
                            eventMonitorRulesRepository.save(rule);
                        }
                    }));
        }catch (Exception e){
            log.error("update survey: {} warning rule error: {}", dto.getSurveyId(), e.getMessage());
        }

    }

    /**
     * 问题删除后修改旅程场景状态
     * @param dto
     */
    private void updateExperienceIndicator(EventQuestionDto dto){
        try{
            experienceIndicatorRepository.findAllBySidAndQid(
                    dto.getSurveyId().toString(),
                    dto.getQuestionId().toString()).forEach(experienceIndicator -> {
                experienceIndicator.setIsValid(ExperienceInteractionStatus.UNVALID);
                experienceIndicatorRepository.save(experienceIndicator);
            });

//            experienceInteractionRepository.findAllByInteractionSids(dto.getSurveyId().toString()).forEach(experienceInteraction -> {
//                experienceInteraction.setIsValid(ExperienceInteractionStatus.UNVALID);
//                experienceInteractionRepository.save(experienceInteraction);
//            });
        }catch (Exception e){
            log.error("update survey: {} experience indicator error: {}", dto.getSurveyId(), e.getMessage());
        }

    }
}
