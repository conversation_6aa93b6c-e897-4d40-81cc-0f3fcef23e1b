package cn.hanyi.cem.event.consumer.question;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventQuestionItemDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.constant.event.EventSurveyStatus;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.ExperienceIndicatorRepository;
import cn.hanyi.ctm.repository.ExperienceInteractionRepository;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.repository.SurveyQuestionItemRepository;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;


@Component
@Slf4j
public class QuestionItemDeleteEventConsumer implements IEventConsumer<EventQuestionItemDto> {
    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;

    @Autowired
    private SurveyQuestionItemRepository surveyQuestionItemRepository;

    @Autowired
    private FileService fileService;

    @Override
    public List<EventType> types() {
        return List.of(EventType.QUESTION_ITEM_DELETE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent event, EventQuestionItemDto dto) {
        deleteMediaItemFile(dto);
        updateWarningRule(dto);
        return ConsumerStatus.SUCCESS;
    }

    /**
     * 问题删除后修改预警规则状态
     * @param dto
     */
    private void updateWarningRule(EventQuestionItemDto dto){
        try{
            eventMonitorRulesRepository.findBySurveyId(dto.getSurveyId())
                    .ifPresent(rules -> rules.forEach(rule -> {
                        rule.setSurveyStatus(EventSurveyStatus.ITEM);
                        rule.setStatus(EventMonitorStatus.CLOSE);
                        eventMonitorRulesRepository.save(rule);
                    }));
        }catch (Exception e){
            log.error("update survey: {} warning rule error: {}", dto.getSurveyId(), e.getMessage());
        }

    }

    /**
     * 刪除多媒体选项oss文件
     */
    public void deleteMediaItemFile(EventQuestionItemDto dto){
        try {
            Optional<SurveyQuestionItem> item = surveyQuestionItemRepository.findById(dto.getQuestionId());
            item.ifPresent(x->{
                if(QuestionType.MEDIA == x.getQuestion().getType()){
                    String configure = x.getConfigure();
                    if(StringUtils.isNotEmpty(configure)){
                        Map<String, Object> map = JsonHelper.toMap(configure);
                        if(map.get("path") != null){
                            fileService.delete(map.get("path").toString());
                        }
                    }
                }
            });
        }catch (Exception e){
            log.error("删除多媒体选项oss文件失败：{}", e.getMessage());
        }

    }

}












