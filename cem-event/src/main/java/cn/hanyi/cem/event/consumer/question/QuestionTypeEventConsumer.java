package cn.hanyi.cem.event.consumer.question;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventQuestionTypeDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.constant.ExperienceInteractionStatus;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.ExperienceIndicatorRepository;
import cn.hanyi.ctm.repository.ExperienceInteractionRepository;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Slf4j
public class QuestionTypeEventConsumer implements IEventConsumer<EventQuestionTypeDto> {
    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;

    @Autowired
    private ExperienceIndicatorRepository experienceIndicatorRepository;

    @Autowired
    private ExperienceInteractionRepository experienceInteractionRepository;

    @Autowired
    private SurveyQuestionRepository questionRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.QUESTION_TYPE_UPDATE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent event, EventQuestionTypeDto dto) {
        log.info("survey:{} question:{} update type:{}", dto.getSurveyId(), dto.getQuestionId(), dto.getType());
        if (StringUtils.isNotEmpty(dto.getType())) {
            updateExperienceIndicator(dto);
        }
        return ConsumerStatus.SUCCESS;
    }


    /**
     * 单/多选切换后无效
     *
     * @param dto
     */
    private void updateExperienceIndicator(EventQuestionTypeDto dto) {
        try {
            experienceIndicatorRepository.findAllBySidAndQid(
                    dto.getSurveyId().toString(),
                    dto.getQuestionId().toString()).forEach(experienceIndicator -> {
                experienceIndicator.setIsValid(ExperienceInteractionStatus.UNVALID);
                experienceIndicatorRepository.save(experienceIndicator);
            });

            experienceInteractionRepository.findAllByInteractionSids(dto.getSurveyId().toString()).forEach(experienceInteraction -> {
                experienceInteraction.setIsValid(ExperienceInteractionStatus.UNVALID);
                experienceInteractionRepository.save(experienceInteraction);
            });

        } catch (Exception e) {
            log.error("update survey: {} experience indicator error: {}", dto.getSurveyId(), e.getMessage());
        }

    }
}
