//package cn.hanyi.cem.event.consumer.recharge;
//
//import cn.hanyi.cem.core.constant.ConsumerStatus;
//import cn.hanyi.cem.core.constant.EventType;
//import cn.hanyi.cem.core.dto.event.EventRechargeDto;
//import cn.hanyi.cem.core.entity.CemEvent;
//import cn.hanyi.cem.event.consumer.IEventConsumer;
//import org.befun.auth.pay.service.OrganizationRechargeService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//@Component
//public class RechargeCompletedEventConsumer implements IEventConsumer<EventRechargeDto> {
//
//    @Autowired
//    private OrganizationRechargeService organizationRechargeService;
//
//    @Override
//    public List<EventType> types() {
//        return List.of(EventType.RECHARGE_COMPLETED, EventType.RECHARGE_EXPIRED);
//    }
//
//    @Override
//    public ConsumerStatus consumer(CemEvent entity, EventRechargeDto param) {
//        return null;
//    }
//
//
//}
