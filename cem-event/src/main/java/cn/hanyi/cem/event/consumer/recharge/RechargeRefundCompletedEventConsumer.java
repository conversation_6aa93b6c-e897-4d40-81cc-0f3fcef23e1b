package cn.hanyi.cem.event.consumer.recharge;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventRechargeRefundDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.adminxchannel.AdminxChannelOrderRefundConsumer;
import cn.hanyi.cem.event.consumer.adminxchannel.AdminxChannelProperties;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.entity.OrganizationOrder;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.auth.pay.repository.OrganizationRechargeRefundRepository;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class RechargeRefundCompletedEventConsumer implements IEventConsumer<EventRechargeRefundDto> {

    @Autowired
    protected AdminxChannelProperties adminxChannelProperties;
    @Autowired
    private OrganizationOrderService organizationOrderService;
    @Autowired
    private OrganizationRechargeService organizationRechargeService;
    @Autowired
    private OrganizationRechargeRefundRepository organizationRechargeRefundRepository;
    @Autowired
    private AdminxChannelOrderRefundConsumer adminxChannelOrderRefundConsumer;
    @Autowired
    private SurveyChannelRepository surveyChannelRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.RECHARGE_REFUND_COMPLETED);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventRechargeRefundDto param) {
        // 发送通知
        // 发送短信
        OrganizationRecharge recharge = organizationRechargeService.get(param.getRechargeId());
        if (recharge != null) {
            OrganizationRechargeRefund refund = organizationRechargeRefundRepository.findById(param.getRefundId()).orElse(null);
            if (refund != null) {
                if (recharge.getOrderId() != null && recharge.getOrderId() > 0) {
                    OrganizationOrder order = organizationOrderService.get(recharge.getOrderId());
                    if (order != null) {
                        if (order.getType() == OrderType.order_adminx_channel) {
                            return adminxChannelOrder(entity, order, refund);
                        }
                    }
                }
            }
        }
        return ConsumerStatus.SUCCESS;
    }

    private ConsumerStatus adminxChannelOrder(CemEvent entity, OrganizationOrder order, OrganizationRechargeRefund refund) {
        if (order.getSourceId() != null && order.getSourceId() > 0) {
            SurveyChannel channel = surveyChannelRepository.findById(order.getSourceId()).orElse(null);
            if (channel != null) {
                return adminxChannelOrderRefundConsumer.checkSurveyAndChannel(channel.getSid(), channel.getId(), entity, (survey, _i) -> {
                    if (refund.getStatus() == RechargeRefundStatus.success) {
                        Map<String, Object> refundInfo = JsonHelper.toMap(channel.getOrderRefund());
                        if (refundInfo != null) {
                            refundInfo.put("refundTime", refund.getSuccessTime());
                            channel.setOrderRefund(JsonHelper.toJson(refundInfo));
                            surveyChannelRepository.save(channel);
                        }
                        adminxChannelOrderRefundConsumer.notifyRefundSuccess(entity.getOrgId(), channel.getUserId(), survey, channel, order, null);
                    } else {
                        adminxChannelOrderRefundConsumer.notifyRefundFailure(entity.getOrgId(), channel.getUserId(), survey, channel, order, null);
                    }
                    return ConsumerStatus.SUCCESS;
                });
            }
        }
        return ConsumerStatus.SUCCESS;
    }


}
