package cn.hanyi.cem.event.consumer.recharge;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventRechargeRefundDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.adminxchannel.AdminxChannelOrderRefundConsumer;
import cn.hanyi.cem.event.consumer.adminxchannel.AdminxChannelProperties;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.auth.pay.repository.OrganizationRechargeRefundRepository;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RechargeRefundQueryEventConsumer implements IEventConsumer<EventRechargeRefundDto> {

    @Autowired
    protected AdminxChannelProperties adminxChannelProperties;
    @Autowired
    private OrganizationOrderService organizationOrderService;
    @Autowired
    private OrganizationRechargeService organizationRechargeService;
    @Autowired
    private OrganizationRechargeRefundRepository organizationRechargeRefundRepository;
    @Autowired
    private AdminxChannelOrderRefundConsumer adminxChannelOrderRefundConsumer;
    @Autowired
    private SurveyChannelRepository surveyChannelRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.RECHARGE_REFUND_QUERY);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventRechargeRefundDto param) {
        OrganizationRecharge recharge = organizationRechargeService.get(param.getRechargeId());
        if (recharge != null) {
            OrganizationRechargeRefund refund = organizationRechargeRefundRepository.findById(param.getRefundId()).orElse(null);
            if (refund != null && refund.getStatus() == RechargeRefundStatus.init) {
                organizationRechargeService.refundCallback(recharge, refund);
            }
        }
        return ConsumerStatus.SUCCESS;
    }
}
