package cn.hanyi.cem.event.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.survey.core.dto.message.SurveyResponseMessageDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import lombok.extern.slf4j.Slf4j;
import org.befun.bi.dto.survey.SurveyResponseEventDto;
import org.befun.bi.service.TextService;
import org.befun.core.service.MapperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.List;

/***
 * BI文本分析追加数据
 */
@Component
@Slf4j
public class BITextAnalysisConsumer implements IEventConsumer<EventResponseDto> {

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Autowired
    private TextService textService;

    @Autowired
    private MapperService mapperService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Override
    public List<EventType> types() {
        return List.of(EventType.RESPONSE_SUBMIT_FINAL,EventType.RESPONSE_IMPORT);
    }

    @Override
    public ConsumerStatus consumer(CemEvent event, EventResponseDto dto) {
        log.info("bi_text_analysis event consumer: surveyId={}, responseId={}, type={}",
                dto.getSurveyId(), dto.getResponseId(), event.getType());
        if (!dto.isFinalSubmit()) {
            return ConsumerStatus.CANCELED;
        }

        // 手动创建事务 获取问卷懒加载数据
        // @Transactional 事务会提交失败
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        SurveyResponseEventDto eventDto = null;
        try {
            var survey = surveyBaseEntityService.require(Survey.class, dto.getSurveyId());
            var response = surveyBaseEntityService.require(SurveyResponse.class, dto.getResponseId());
            var cells = response.getCells();

            SurveyResponseMessageDto responseMessageDto = new SurveyResponseMessageDto(survey, response,
                    cells);

            eventDto = mapperService
                    .map(responseMessageDto, SurveyResponseEventDto.class);
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
        }
        textService.appendSurveyData(eventDto);
        return ConsumerStatus.SUCCESS;
    }


}
