package cn.hanyi.cem.event.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.repository.CustomerAnswersRepository;
import cn.hanyi.ctm.repository.CustomerExperienceIndicatorRepository;
import cn.hanyi.ctm.service.CustomerAnswersService;
import cn.hanyi.ctm.service.CustomerHistoryRecordService;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.entity.BaseEntity;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 答题更新客户数据
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CustomerConsumer implements IEventConsumer<EventResponseDto> {

    @Autowired
    private CustomerAnswersRepository customerAnswersRepository;
    @Autowired
    private CustomerAnswersService customerAnswersService;
    @Autowired
    private CustomerHistoryRecordService customerHistoryRecordService;
    @Autowired
    private EventConsumerUtils customerHelper;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private CustomerExperienceIndicatorRepository customerExperienceIndicatorRepository;

    private static final String CUSTOMER_SYNC_IDS = "customer-sync-ids:%s"; // yyyy-MM-dd

    public static String getSyncCustomerKey() {
        return getSyncCustomerKey(LocalDate.now());
    }

    public static String getSyncCustomerKey(LocalDate date) {
        return String.format(CUSTOMER_SYNC_IDS, date);
    }

    public void addSyncCustomer(Long customerId) {
        if (customerId != null && customerId > 0) {
            String key = getSyncCustomerKey();
            stringRedisTemplate.opsForSet().add(key, customerId.toString());
            stringRedisTemplate.expire(key, Duration.ofHours(25));
        }
    }

    @Override
    public List<EventType> types() {
        return List.of(EventType.RESPONSE_SUBMIT_FINAL, EventType.RESPONSE_IMPORT);
    }


    @Transactional
    @Override
    public ConsumerStatus consumer(CemEvent entity, EventResponseDto dto) {
        log.info("push response submit to update customer: surveyId={}, responseId={}, type={}", dto.getSurveyId(), dto.getResponseId(), entity.getType());
        SurveyResponse response = surveyResponseRepository.findById(dto.getResponseId()).orElse(null);
        SimpleSurvey survey;
        Customer customer;
        if (response != null
                && (customer = customerHelper.getCustomerFromResponse(response)) != null
                && (survey = surveyRepository.findSimpleById(dto.getSurveyId())) != null) {
            updateResponseWithCustomer(response, customer);
            addSyncCustomer(customer.getId());
            updateCustomerAnswers(dto.getSurveyId(), dto.getResponseId(), customer.getId(), customer.getUsername(), response.getClientId(),
                    survey.getTitle(), response.getDurationSeconds() == null ? 0 : response.getDurationSeconds());
            if(entity.getType().equals(EventType.RESPONSE_SUBMIT_FINAL)){
                updateCustomerExperienceIndicator(surveyRepository.getOne(survey.getId()), customer.getId(), response);
            }
        }
        return ConsumerStatus.SUCCESS;
    }

    public void updateResponseWithCustomer(SurveyResponse response, Customer customer) {
        if (null == response.getCustomerId()) {
            response.setCustomerId(customer.getId());
            surveyResponseRepository.save(response);
        }

        if (StringUtils.isEmpty(response.getExternalUserId()) && StringUtils.isNotEmpty(customer.getExternalUserId())) {
            response.setExternalUserId(customer.getExternalUserId());
            surveyResponseRepository.save(response);
        }
    }

    public void updateCustomerAnswers(Long surveyId, Long responseId, Long customerId, String customerName, String clientId, String surveyName, int durationInSeconds) {
        if (customerAnswersRepository.countByCustomerIdAndAnswerId(customerId, responseId) == 0) {
            customerAnswersService.updateBySurveyResponse(customerId, surveyId, clientId, responseId, durationInSeconds);
            customerHistoryRecordService.addBySubmitSurvey(customerId, customerName, surveyId, surveyName);
        }
    }

    public void updateCustomerExperienceIndicator(Survey survey, Long customerId, SurveyResponse response) {
        try {
            List<Long> qids = survey.getQuestions().stream().map(BaseEntity::getId).collect(Collectors.toList());
            Collection<SurveyResponseCell> cells = response.getCells();
            customerExperienceIndicatorRepository.findTopOneByCustomerIdAndSurveyIdAndQuestionIdInOrderByCreateTimeDesc(customerId, survey.getId(), qids).ifPresent(customerExperienceIndicator -> {
                Long qId = customerExperienceIndicator.getQuestionId();
                AtomicReference<String> value = new AtomicReference<>(null);
                cells.stream().filter(c -> c.getQuestionId().equals(qId)).findFirst().ifPresent(cell -> {
                    survey.getQuestions().stream().filter(q -> q.getId().equals(qId)).findFirst().ifPresent(question -> {
                        switch (question.getType()) {
                            case MATRIX_SCORE:
                                question.getItems().stream().filter(i -> i.getId().equals(customerExperienceIndicator.getItemId())).findFirst().ifPresent(item -> {
                                    value.set(Objects.toString(JsonHelper.toMap(cell.getValue()).get(item.getValue()), null));
                                });
                                break;
                            case SCORE_EVALUATION:
                                question.getItems().stream().filter(i -> i.getId().equals(customerExperienceIndicator.getItemId())).findFirst().ifPresent(item -> {
                                    value.set(item.getText());
                                });
                                break;
                            default:
                                value.set(Objects.toString(cell.getValue(), null));
                                break;
                        }
                    });
                });

                Optional.ofNullable(value).ifPresent(v -> {
                    customerExperienceIndicatorRepository.save(customerExperienceIndicator);
                });
            });
        } catch (Exception e) {
            log.error("customerExperienceIndicator update error", e);
        }
    }
}
