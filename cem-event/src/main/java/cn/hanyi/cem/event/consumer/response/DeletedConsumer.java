package cn.hanyi.cem.event.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.constant.survey.ResponseStatus;
import cn.hanyi.ctm.repository.EventRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/***
 * 答卷删除需要修改预警事件状态
 */
@Component
@Slf4j
public class DeletedConsumer implements IEventConsumer<EventResponseDto> {

    @Autowired
    private EventRepository eventRepository;


    @Override
    public List<EventType> types() {
        return List.of(EventType.RESPONSE_DELETE);
    }


    @Override
    public ConsumerStatus consumer(CemEvent event, EventResponseDto dto) {
        log.info("deleted survey:{} response:{}", dto.getSurveyId(), dto.getResponseId());
        updateEventStatus(dto);
        return ConsumerStatus.SUCCESS;
    }

    private void updateEventStatus(EventResponseDto dto) {
        eventRepository.findOneBySurveyIdAndResponseId(dto.getSurveyId(), dto.getResponseId()).ifPresent(event -> {
            event.setResponseStatus(ResponseStatus.DELETED);
            eventRepository.save(event);
        });
    }
}
