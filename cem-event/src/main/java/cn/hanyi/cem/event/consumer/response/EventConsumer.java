package cn.hanyi.cem.event.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.dto.task.TaskBotNotifyDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyInboxDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyOutWorkerDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import cn.hanyi.ctm.constant.EventActionStatusType;
import cn.hanyi.ctm.constant.EventActionType;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.constant.event.EventNotifyDelayUnit;
import cn.hanyi.ctm.constant.event.EventSurveyStatus;
import cn.hanyi.ctm.dto.event.EventReceiverDto;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.service.EventActionService;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.exception.AmountNotEnoughException;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationAiPointRecordResponseService;
import org.befun.auth.service.RoleService;
import org.befun.auth.service.UserRoleService;
import org.befun.auth.service.UserService;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.InboxMessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

/**
 * 用于事件预警
 * 收到这个task说明一定产生了预警事件并且允许通知
 * 通过查询事件规则关系表获取通知方式
 */
@Slf4j
@Component
@Order(2000)
public class EventConsumer extends WarningBaseConsumer implements IEventConsumer<EventResponseDto> {

    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;
    @Autowired
    private TaskProducerHelper taskProducerHelper;
    @Autowired
    private WorkerProperties workerProperties;
    @Autowired
    private SurveyResponseRepository responseRepository;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private EventConsumerUtils eventConsumerUtils;
    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private EventActionService actionService;
    @Autowired
    private OrganizationAiPointRecordResponseService organizationAiPointRecordResponseService;

    @Override
    public List<EventType> types() {
        return List.of(EventType.RESPONSE_SUBMIT_FINAL, EventType.RESPONSE_IMPORT);
    }

    @Override
    public ConsumerStatus consumer(CemEvent event, EventResponseDto dto) {
        SurveyResponse response = responseRepository.findById(dto.getResponseId()).orElse(null);
        if (response == null) {
            event.setResponse("response not found, responseId=" + dto.getResponseId());
            return ConsumerStatus.CANCELED;
        }
        if (response.getStatus() != ResponseStatus.FINAL_SUBMIT) {
            event.setResponse("response status not FINAL_SUBMIT, status=" + response.getStatus());
            return ConsumerStatus.CANCELED;
        }

        List<EventMonitorRules> eventMonitorRules = eventMonitorRulesRepository.findBySurveyIdAndStatusAndSurveyStatus(
                dto.getSurveyId(), EventMonitorStatus.OPEN, EventSurveyStatus.NONE).orElse(null);
        if (CollectionUtils.isEmpty(eventMonitorRules)) {
            return ConsumerStatus.CANCELED;
        }
        SimpleUser superAdmin = userService.getAdminUser(event.getOrgId());
        Long userId = superAdmin == null ? 1 : superAdmin.getId();
        Map<Long, Integer> ruleCostMap = eventRuleService.calculateRuleCost(eventMonitorRules);
        HashMap<EventMonitorRules, Set<String>> ruleFalseCauses = new HashMap<>();
        try {
            organizationAiPointRecordResponseService.addByWarningTrigger(event.getOrgId(), userId, dto.getResponseId(), ruleCostMap,
                    () -> buildEvent(eventMonitorRules, dto.getSurveyId(), dto.getResponseId(), dto, ruleFalseCauses)
            );
        } catch (AmountNotEnoughException e) {
            // 余额不足 close rule
            Set<Long> closeIds = eventRuleService.closeRules(ruleCostMap);
            // 去除AI预警
            List<EventMonitorRules> rules = new ArrayList<>();
            Map<Long, Integer> emptyRuleCostMap = new HashMap<>();
            eventMonitorRules.forEach(rule -> {
                if (!closeIds.contains(rule.getId())) {
                    rules.add(rule);
                    emptyRuleCostMap.put(rule.getId(), 0);
                }
            });
            if (!rules.isEmpty()) {
                organizationAiPointRecordResponseService.addByWarningTrigger(event.getOrgId(), userId, dto.getResponseId(), emptyRuleCostMap,
                        () -> buildEvent(rules, dto.getSurveyId(), dto.getResponseId(), dto, ruleFalseCauses)
                );
            }
            log.warn("response {} warning error, ai point not enough stop AI warning {}", response.getId(), JsonHelper.toJson(closeIds));
        }
        return ConsumerStatus.SUCCESS;
    }

    private void buildEvent(List<EventMonitorRules> eventMonitorRuleList,
                            Long surveyId,
                            Long responseId,
                            EventResponseDto dto,
                            HashMap<EventMonitorRules, Set<String>> ruleFalseCauses) {
        Survey survey = surveyRepository.findById(surveyId).orElseThrow(EntityNotFoundException::new);
        SurveyResponse response = responseRepository.findById(responseId).orElseThrow(EntityNotFoundException::new);
        List<EventMonitorRules> hitRulesList = triggerWarning(dto.getSurveyId(), dto.getResponseId(), eventMonitorRuleList, ruleFalseCauses::putAll);
        Event warning = saveEvent(survey, hitRulesList, ruleFalseCauses, eventMonitorRuleList, response, eventConsumerUtils.getCustomerFromResponse(response));
        Set<Long> toGrantUserIds = new HashSet<>();
        if (!hitRulesList.isEmpty()) {
            actionService.addActionSingle(
                    new SimpleUser(-1L, "系统", null, null, null, null),
                    EventActionType.ACTION_TYPE_WARNING,
                    "触发预警规则：" + warning.getWarningTitle().replace(";", "、"),
                    EventActionStatusType.SUCCESS,
                    warning
            );
            warningNotify(hitRulesList, warning, response.getId(), toGrantUserIds);
        }
        // 超管、问卷创建者、预警创建者、接收预警的人
        userRoleService.getByRole(roleService.getSuperAdminByOrg(warning.getOrgId()).getId()).forEach(user -> {
            toGrantUserIds.add(user.getId());
        });
        toGrantUserIds.add(survey.getUserId());
        eventMonitorRuleList.stream().mapToLong(EventMonitorRules::getUserId).forEach(toGrantUserIds::add);
        grantUserEventPermission(warning.getOrgId(), warning.getId(), toGrantUserIds);
    }

    public static Duration getDuration(EventReceiverDto dto) {
        // 解析延时时间 默认无延迟
        if (dto.getDelayUnit() != null && dto.getDelayInterval() != null && dto.getDelayInterval() > 0) {
            if (dto.getDelayUnit() == EventNotifyDelayUnit.MINUTE) {
                return Duration.ofMinutes(dto.getDelayInterval());
            } else if (dto.getDelayUnit() == EventNotifyDelayUnit.HOUR) {
                return Duration.ofHours(dto.getDelayInterval());
            } else if (dto.getDelayUnit() == EventNotifyDelayUnit.DAY) {
                return Duration.ofDays(dto.getDelayInterval());
            } else if (dto.getDelayUnit() == EventNotifyDelayUnit.MONTH) {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime end = now.plusMonths(dto.getDelayInterval());
                return Duration.between(now, end);
            }
        }
        return null;
    }


    /**
     * 生成预警通知
     */
    private void warningNotify(List<EventMonitorRules> rulesList, Event warning, Long responseId, Set<Long> toNotifyUserIds) {

        // rulesList根据修改时间倒序排序
        rulesList.sort(comparing(EventMonitorRules::getCreateTime).reversed());

        //  去重rulesList中的roleId
//        ArrayList<EventReceiverDto> notifyReceivers = rulesList.stream()
//                .filter(EventMonitorRules::getNotifyUser)
//                .map(EventMonitorRules::getReceiver)
//                .flatMap(List::stream)
//                .collect(collectingAndThen(
//                        toCollection(
//                                () -> new TreeSet<>(comparingLong(EventReceiverDto::getRoleIds))
//                        ), ArrayList::new));


        //  去重rulesList中的roleId
        List<EventReceiverDto> notifyReceivers = rulesList.stream()
                .filter(EventMonitorRules::getNotifyUser)
                .map(EventMonitorRules::getReceiver)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 开启通知 同时存在通知用户
        if (CollectionUtils.isNotEmpty(notifyReceivers) && workerProperties.getEvent().getWarning().getEnableNotify()) {
            Set<Long> roleIds = notifyReceivers.stream().map(EventReceiverDto::getRoleIds).flatMap(List::stream).collect(Collectors.toSet());
            Set<Long> userIds = notifyReceivers.stream().map(EventReceiverDto::getUserIds).flatMap(List::stream).collect(Collectors.toSet());
            var users = eventConsumerUtils.getNotifyUsers(warning.getOrgId(), warning.getDepartmentId(), roleIds, userIds);
            Map<EventReceiverDto, Set<Long>> notifyUserIds = new HashMap<>();

            if (null != users) {
                notifyReceivers.forEach(receiver -> {

                    receiver.getRoleIds().stream().map(r -> userRoleService.getByRole(r)).flatMap(List::stream).distinct().forEach(user -> {
                        if (!toNotifyUserIds.contains(user.getId()) && users.stream().anyMatch(u -> u.getId().equals(user.getId()))) {
                            toNotifyUserIds.add(user.getId());
                            notifyUserIds.computeIfAbsent(receiver, k -> new HashSet<>()).add(user.getId());
                        }
                    });

                    receiver.getUserIds().stream().distinct().forEach(userId -> {
                        if (!toNotifyUserIds.contains(userId) && users.stream().anyMatch(u -> u.getId().equals(userId))) {
                            toNotifyUserIds.add(userId);
                            notifyUserIds.computeIfAbsent(receiver, k -> new HashSet<>()).add(userId);
                        }
                    });
                });

                notifyReceivers.forEach(receiver -> {
                    Duration delay = getDuration(receiver);
                    Set<Long> notifyIds = notifyUserIds.get(receiver);
                    if (CollectionUtils.isNotEmpty(notifyIds)) {
                        var targetUrl = StringUtils.isNotEmpty(workerProperties.getEvent().getWarning().getEventUrl()) ? String.format("%s%s", workerProperties.getEvent().getWarning().getEventUrl(), warning.getId()) : "";
                        var inboxDto = eventConsumerUtils.buildBaseParam(TaskNotifyInboxDto.class, warning.getOrgId(), null, null, notifyIds, warning.getId());
                        inboxDto.setResourceType(ResourcePermissionType.EVENT.name());
                        inboxDto.setSourceId(warning.getId());
                        inboxDto.setType(InboxMessageType.WARNING.name());
                        inboxDto.setTargetUrl(targetUrl);
                        inboxDto.setTitle(String.format("%s:%s", warning.getWarningLevel().getText(), warning.getWarningTitle()));

                        CompletableFuture.runAsync(() -> taskProducerHelper.notifyInbox(inboxDto, rulesList.get(0).getOrgId(), rulesList.get(0).getUserId(), responseId, delay));

                        if (CollectionUtils.isNotEmpty(receiver.getNotifyChannel())) {

                            var app = workerProperties.getNotify().getApp();
                            var types = receiver.getNotifyChannel();
                            var template = workerProperties.getNotify().getWarning();
                            var params = eventConsumerUtils.buildNotifyParam(null, warning, targetUrl, null);
                            var outWorker = eventConsumerUtils.buildBaseParam(TaskNotifyOutWorkerDto.class, warning.getOrgId(), null, null, notifyIds, warning.getId());

                            outWorker.setResourceType(ResourcePermissionType.EVENT.name());
                            outWorker.setSourceId(warning.getId());
                            outWorker.setTypes(types.stream().map(Enum::name).collect(Collectors.toList()));
                            outWorker.setApp(app);
                            outWorker.setTemplate(template);
                            outWorker.setParams(params);

                            CompletableFuture.runAsync(() -> taskProducerHelper.notifyOutWork(outWorker, rulesList.get(0).getOrgId(), rulesList.get(0).getUserId(), responseId, delay));
                        }
                    }
                });

                actionLog(warning, roleIds, userIds);
            }
        }


        // bot通知
        // rulesList 肯定不为空
        CompletableFuture.runAsync(() -> taskProducerHelper.botNotify(
                new TaskBotNotifyDto(
                        warning.getOrgId(),
                        rulesList.stream().filter(EventMonitorRules::getNotifyConsumer).map(EventMonitorRules::getId).collect(Collectors.toSet()),
                        responseId,
                        warning.getId()),
                rulesList.get(0).getOrgId(), rulesList.get(0).getUserId(), responseId, null));
    }
}
