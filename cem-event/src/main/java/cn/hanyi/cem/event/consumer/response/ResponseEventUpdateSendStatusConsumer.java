package cn.hanyi.cem.event.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.entity.SendManageRecord;
import cn.hanyi.ctm.repository.SendManageRecordRepository;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.survey.ReceiveStatus;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import cn.hanyi.survey.core.projection.SimpleResponse2;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static cn.hanyi.cem.core.constant.EventType.RESPONSE_CREATE;
import static cn.hanyi.cem.core.constant.EventType.RESPONSE_SUBMIT_FINAL;

@Component
public class ResponseEventUpdateSendStatusConsumer implements IEventConsumer<EventResponseDto> {

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private SendManageRecordRepository sendManageRecordRepository;

    @Override
    public List<EventType> types() {
        return List.of(RESPONSE_CREATE, RESPONSE_SUBMIT_FINAL);
    }

    @Override
    public ConsumerStatus consumer(CemEvent event, EventResponseDto dto) {
        updateStatusByTrackId(event, dto);
        return ConsumerStatus.SUCCESS;
    }

    private void updateStatusByTrackId(CemEvent event, EventResponseDto dto) {
        Long responseId = dto.getResponseId();
        if (responseId != null && responseId > 0) {
            SimpleResponse2 response = surveyResponseRepository.findSimple2ById(responseId);
            if (response != null) {
                String trackId = response.getTrackId();
                if (StringUtils.isNotEmpty(trackId)) {
                    String[] as = trackId.split(":");
                    if (as.length == 2 && "SEND_MANAGE".equals(as[0]) && NumberUtils.isDigits(as[1])) {
                        updateSendManageStatusByTrackId(event, Long.parseLong(as[1]), responseId);
                    }
                }
            }
        }
    }

    private void updateSendManageStatusByTrackId(CemEvent event, Long sendManageRecordId, Long responseId) {
        SendManageRecord sendManageRecord = sendManageRecordRepository.findById(sendManageRecordId).orElse(null);
        if (sendManageRecord != null) {
            if (sendManageRecord.getResponseId() == null) {
                sendManageRecordRepository.updateResponseIdById(responseId, sendManageRecordId);
            }
            SendStatus sendStatus = SendStatus.SEND_SUCCESS;
            ReceiveStatus receiveStatus = ReceiveStatus.SUCCESS;
            if (event.getType() == RESPONSE_CREATE) {
                ReplyStatus replyStatus = ReplyStatus.UN_SUBMIT;
                sendManageRecordRepository.updateSendStatusAndReceiveStatusAndReplyStatusById(sendStatus, receiveStatus, replyStatus, sendManageRecordId);
            } else if (event.getType() == RESPONSE_SUBMIT_FINAL) {
                ReplyStatus replyStatus = ReplyStatus.SUBMIT;
                sendManageRecordRepository.updateSendStatusAndReceiveStatusAndReplyStatusById(sendStatus, receiveStatus, replyStatus, sendManageRecordId);
            }
        }
    }

}
