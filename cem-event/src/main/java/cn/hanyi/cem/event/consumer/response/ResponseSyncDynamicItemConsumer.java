package cn.hanyi.cem.event.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseSyncDynamicItemDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.survey.core.dto.question.DynamicQuestionItemDto;
import cn.hanyi.survey.core.dto.question.DynamicSyncQuestionDto;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.service.QuestionsDynamicItemService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.LockKey;
import org.befun.extension.service.LockRunnableHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class ResponseSyncDynamicItemConsumer implements IEventConsumer<EventResponseSyncDynamicItemDto> {

    @Autowired
    private LockRunnableHelper lockRunnableHelper;
    @Autowired
    private QuestionsDynamicItemService questionsDynamicItemService;
    private static final LockKey LOCK_KEY = new LockKeySyncDynamicItem();

    @Override
    public List<EventType> types() {
        return List.of(EventType.RESPONSE_SYNC_DYNAMIC_ITEM);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventResponseSyncDynamicItemDto param) {
        List<DynamicSyncQuestionDto> questions = JsonHelper.toList(param.getDynamicItems(), DynamicSyncQuestionDto.class);
        if (CollectionUtils.isNotEmpty(questions)) {
            questions.forEach(question -> {
                List<DynamicQuestionItemDto> items = question.getItems();
                if (question.getQuestionId() != null && question.getQuestionId() > 0 && CollectionUtils.isNotEmpty(items)) {
                    items.forEach(item -> addOrUpdateDynamicItem(question.getQuestionId(), item));
                }

            });
        }
        return null;
    }

    private void addOrUpdateDynamicItem(Long questionId, DynamicQuestionItemDto item) {
        if (StringUtils.isNotEmpty(item.getValue()) && StringUtils.isNotEmpty(item.getText())) {
            lockRunnableHelper.run(LOCK_KEY, List.of(questionId, item.getValue()), () -> {
                SurveyQuestionItem find = questionsDynamicItemService.findOneByQuestionAndValue(questionId, item.getValue());
                if (find == null) {
                    questionsDynamicItemService.add(questionId, item.getValue(), item.getText());
                } else if (!item.getText().equals(find.getText())) {
                    questionsDynamicItemService.update(find.getId(), item.getText());
                }
            });
        }
    }

    public static class LockKeySyncDynamicItem implements LockKey {

        @Override
        public String getPlaceholder() {
            return "lock:sync-dynamic-item:%d-%s";
        }

        @Override
        public boolean isFormat() {
            return true;
        }
    }

}
