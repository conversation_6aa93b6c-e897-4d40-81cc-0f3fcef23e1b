package cn.hanyi.cem.event.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;


/***
 * 更新答卷数量
 */
@Component
@Slf4j
public class SurveyUpdateConsumer implements IEventConsumer<EventResponseDto> {

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<EventType> types() {
        return List.of(EventType.RESPONSE_SUBMIT_FINAL);
    }


    @Override
    public ConsumerStatus consumer(CemEvent event, EventResponseDto dto) {
        log.info("update response_finish_num survey:{} response:{}", dto.getSurveyId(), dto.getResponseId());
        updateResponseNum2(dto);
        return ConsumerStatus.SUCCESS;
    }

    private void updateResponseNum(EventResponseDto dto) {
        SurveyResponse response = surveyResponseRepository.findById(dto.getResponseId()).get();
        if(response != null && response.getStatus() == ResponseStatus.FINAL_SUBMIT && response.getCollectorMethod() != SurveyCollectorMethod.SURVEY_PLUS){
            surveyRepository.findById(dto.getSurveyId()).ifPresent(survey -> {
                if (survey.getResponseFinishNum() == null) {
                    long count = surveyResponseRepository.countBySurveyIdAndStatus(survey.getId(), ResponseStatus.FINAL_SUBMIT);
                    survey.setResponseFinishNum((int) count);
                    surveyRepository.save(survey);
                } else {
                    String sql = String.format("update survey set response_finish_num = response_finish_num + (%s) where id = %s", 1, dto.getSurveyId());
                    jdbcTemplate.update(sql);
                }
            });
        }
    }

    private void updateResponseNum2(EventResponseDto dto) {
        SurveyResponse response = surveyResponseRepository.findById(dto.getResponseId()).get();
        if(response != null && response.getStatus() == ResponseStatus.FINAL_SUBMIT && response.getCollectorMethod() != SurveyCollectorMethod.SURVEY_PLUS){
            surveyRepository.findById(dto.getSurveyId()).ifPresent(survey -> {
                long count = surveyResponseRepository.countBySurveyIdAndStatus(survey.getId(), ResponseStatus.FINAL_SUBMIT);
                String sql = String.format("update survey set response_finish_num =%s where id = %s", count, survey.getId());
                jdbcTemplate.update(sql);
            });
        }
    }
}
