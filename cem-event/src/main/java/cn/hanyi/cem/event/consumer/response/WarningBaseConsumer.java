package cn.hanyi.cem.event.consumer.response;

import cn.hanyi.cem.core.utils.SignGenerator;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import cn.hanyi.cem.event.consumer.utils.ModelUtils;
import cn.hanyi.ctm.constant.EventActionStatusType;
import cn.hanyi.ctm.constant.EventActionType;
import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.constant.survey.QuestionType;
import cn.hanyi.ctm.dto.event.EventWarningDto;
import cn.hanyi.ctm.dto.survey.SurveyResponseCellMessageRuleDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.ctm.entity.EventRuleRelation;
import cn.hanyi.ctm.repository.CustomerRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.repository.EventRuleRelationRepository;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import cn.hanyi.ctm.service.EventActionService;
import cn.hanyi.ctm.service.EventRuleService;
import cn.hanyi.ctm.service.EventThesaurusService;
import cn.hanyi.ctm.utils.RegularExpressionUtils;
import cn.hanyi.survey.core.dto.message.QuestionsItemDto;
import cn.hanyi.survey.core.dto.message.SurveyResponseCellMessageDto;
import cn.hanyi.survey.core.dto.message.SurveyResponseMessageDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.service.expression.ExpressionService;
import cn.hanyi.survey.service.QuestionsDynamicItemService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.entity.Department;
import org.befun.auth.entity.Role;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.RoleService;
import org.befun.auth.service.UserRoleService;
import org.befun.auth.service.UserService;
import org.befun.bi.entity.TextTopic;
import org.befun.bi.repository.TextTopicRepository;
import org.befun.core.entity.BaseEntity;
import org.befun.core.utils.JsonHelper;
import org.befun.nlp.core.constant.Industry;
import org.befun.nlp.core.constant.SentimentLabel;
import org.befun.nlp.core.dto.TextClassificationLabelDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hanyi.ctm.constant.survey.QuestionType.*;
import static java.util.Comparator.comparing;

@Component
@Slf4j
public abstract class WarningBaseConsumer {

    @Autowired
    private ExpressionService expressionService;

    @Autowired
    private EventThesaurusService eventThesaurusService;

    @Autowired
    private ModelUtils modelUtils;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private ConnectorConsumerService connectorConsumerService;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private EventRuleRelationRepository eventRuleRelationRepository;

    @Autowired
    private EventConsumerUtils eventConsumerUtils;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private EventActionService actionService;
    @Autowired
    private UserService userService;

    @Autowired
    private TextTopicRepository textTopicRepository;

    @Autowired
    private SurveyQuestionRepository surveyQuestionRepository;

    @Autowired
    private QuestionsDynamicItemService questionsDynamicItemService;

    private List<QuestionType> questionsDynamic = List.of(SINGLE_CHOICE, MULTIPLE_CHOICES, DROP_DOWN);

    @Autowired
    private EntityManager entityManager;
    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;
    @Autowired
    protected EventRuleService eventRuleService;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private CustomerRepository customerRepository;

    /**
     * 命中预警的规则
     *
     * @param surveyId
     * @param responseId
     * @param eventMonitorRules
     * @return
     */
    @Transactional
    public List<EventMonitorRules> triggerWarning(Long surveyId, Long responseId, List<EventMonitorRules> eventMonitorRules, Consumer<HashMap<EventMonitorRules, Set<String>>> consumer) {

        List<EventMonitorRules> triggeredRules = new ArrayList<>();
        HashMap<EventMonitorRules, Set<String>> ruleFalseCauses = new HashMap<>();
        HashMap configContent = buildConfigContent(eventMonitorRules, responseId);
        configContent.putAll(buildCustomerContent(responseId));
        Objects.requireNonNull(eventMonitorRules).forEach(rule -> {
            try {

                AtomicReference<Set<String>> falseCauses = new AtomicReference<>(new HashSet<>());
                Boolean triggerRule = expressionService.triggerExpression(responseId, rule.getExpression(), configContent, false, falseCauses::set);

                if (triggerRule) {
                    ruleFalseCauses.put(rule, falseCauses.get());
                    triggeredRules.add(rule);
                }
            } catch (Exception ex) {
                log.error(
                        "rule {} triggerRule surveyId {} with responseId {} error: {}",
                        rule.getId(),
                        surveyId,
                        responseId,
                        ex.getMessage()
                );
                ex.printStackTrace();
            }
        });
        consumer.accept(ruleFalseCauses);
        return triggeredRules;
    }

    private HashMap buildConfigContent(List<EventMonitorRules> eventMonitorRules, Long responseId) {
        Map<String, Object> thesaurusContent = eventThesaurusService.buildThesaurusContent(eventMonitorRules);
        Map<String, Object> modelContent = buildModelContentDict(eventMonitorRules, responseId);

        HashMap<String, Object> dictionaries = new HashMap<>();

        if (!thesaurusContent.isEmpty()) {
            dictionaries.putAll((Map) ((Map) thesaurusContent.getOrDefault("config", new HashMap<>())).get("dictionaries"));
        }

        if (!modelContent.isEmpty()) {
            dictionaries.putAll((Map) ((Map) modelContent.getOrDefault("config", new HashMap<>())).get("dictionaries"));
        }

        return dictionaries.isEmpty() ? dictionaries : new HashMap<>() {
            {
                put("config", new HashMap<>() {{
                    put("dictionaries", dictionaries);
                }});
            }
        };
    }

    private HashMap buildCustomerContent(Long responseId) {
        HashMap content = new HashMap();

        surveyResponseRepository.findById(responseId).flatMap(surveyResponse -> Optional.ofNullable(surveyResponse.getCustomerId())).ifPresent(customerId -> {
            Customer customer = customerRepository.findById(customerId).orElse(null);
            if (customer != null) {
                content.put("customerId", customer.getId());
                content.put("customerName", customer.getUsername());
                content.put("customerGender", customer.getGender());
                content.put("departmentName", customer.getDepartmentNames());
                content.put("departmentId", customer.getDepartmentId());
            }
        });

        return content;
    }
    /**
     * 构建情感文本config
     * {
     * "config": {
     * "dictionaries":{
     * new BigInteger(1, md5.getBytes("好")): ["POSITIVE"],
     * new BigInteger(1, md5.getBytes("差")): ["NEGATIVE"]
     * }
     * }
     * }
     *
     * @param rules
     * @param responseId
     * @return
     */
    private HashMap buildModelContentDict(List<EventMonitorRules> rules, Long responseId) {
        var modelContentConfig = new HashMap<>();
        var sentimentDict = new HashMap<String, List<String>>();
        var classificationDict = new HashMap<String, List<String>>();

        var content = expressionService.getContent(responseId,null, false);

        rules.forEach(rule -> {
            ArrayList<String[]> sentimentIdLabel = RegularExpressionUtils.getSentimentIdLabel(rule.getExpression());
            ArrayList<String[]> topicIdLabel = RegularExpressionUtils.getTopicId(rule.getExpression());

            sentimentIdLabel.forEach(nameLabel -> {
                String qName = nameLabel[0];
                Map valueMap = (Map) content.getOrDefault(qName, new HashMap<>());
                String valueKey = "commentSentiment".equals(nameLabel[2]) ? "comment" : "value";
                Object value = valueMap.getOrDefault(valueKey, null);
                String text = value == null ? null : value.toString();

                if (StringUtils.isNotEmpty(text)) {
                    // 目前行业暂时用零售的 后期再扩展
                    SentimentLabel predictLabel = modelUtils.predictSentimentLabel(text, Industry.FOOD);
                    Optional.ofNullable(predictLabel).ifPresent(label -> {
                        try {
                            sentimentDict.put(SignGenerator.md5(text), new ArrayList<>() {{
                                add(label.name());
                            }});
                        } catch (Exception e) {
                            log.error("buildModelContentDict error: {}", e.getMessage());
                        }
                    });
                }
            });

            for (String[] nameLabel : topicIdLabel) {
                String qName = nameLabel[0];
                Long topicId = Long.valueOf(nameLabel[3]);
                String pick = nameLabel[1];

                Optional<TextTopic> textTopic = textTopicRepository.findById(topicId);
                Map valueMap = (Map) content.getOrDefault(qName, new HashMap<>());
                String valueKey = "commentTopicIn".equals(nameLabel[2]) ? "comment" : "value";
                Object value = valueMap.getOrDefault(valueKey, null);
                String text = value == null ? null : value.toString();

                if (StringUtils.isNotEmpty(text) && textTopic.isPresent() && StringUtils.isNotEmpty(textTopic.get().getContent())) {
                    List<String> additionalText = JsonHelper.toList(textTopic.get().getContent(), String.class);
                    List<TextClassificationLabelDto> classificationLabel = modelUtils.predictClassificationLabel(text, Industry.FOOD, additionalText);
                    Optional.ofNullable(classificationLabel).ifPresent(label -> {
                        try {
                            classificationDict.put(
                                    SignGenerator.md5(textTopic.get().getId() + text),
                                    label.stream().map(TextClassificationLabelDto::getLabel).collect(Collectors.toList())
                            );
                        } catch (Exception e) {
                            log.error("buildModelContentDict error: {}", e.getMessage());
                        }
                    });
                }
            }
        });


        if (!sentimentDict.isEmpty()) {
            modelContentConfig.put("config", new HashMap<>() {{
                put("dictionaries", sentimentDict);
            }});
        }
        if (!classificationDict.isEmpty()) {

            // modelContentConfig追加classificationDict
            if (modelContentConfig.containsKey("config")) {
                Map config = (Map) modelContentConfig.get("config");
                Map dictionaries = (Map) config.get("dictionaries");
                dictionaries.putAll(classificationDict);
            } else {
                modelContentConfig.put("config", new HashMap<>() {{
                    put("dictionaries", classificationDict);
                }});
            }

        }

        return modelContentConfig;
    }

    /**
     * 保存预警事件
     *
     * @param hitRules
     * @param rules
     * @param response
     * @param customer
     * @return
     */
    @SneakyThrows
    @Transactional
    public Event saveEvent(Survey survey, List<EventMonitorRules> hitRules, HashMap<EventMonitorRules, Set<String>> ruleFalseCauses, List<EventMonitorRules> rules, SurveyResponse response, Customer customer) {
        // 一次答题触发多个规则生成一次事件
        SurveyResponseMessageDto surveyResponseMessageDto = new SurveyResponseMessageDto(survey, response, response.getCells());

        Map<Long, String> qIdNameMap = survey.getQuestions().stream()
                .filter(q -> hitRules.stream().anyMatch(r -> r.getQuestionIds().contains(q.getId())))
                .collect(Collectors.toMap(SurveyQuestion::getId, SurveyQuestion::getName));


        List<String> source = new ArrayList<>();
        Event event = new Event();
        List<EventWarningType> eventWarningTypeList = new ArrayList<>();
        List<EventWarningDto> warningDto = new ArrayList<>();
        List<String> warningTitle = new ArrayList<>();

        // 默认最低等级
        eventWarningTypeList.add(EventWarningType.NONE);

        List<SurveyResponseCellMessageDto> questions = surveyResponseMessageDto.getData();

        Department department = null;
        if (response.getDepartmentId() != null) {
            department = departmentService.get(response.getDepartmentId());
        }
        if (department == null && StringUtils.isNotEmpty(response.getDepartmentCode())) {
            department = departmentService.getByCodeOrRoot(response.getOrgId(), response.getDepartmentCode());
        }
        if (department == null) {
            department = departmentService.getRoot(response.getOrgId());
        }
        if (department != null) {
            event.setDepartmentId(department.getId());
            do {
                source.add(0, department.getTitle());
                if (department.getPid() != null && department.getPid() > 0) {
                    department = departmentService.get(department.getPid());
                } else {
                    department = null;
                }
            } while (department != null);
        }

        var questionsRules = questions.stream().map(q -> {
            SurveyResponseCellMessageRuleDto qc = JsonHelper.getObjectMapper().convertValue(q, SurveyResponseCellMessageRuleDto.class);
            if (qc.getType() != null && questionsDynamic.stream().anyMatch(d -> d.name().equals(qc.getType().name()))) {
                List<SurveyQuestionItem> it = questionsDynamicItemService.findAllByQuestion(q.getQuestionId());
                if (CollectionUtils.isNotEmpty(it)) {
                    qc.getQuestionsItems().values().forEach(i -> {
                        i.setItems(it.stream().map(x -> new QuestionsItemDto(x.getValue(), x.getText())).collect(Collectors.toList()));
                    });
                }
            }


            if (qc.getType() != null && MATRIX_SLIDER.name().equals(qc.getType().name())) {
                surveyQuestionRepository.findById(q.getQuestionId()).ifPresent(surveyQuestion -> {
                    if (surveyQuestion.getEnableTotal()) {
                        Optional.ofNullable(q.getValue()).ifPresent(value -> {
                            Integer total = ((Map) value).values().stream().mapToInt(i -> Integer.parseInt(i.toString())).sum();
                            qc.setTotal(total.toString());
                        });
                    }
                    qc.setUnit(surveyQuestion.getPlaceHolder());
                });
            }
            return qc;
        }).collect(Collectors.toList());

        // 需要保存所有设置了规则的事件
        // 高中低等级排序
        Objects.requireNonNull(rules.stream().sorted(comparing(EventMonitorRules::getLevel).reversed())).forEach(rule -> {

            // 命中了规则的事件
            if (hitRules.contains(rule)) {
                entityManager.detach(rule);
                eventWarningTypeList.add(rule.getLevel());
                warningTitle.add(rule.getTitle());
                event.getWarnings().add(new EventWarningDto(response.getId(), rule.getId()));
                event.setStatus(EventStatusType.WAIT);
                questionsRules.stream().filter(question -> {
                    String name = qIdNameMap.get(question.getQuestionId());
                    Set<String> qNames = ruleFalseCauses.get(rule);
                    return CollectionUtils.isNotEmpty(qNames) && qNames.contains(name);
                }).forEach(question -> {
                    question.setEventMonitorRules(rule);
                });
            }
        });
        if (event.getWarnings().isEmpty()) {
            // 没有命中预警记录responseId, ruleId为0
            event.getWarnings().add(new EventWarningDto(response.getId(), 0L));
        }

        String pushIds = connectorConsumerService.consumers(rules.stream().map(BaseEntity::getId).collect(Collectors.toSet())).stream().map(c -> c.getId().toString()).collect(Collectors.joining(","));

        event.setResponseId(response.getId());
        event.setParameters((HashMap) response.getParameters());
        event.setExternalUserId(Optional.ofNullable(customer).isPresent() ? customer.getExternalUserId() : null);
        event.setCustomerId(Optional.ofNullable(customer).isPresent() ? customer.getId() : null);
        event.setSurveyId(response.getSurveyId());
        event.setSurveyName(survey.getTitle());
        event.setSurveyVersion(null);
        event.setDepartmentName(response.getDepartmentName());
        event.setCustomerName(Optional.ofNullable(customer).isPresent() ? customer.getUsername() : null);
        event.setCustomerGender(Optional.ofNullable(customer).isPresent() ? customer.getGender() : null);
        event.setDepartmentCode(response.getDepartmentCode());
        event.setExternalCompanyId(response.getExternalCompanyId());
        event.setDefaultPa(Objects.toString(response.getDefaultPa(), null));
        event.setDefaultPb(Objects.toString(response.getDefaultPb(), null));
        event.setDefaultPc(Objects.toString(response.getDefaultPc(), null));
        // 存放命中等级最高的
        event.setWarningLevel(eventWarningTypeList.stream().max(comparing(EventWarningType::getOrder)).get());
        event.setWarningTitle(warningTitle.stream().collect(Collectors.joining(";")));
        event.setOrgId(response.getOrgId());
        event.setSource(source.stream().distinct().collect(Collectors.joining("/")));
        event.setQuestions(questionsRules);
        event.setResponseTime(response.getFinishTime());
        event.setPushIds("".equals(pushIds) ? null : pushIds);
        String tags = hitRules.stream().filter(e -> !e.getTags().isEmpty()).map(e -> String.join("/", e.getTags())).collect(Collectors.joining("/"));
        event.setTags(tags);

        Event savedEvent = eventRepository.save(event);
        hitRules.forEach(rule -> addEventRelation(savedEvent.getId(), response.getId(), rule.getId()));
        return savedEvent;

    }

    /**
     * 添加事件关系
     */
    private void addEventRelation(Long eventId, Long responseId, Long ruleId) {
        try {
            var eventRuleRelation = new EventRuleRelation();
            eventRuleRelation.setEventId(eventId);
            eventRuleRelation.setResponseId(responseId);
            eventRuleRelation.setRuleId(ruleId);
            eventRuleRelationRepository.save(eventRuleRelation);
        } catch (Exception ex) {
            log.error("add event: {} relation error: {}", eventId, ex.getMessage());
        }
    }


    public void grantUserEventPermission(Long orgId, Long eventId, Set<Long> userIds) {
        userIds.forEach(userId -> {
            log.info("grant user {} event {} permission", userId, eventId);
            eventConsumerUtils.grantResourcePermission(orgId, eventId, userId, ResourcePermissionType.EVENT);
        });
    }


    public void actionLog(Event warning, Set<Long> roleIds, Set<Long> userIds) {
        List<SimpleUser> users = userService.getSimpleByIds(userIds);
        List<Role> roles = roleService.getByIds(new ArrayList<>(roleIds));

        String userNames = CollectionUtils.isEmpty(users) ? null : users.stream().map(SimpleUser::getTruename).collect(Collectors.joining("、"));
        String roleNames = CollectionUtils.isEmpty(roles) ? null : roles.stream().distinct().map(Role::getName).collect(Collectors.joining("、"));
        String comma = StringUtils.isNotEmpty(userNames) && StringUtils.isNotEmpty(roleNames) ? "，" : "";

        String content = (StringUtils.isNotEmpty(userNames) ? String.format("已通知成员：%s%s", userNames, comma) : "") +
                (StringUtils.isNotEmpty(roleNames) ? String.format("已通知角色：%s", roleNames) : "");
        actionService.addActionSingle(
                new SimpleUser(null, "系统", null, null, null, null),
                EventActionType.ACTION_TYPE_NOTICE,
                content,
                EventActionStatusType.SUCCESS,
                warning
        );
    }

}
