package cn.hanyi.cem.event.consumer.response.webhook;


import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import cn.hanyi.ctm.constant.ConnectorParamsType;
import cn.hanyi.ctm.constant.PushStatus;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import cn.hanyi.ctm.constant.connector.ConnectorPushType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.connector.ConnectorParamsDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ConnectorConsumer;
import cn.hanyi.ctm.entity.Push;
import cn.hanyi.ctm.repository.PushRepository;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public abstract class BaseWebhookConsumer {

    @Autowired
    private ConnectorConsumerService connectorConsumerService;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Autowired
    private PushRepository pushRepository;

    @Autowired
    private EventConsumerUtils eventConsumerUtils;


    public List<Connector> getConnector(Set<Long> relationIds, ConnectorPushType pushType, ConnectorPushCondition pushCondition) {
        return connectorConsumerService.webhookConnector(
                relationIds,
                pushType,
                pushCondition
        );
    }

    public void createPush(CemEvent event, Map<String, Object> content, List<Connector> connectors, Long relationId) {
        log.info("createPush: cem-event={} connectors={}", event.getId(), connectors.size());
        List<Push> pushList = new ArrayList<>();
        connectors.stream().filter(c -> ConnectorProviderType.WEBHOOK.equals(c.getProviderType())).forEach(connector -> {
            ConnectorConsumer connectorConsumer = connectorConsumerService.getConsumerByConnectorAndRelationId(connector, relationId);
            if (connectorConsumer == null) {
                return;
            }
            List<ConnectorParamsDto> params = connectorConsumer.getParams();
            Map<String, Object> parameters = new HashMap<>();
            Object parametersObj = content.get("parameters");
            if (parametersObj instanceof Map<?, ?>) {
                ((Map<?, ?>) parametersObj).forEach((key, value) -> {
                    if (key != null) {
                        parameters.put(key.toString(), value);
                    }
                });
            }
            List<ConnectorParamsDto> standardParams = connector.getParams();
            if (CollectionUtils.isNotEmpty(standardParams) && CollectionUtils.isNotEmpty(params)) {
                Set<String> standardParamNames = standardParams.stream().map(ConnectorParamsDto::getName).collect(Collectors.toSet());
                params.forEach(param -> {
                    if (standardParamNames.contains(param.getName())) {
                        if (ConnectorParamsType.Number.equals(param.getType())) {
                            parameters.put(param.getName(), param.getValue());
                        } else {
                            parameters.put(param.getName(), String.valueOf(param.getValue()));
                        }
                    }
                });
            }

            if (!parameters.isEmpty()) {
                content.put("parameters", parameters);
            }

            var url = connector.getGateway();
            Push push = new Push();
            push.setOrgId(event.getOrgId());
            push.setName(connector.getName());
            push.setConnector(connector);
            push.setType(ConnectorType.WEBHOOK);
            push.setAddress(url);
            push.setContent(JsonHelper.toJson(content));
            push.setStatus(PushStatus.FAILED);
            pushRepository.save(push);
            pushList.add(push);
        });
        log.info("createPush: cem-event={} pushList={}", event.getId(), pushList.size());
        pushList.forEach(push -> {
            taskProducerHelper.webhookCreateFromEvent(event, push.getId());
        });
    }

    public <C> C buildResponseDetailDto(Survey survey, SurveyResponse response, Class<C> clazz) {
        return eventConsumerUtils.buildResponseDetailDto(survey, response, clazz);
    }
}
