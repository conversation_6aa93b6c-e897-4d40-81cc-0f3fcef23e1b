package cn.hanyi.cem.event.consumer.response.webhook;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import cn.hanyi.ctm.constant.connector.ConnectorPushType;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.task.ResponseDetailDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Component
@Slf4j
public class SubmitWebhookConsumer extends BaseWebhookConsumer implements IEventConsumer<EventResponseDto> {

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Override
    public List<EventType> types() {
        return List.of(EventType.RESPONSE_SUBMIT, EventType.RESPONSE_SUBMIT_FINAL);
    }

    // 需要使用事务来懒加载数据
    @Transactional
    @Override
    public ConsumerStatus consumer(CemEvent event, EventResponseDto dto) {
        log.info("push response submit: surveyId={}, responseId={}, type={}", dto.getSurveyId(), dto.getResponseId(), event.getType());
        var survey = surveyBaseEntityService.require(Survey.class, dto.getSurveyId());
        var response = surveyBaseEntityService.require(SurveyResponse.class, dto.getResponseId());

        List<Connector> connectors = getConnector(Stream.of(survey.getId()).collect(Collectors.toSet()), ConnectorPushType.RESPONSE, ConnectorPushCondition.NEXT);
        if(connectors.size() > 0){
            ResponseDetailDto sendData = buildResponseDetailDto(survey, response, ResponseDetailDto.class);
            sendData.getData().forEach(data ->
                    data.setType(String.valueOf(QuestionType.valueOf(data.getType()).getText()))
            );
            Map<String, Object> pushBody = JsonHelper.toMap(sendData);
            createPush(event, pushBody, connectors, survey.getId());
        }
        return ConsumerStatus.SUCCESS;
    }
}
