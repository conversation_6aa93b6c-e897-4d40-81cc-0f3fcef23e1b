package cn.hanyi.cem.event.consumer.response.webhook;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventWarningChangeStatusDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.constant.EventActionStatusType;
import cn.hanyi.ctm.constant.EventActionType;
import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import cn.hanyi.ctm.constant.connector.ConnectorPushType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.task.DataWarningDto;
import cn.hanyi.ctm.dto.task.DataWarningRuleDto;
import cn.hanyi.ctm.dto.task.ResponseTaskDetailDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventAction;
import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.repository.EventRuleRelationRepository;
import cn.hanyi.ctm.repository.PushRepository;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import cn.hanyi.ctm.service.EventActionService;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hanyi.cem.core.constant.EventType.WARNING_CHANGE_STATUS;


@Component
@Slf4j
public class WarningChangeStatusWebhookConsumer extends BaseWebhookConsumer implements IEventConsumer<EventWarningChangeStatusDto> {


    @Autowired
    private ConnectorConsumerService connectorConsumerService;
    @Autowired
    private PushRepository pushRepository;
    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Autowired
    private EventRuleRelationRepository eventRuleRelationRepository;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private EventMonitorRulesRepository eventRulesRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private EventActionService actionService;

    @Autowired
    private EntityManager entityManager;

    @Override
    public List<EventType> types() {
        return List.of(WARNING_CHANGE_STATUS);
    }

    // 需要使用事务来懒加载数据
    @Transactional
    @Override
    public ConsumerStatus consumer(CemEvent event, EventWarningChangeStatusDto dto) {
        Event warning = eventRepository.findById(dto.getWarningId()).orElse(null);
        if (warning == null) {
            log.warn("warning change status cancel: id={}, userId={}, event not found", dto.getWarningId(), dto.getUserId());
            return ConsumerStatus.CANCELED;
        }
        SimpleUser actionUser = userService.getSimple(dto.getUserId()).orElse(null);
        EventStatusType eventStatus = EnumHelper.parse(EventStatusType.values(), dto.getStatus(), EventStatusType.NONE);
        EventAction action = actionService.get(dto.getActionId());
        log.info("warning change status: id={}, userId={}", dto.getWarningId(), dto.getUserId());
        webhook(event, warning, warning.getSurveyId(), warning.getResponseId(), action, actionUser, eventStatus);
        return ConsumerStatus.SUCCESS;
    }

    @Transactional
    public void webhook(CemEvent event, Event e, long surveyId, long responseId, @Nullable EventAction action, @Nullable SimpleUser actionUser, EventStatusType status) {
        var survey = surveyBaseEntityService.require(Survey.class, surveyId);
        var response = surveyBaseEntityService.require(SurveyResponse.class, responseId);
        List<EventMonitorRules> rules = new ArrayList<>();
        Set<Long> ruleIds = new HashSet<>();
        Set<Long> notifyRoleIds = new HashSet<>();
        Set<Long> notifyUserIds = new HashSet<>();
        // 找到开启通知的规则，通知人，角色
        e.getWarnings().forEach(warning -> {
            eventRulesRepository.findById(warning.getRuleId()).ifPresent(rule -> {
                entityManager.detach(rule);
                if (rule.getNotifyConsumer()) {
                    rules.add(rule);
                    ruleIds.add(rule.getId());
                    // 从规则中获取预警名称和通知人员
                    rule.getReceiver().forEach(receiver -> {
                        notifyRoleIds.addAll(receiver.getRoleIds());
                        notifyUserIds.addAll(receiver.getUserIds());
                    });
                }
            });
        });

        List<Connector> connectors = getConnector(ruleIds, ConnectorPushType.WARNING, statusMapTo(status)).stream().filter(c -> c.getType() == ConnectorType.WEBHOOK).collect(Collectors.toList());
        if (!connectors.isEmpty()) {
            // 有需要通知的webhook，开始构建发送内容
            DataWarningDto dataWarningDto = buildWarningData(e.getOrgId(), e, action, status, notifyRoleIds, notifyUserIds, rules);
            ResponseTaskDetailDto sendData = buildResponseDetailDto(survey, response, ResponseTaskDetailDto.class);
            sendData.setDataWarning(dataWarningDto);
            Map<String, Object> pushBody = JsonHelper.toMap(sendData);
            ruleIds.forEach(rid -> createPush(event, pushBody, connectors, rid));
            String webhookName = connectors.stream().filter(c -> ConnectorProviderType.WEBHOOK.equals(c.getProviderType())).map(x -> "webhook-" + x.getName()).collect(Collectors.joining("、"));
            actionService.addActionSingle(
                    actionUser != null ? actionUser : new SimpleUser(null, "系统", null, null, null, null),
                    EventActionType.ACTION_TYPE_NOTICE,
                    "已推送：" + webhookName,
                    EventActionStatusType.SUCCESS,
                    e
            );
        }
    }

    private ConnectorPushCondition statusMapTo(EventStatusType eventStatus) {
        switch (eventStatus) {
            case APPLYING:
                return ConnectorPushCondition.WARNING_APPLYING;
            case SUCCESS:
                return ConnectorPushCondition.WARNING_SUCCESS;
            default:
                return ConnectorPushCondition.WARNING;
        }
    }

    private DataWarningDto buildWarningData(Long orgId,
                                            Event event, @Nullable EventAction action, EventStatusType status,
                                            Set<Long> notifyRoleIds, Set<Long> notifyUserIds,
                                            List<EventMonitorRules> rules) {
        Map<Long, List<SimpleUser>> roleUserMap = userService.getSimpleMapByRoleIds(orgId, new ArrayList<>(notifyRoleIds));
        Map<Long, SimpleUser> userMap = userService.getSimpleMapByIds(notifyUserIds);
        List<DataWarningRuleDto> warningRules = new ArrayList<>();
        Set<String> warningNotices = new HashSet<>();
        rules.forEach(rule -> {
            Set<String> emails = new HashSet<>();
            rule.getReceiver().forEach(receiver -> {
                receiver.getUserIds().forEach(userId -> addEmail(emails, userMap.get(userId)));
                receiver.getRoleIds().forEach(roleId -> addEmail(emails, roleUserMap.get(roleId)));
            });
            warningNotices.addAll(emails);
            warningRules.add(new DataWarningRuleDto(rule.getTitle(), rule.getLevel().getText(), new ArrayList<>(emails)));
        });

        DataWarningDto dataWarningDto = new DataWarningDto();
        dataWarningDto.setEventId(event.getId());
        dataWarningDto.setWarningNotices(new ArrayList<>(warningNotices));
        dataWarningDto.setWarningRules(warningRules);
        dataWarningDto.setStatus(status.desc);
        dataWarningDto.setRemarks(actionService.getRemarksByEventId(event.getId()));
        if (action != null) {
            dataWarningDto.setActionTime(DateHelper.formatDateTime(action.getCreateTime()));
            dataWarningDto.setActionUsername(action.getActionUsername());
        }
        return dataWarningDto;
    }

    private void addEmail(Set<String> emails, SimpleUser simpleUser) {
        if (simpleUser != null && StringUtils.isNotEmpty(simpleUser.getEmail())) {
            emails.add(simpleUser.getEmail());
        }
    }

    private void addEmail(Set<String> emails, List<SimpleUser> simpleUsers) {
        if (CollectionUtils.isNotEmpty(simpleUsers)) {
            simpleUsers.forEach(simpleUser -> addEmail(emails, simpleUser));
        }
    }
}
