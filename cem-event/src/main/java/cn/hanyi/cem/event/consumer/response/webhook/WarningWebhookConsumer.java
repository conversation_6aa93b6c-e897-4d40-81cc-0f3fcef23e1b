package cn.hanyi.cem.event.consumer.response.webhook;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventResponseDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.repository.EventRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Component
@Slf4j
@Order(3000)
public class WarningWebhookConsumer extends BaseWebhookConsumer implements IEventConsumer<EventResponseDto> {

    @Autowired
    private EventRepository eventRepository;
    @Autowired
    private WarningChangeStatusWebhookConsumer proxy;

    @Override
    public List<EventType> types() {
        return List.of(EventType.RESPONSE_SUBMIT_FINAL);
    }

    @Transactional
    @Override
    public ConsumerStatus consumer(CemEvent event, EventResponseDto dto) {
        Event warning = eventRepository.findOneBySurveyIdAndResponseId(dto.getSurveyId(), dto.getResponseId()).orElse(null);
        if (warning == null) {
            log.warn("push response warning cancel: surveyId={}, responseId={}, event not found", dto.getSurveyId(), dto.getResponseId());
            return ConsumerStatus.CANCELED;
        }
        log.info("push response warning: surveyId={}, responseId={}", dto.getSurveyId(), dto.getResponseId());
        proxy.webhook(event, warning, dto.getSurveyId(), dto.getResponseId(), null, null, EventStatusType.WAIT);
        return ConsumerStatus.SUCCESS;
    }
}
