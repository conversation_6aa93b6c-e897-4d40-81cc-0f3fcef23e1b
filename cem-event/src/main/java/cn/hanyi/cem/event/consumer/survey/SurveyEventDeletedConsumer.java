package cn.hanyi.cem.event.consumer.survey;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSurveyDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.constant.ExperienceInteractionStatus;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.constant.event.EventSurveyStatus;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.ExperienceIndicatorRepository;
import cn.hanyi.ctm.repository.ExperienceInteractionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/***
 * 问卷删除后 预警事件和旅程场景需要做修改
 */
@Slf4j
@Component
public class SurveyEventDeletedConsumer implements IEventConsumer<EventSurveyDto> {


    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;

    @Autowired
    private ExperienceIndicatorRepository experienceIndicatorRepository;

    @Autowired
    private ExperienceInteractionRepository experienceInteractionRepository;

    @Override
    public List<EventType> types() {
        return List.of( EventType.SURVEY_DELETE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent event, EventSurveyDto dto) {
        log.info("survey deleted: surveyId={}", dto.getSurveyId());
        updateWarningRule(dto);
        updateExperienceIndicator(dto);
        return ConsumerStatus.SUCCESS;
    }

    /**
     * 问卷删除后修改预警规则状态
     * @param dto
     */
    private void updateWarningRule(EventSurveyDto dto){
        try{
            eventMonitorRulesRepository.findBySurveyId(dto.getSurveyId())
                    .ifPresent(rules -> rules.forEach(rule -> {
                        rule.setSurveyStatus(EventSurveyStatus.SURVEY);
                        rule.setStatus(EventMonitorStatus.CLOSE);
                        eventMonitorRulesRepository.save(rule);
                    }));
        }catch (Exception e){
            log.error("update survey: {} warning rule error: {}", dto.getSurveyId(), e.getMessage());
        }

    }

    /**
     * 问卷删除后修改旅程场景状态
     * @param dto
     */
    private void updateExperienceIndicator(EventSurveyDto dto){
        try{
            experienceIndicatorRepository.findAllBySid(dto.getSurveyId().toString())
                    .forEach(experienceIndicator -> {
                        experienceIndicator.setIsValid(ExperienceInteractionStatus.UNVALID);
                        experienceIndicatorRepository.save(experienceIndicator);
                    });

            experienceInteractionRepository.findAllByInteractionSids(dto.getSurveyId().toString()).forEach(experienceInteraction -> {
                experienceInteraction.setIsValid(ExperienceInteractionStatus.UNVALID);
                experienceInteractionRepository.save(experienceInteraction);
            });
        }catch (Exception e){
            log.error("update survey: {} experience indicator error: {}", dto.getSurveyId(), e.getMessage());
        }

    }

}
