package cn.hanyi.cem.event.consumer.survey;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSurveyDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.repository.PushRepository;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.UserService;
import org.befun.core.exception.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static cn.hanyi.cem.event.consumer.utils.ForeachPageUtils.foreachPage;

@Slf4j
@Component
public class SurveyEventSyncTitleConsumer implements IEventConsumer<EventSurveyDto> {

    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private EventRepository eventRepository;
    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.SURVEY_UPDATE);
    }

    @Override
    @Transactional
    public ConsumerStatus consumer(CemEvent event, EventSurveyDto dto) {
        log.info("push survey status: surveyId={}, status={}", dto.getSurveyId(), event.getType());
        // 问卷删除的时候是软删除，jpa查询不到
        Survey survey = surveyRepository.getOneByIdSql(dto.getSurveyId()).orElseThrow(EntityNotFoundException::new);

        updateEventSurveyTitle(survey);
        updateEventRuleTitle(survey);

        return ConsumerStatus.SUCCESS;
    }


    private void updateEventSurveyTitle( Survey survey) {
        try {
            String title = survey.getTitle();
            foreachPage(
                    page -> eventRepository.findAll((r,q,c)->{q.where(c.equal(r.get("surveyId"),survey.getId()));return q.getRestriction();
                    },  PageRequest.of(page, 500)),
                    e -> {
                        if(!title.equals(e.getSurveyName())){
                            e.setSurveyName(title);
                            eventRepository.save(e);
                        }
                    }
            );
        } catch (Exception e) {
            log.error("sId:{} updateEventSurveyTitle failed", survey.getId(), e);
        }
    }

    private void updateEventRuleTitle( Survey survey) {
        try {
            String title = survey.getTitle();
            foreachPage(
                    page -> eventMonitorRulesRepository.findAll((r,q,c)->{q.where(c.equal(r.get("surveyId"),survey.getId()));return q.getRestriction();
                    },  PageRequest.of(page, 500)),
                    e -> {
                        if(!title.equals(e.getSurveyName())){
                            e.setSurveyName(title);
                            eventMonitorRulesRepository.save(e);
                        }
                    }
            );
        } catch (Exception e) {
            log.error("sId:{} updateEventRuleTitle failed", survey.getId(), e);
        }
    }


}
