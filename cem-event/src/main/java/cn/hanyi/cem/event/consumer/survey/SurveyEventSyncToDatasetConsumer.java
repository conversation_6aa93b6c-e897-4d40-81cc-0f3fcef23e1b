package cn.hanyi.cem.event.consumer.survey;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSurveyDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionColumn;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.befun.bi.constant.DynamicColumnExtendType;
import org.befun.bi.constant.TableDynamicField;
import org.befun.bi.constant.dataset.DatasetType;
import org.befun.bi.dto.survey.FieldDataDto;
import org.befun.bi.entity.Dataset;
import org.befun.bi.provider.impls.DynamicJdbcProvider;
import org.befun.bi.repository.DatasetFieldRepository;
import org.befun.bi.repository.DatasetRepository;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

/***
 * 问卷更新后，题目选项同步到BI数据集字段的rawSql中
 */
@Slf4j
@Component
public class SurveyEventSyncToDatasetConsumer implements IEventConsumer<EventSurveyDto> {

    @Autowired
    private DatasetRepository datasetRepository;

    @Autowired
    private DatasetFieldRepository datasetFieldRepository;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Autowired
    private DynamicJdbcProvider dynamicJdbcProvider;

    private static final List<QuestionType> needSyncType =
            Arrays.asList(QuestionType.SINGLE_CHOICE, QuestionType.MULTIPLE_CHOICES,
                    QuestionType.SCORE_EVALUATION, QuestionType.EVALUATION,
                    QuestionType.MATRIX_CHOICE, QuestionType.COMBOBOX);

    private static final List<DynamicColumnExtendType> allowedType =
            Arrays.asList(DynamicColumnExtendType.SINGLE_CHOICE, DynamicColumnExtendType.MULTIPLE_CHOICES,
                    DynamicColumnExtendType.SCORE_EVALUATION_EVA, DynamicColumnExtendType.EVALUATION_EVA,
                    DynamicColumnExtendType.MATRIX_CHOICE, DynamicColumnExtendType.COMBOBOX);

    @Override
    public List<EventType> types() {
        return List.of( EventType.SURVEY_ENABLE);
    }

    @Override
    @Transactional
    public ConsumerStatus consumer(CemEvent event, EventSurveyDto dto) {
        log.info("survey update: surveyId={}", dto.getSurveyId());
        // 手动创建事务 获取问卷懒加载数据
        // @Transactional 事务会提交失败
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            Survey survey = surveyBaseEntityService.require(Survey.class, dto.getSurveyId());
            Map<String, Map<String, String>> questionMap = getQuestionMap(survey.getQuestions());
            transactionManager.commit(status);
            updateDatasetField(survey, questionMap);
        } catch (Exception e) {
            transactionManager.rollback(status);
            log.error("question sync to dataset field error: {}", Arrays.toString(e.getStackTrace()));
        }
        return ConsumerStatus.SUCCESS;
    }

    /**
     * 问卷更新后同步更新数据集字段sql
     */
    private void updateDatasetField(Survey survey, Map<String, Map<String, String>> questionMap) {
        if (survey == null) {
            log.info("survey not exist");
            return;
        }

        List<Dataset> datasetList = this.datasetRepository
                .findAllByTableNameAndType(survey.getId().toString(), DatasetType.SURVEY);
        datasetList.forEach(d -> d.getFields().stream()
                .filter(f -> f.getData() != null && allowedType.contains(f.getExtendType()))
                .forEach(f -> {
                    FieldDataDto fieldDataDto = JsonHelper
                            .toObject(f.getData(), FieldDataDto.class);
                    Map<String, String> itemMap;
                    if (fieldDataDto != null
                            && (itemMap = questionMap.get(fieldDataDto.getQ_id().toString()))
                            != null) {
                        if (f.getExtendType() == DynamicColumnExtendType.MULTIPLE_CHOICES) {
                            f.setRawSql(TableDynamicField.QUESTION_ITEM_ARRAY
                                    .getSql(dynamicJdbcProvider.getDialect(), itemMap, f.getOriginId()));
                        } else {
                            f.setRawSql(TableDynamicField.QUESTION_ITEM
                                    .getSql(dynamicJdbcProvider.getDialect(), questionMap.get(fieldDataDto.getQ_id().toString()),
                                            f.getOriginId()));
                        }
                        log.info("update survey field {} sql: {}", f.getId(), f.getRawSql());
                    }
                    datasetFieldRepository.save(f);
                }));
    }

    private Map<String, Map<String, String>> getQuestionMap(List<SurveyQuestion> questions) {
        Map<String, Map<String, String>> questionMap = new HashMap<>();
        questions.stream().filter(q -> needSyncType.contains(q.getType())).forEach(q -> {
            Map<String, String> questionItemsMap = getQuestionItemMap(q);
            questionMap.put(q.getId().toString(), questionItemsMap);
        });
        return questionMap;
    }

    private Map<String, String> getQuestionItemMap(SurveyQuestion question) {
        Map<String, String> questionItemsMap = new HashMap<>();
        String regex = "<[^>]*>";
        Pattern pattern = Pattern.compile(regex);

        questionItemsMap.put("other_label", question.getOtherLabel());
        if (question.getType() == QuestionType.MATRIX_CHOICE) {
            question.getColumns().forEach(c -> questionItemsMap.put(c.getValue(), pattern.matcher(c.getText()).replaceAll("")));
        } else {
            question.getItems().forEach(i -> questionItemsMap.put(i.getValue(), pattern.matcher(i.getText()).replaceAll("")));
        }
        return questionItemsMap;
    }

}
