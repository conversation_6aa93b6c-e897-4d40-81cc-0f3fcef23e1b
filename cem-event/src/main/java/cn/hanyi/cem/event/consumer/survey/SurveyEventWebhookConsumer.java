package cn.hanyi.cem.event.consumer.survey;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSurveyDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.constant.PushStatus;
import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import cn.hanyi.ctm.constant.connector.ConnectorPushType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.entity.Push;
import cn.hanyi.ctm.repository.PushRepository;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import cn.hanyi.survey.core.dto.webhook.PushSurvey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class SurveyEventWebhookConsumer implements IEventConsumer<EventSurveyDto> {

    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private ConnectorConsumerService connectorConsumerService;
    @Autowired
    private PushRepository pushRepository;
    @Autowired
    private TaskProducerHelper taskProducerHelper;

    // 兼容以前webhook推送的
    private final HashMap<EventType, ConnectorPushCondition> pushStatus = new HashMap<>() {{
        put(EventType.SURVEY_CREATE, ConnectorPushCondition.CREATED);
        put(EventType.SURVEY_UPDATE, ConnectorPushCondition.UPDATED);
        put(EventType.SURVEY_DELETE, ConnectorPushCondition.DELETED);
        put(EventType.SURVEY_ENABLE, ConnectorPushCondition.ENABLE);
        put(EventType.SURVEY_DISABLE, ConnectorPushCondition.DISABLE);
    }};

    @Override
    public List<EventType> types() {
        return List.of(EventType.SURVEY_CREATE, EventType.SURVEY_DELETE, EventType.SURVEY_DISABLE, EventType.SURVEY_ENABLE, EventType.SURVEY_UPDATE);
    }

    @Override
    @Transactional
    public ConsumerStatus consumer(CemEvent event, EventSurveyDto dto) {
        log.info("push survey status: surveyId={}, status={}", dto.getSurveyId(), event.getType());
        // 问卷删除的时候是软删除，jpa查询不到
        var survey = surveyRepository.getOneByIdSql(dto.getSurveyId()).orElseThrow(EntityNotFoundException::new);
        HashSet<Long> s = new HashSet<>();

        Optional.ofNullable(survey.getUserId()).ifPresent(s::add);
        Optional.ofNullable(survey.getEditorId()).ifPresent(s::add);
        List<SimpleUser> users = Optional.ofNullable(userService.getSimpleByIds(s)).orElse(List.of());

        Optional<SimpleUser> creator = users.stream().filter(u -> u.getId().equals(survey.getUserId())).findFirst();
        Optional<SimpleUser> editor = users.stream().filter(u -> u.getId().equals(survey.getEditorId())).findFirst();

        var body = PushSurvey.builder()
                .surveyId(dto.getSurveyId())
                .surveyTitle(survey.getTitle())
                .surveyCode(survey.getSurveyCode())
                .surveyStatus(pushStatus.get(event.getType()))
                .createTime(survey.getCreateTime())
                .modifyTime(survey.getModifyTime())
                .creator(creator.isEmpty() ? null : creator.get().getTruename())
                .editor(editor.isEmpty() ? null : editor.get().getTruename())
                .build();

        String pushBody = JsonHelper.toJson(body);

        List<Push> pushList = new ArrayList<>();

        connectorConsumerService.webhookConnector(
                Stream.of(survey.getId()).collect(Collectors.toSet()),
                ConnectorPushType.SURVEY,
                pushStatus.get(event.getType())
        ).forEach(connector -> {
            var url = connector.getGateway();
            Push push = new Push();
            push.setOrgId(survey.getOrgId());
            push.setName(connector.getName());
            push.setConnector(connector);
            push.setType(ConnectorType.WEBHOOK);
            push.setAddress(url);
            push.setContent(pushBody);
            push.setStatus(PushStatus.FAILED);
            pushRepository.save(push);
            pushList.add(push);
        });
        pushList.forEach(push -> {
            taskProducerHelper.webhookCreateFromEvent(event, push.getId());
        });
        return ConsumerStatus.SUCCESS;
    }
}
