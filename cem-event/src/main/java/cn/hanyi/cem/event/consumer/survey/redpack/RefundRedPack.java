package cn.hanyi.cem.event.consumer.survey.redpack;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventRefundRedPackDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeWinnerRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import com.github.binarywang.wxpay.bean.result.WxPayRedpackQueryResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.dto.order.RedPacketOrderRefundRequestDto;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/21 18:21:11
 */
@Component
@Slf4j
public class RefundRedPack implements IEventConsumer<EventRefundRedPackDto> {

    @Autowired
    private WxPayService wxPayService;

    @Autowired
    private OrganizationOrderService organizationOrderService;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SurveyLotteryPrizeWinnerRepository winnerRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.REFUND_RED_PACK);
    }

    @Override
    public ConsumerStatus consumer(CemEvent cemEvent, EventRefundRedPackDto dto) {
        try {
            WxPayRedpackQueryResult request = wxPayService.getRedpackService().queryRedpack(dto.getMchBillNo());
            log.info("红包查询数据==========\n {}", request);
//            <return_code><![CDATA[SUCCESS]]></return_code>
//                    <return_msg><![CDATA[OK]]></return_msg>
//                    <result_code><![CDATA[SUCCESS]]></result_code>
//                    <err_code><![CDATA[SUCCESS]]></err_code>
//                    <err_code_des><![CDATA[OK]]></err_code_des>
//                    <mch_billno><![CDATA[0662554322462701877153473605]]></mch_billno>
//                    <mch_id><![CDATA[1522047931]]></mch_id>
//                    <detail_id><![CDATA[1000041701202504293059281354324]]></detail_id>
//                    <status><![CDATA[SENDING]]></status>
//                    <send_type><![CDATA[API]]></send_type>
//                    <hb_type><![CDATA[NORMAL]]></hb_type>
//                    <total_num>1</total_num>
//                    <total_amount>100</total_amount>
//                    <reason><![CDATA[该用户今日操作次数超过限制，如有需要请登录微信支付商户平台更改API安全配置]]></reason>
//                    <send_time><![CDATA[2025-04-29 14:40:21]]></send_time>
//                    </xml>
            //请求成功
            String success = "SUCCESS";
            if (success.equals(request.getReturnCode()) && success.equals(request.getResultCode())) {
                String status = request.getStatus();
                //已退款  // 发放失败
                if ("REFUND".equals(status) || "SENDING".equals(status)) {
                    log.info("红包已退款====status:{}", status);
                    Survey survey = surveyRepository.findById(dto.getSurveyId()).orElseThrow(() -> new BadRequestException("问卷已删除"));
                    Long orgId = survey.getOrgId();
                    Long userId = cemEvent.getUserId();
                    Integer amount = request.getRefundAmount() != null ? request.getRefundAmount() : request.getTotalAmount();
                    organizationOrderService.refundOrder(orgId, userId, OrderType.order_red_packet,
                            new RedPacketOrderRefundRequestDto(
                                    dto.getOrderId(),
                                    dto.getLotteryId(),
                                    amount,
                                    dto.getWinnerId().toString(),
                                    "红包返还:" + survey.getTitle()));
                    //状态改为已退还
                    winnerRepository.findById(dto.getWinnerId()).ifPresent(winner -> {
                        winner.setStatus(PrizeSendStatus.REFUND);
                        winner.setRefundTime(new Date());
                        winnerRepository.save(winner);
                        log.info("退款成功，{}", winner.getStatus());
                    });
                }
                return ConsumerStatus.SUCCESS;
            } else {
                log.info("查询红包失败,return_msg:{},err_code:{},err_code_des:{}", request.getReturnMsg(), request.getErrCode(), request.getErrCodeDes());
                return ConsumerStatus.FAILED;
            }
        } catch (WxPayException e) {
            log.info("回退红包异常", e);
            return ConsumerStatus.FAILED;
        }
    }

}
