package cn.hanyi.cem.event.consumer.survey.redpack;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSendRedPackDelayDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.survey.client.service.luckyDraw.WechatLuckyDrawService;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import cn.hanyi.survey.core.dto.lottery.SendRedPackParam;
import cn.hanyi.survey.core.entity.SurveyLotteryPrizeWinner;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeWinnerRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SendRedPackDelay implements IEventConsumer<EventSendRedPackDelayDto> {

    @Autowired
    private WechatLuckyDrawService wechatLuckyDrawService;
    @Autowired
    private SurveyLotteryPrizeWinnerRepository prizeWinnerRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.SEND_RED_PACK_DELAY);
    }

    @Override
    public ConsumerStatus consumer(CemEvent cemEvent, EventSendRedPackDelayDto dto) {
        String openid = dto.getOpenid();
        Long winnerId = dto.getWinnerId();
        log.info("sendRedPackDelay openid:{} , winnerId: {}", openid, winnerId);

        if(openid == null || winnerId == null){
            return ConsumerStatus.CANCELED;
        }


        SurveyLotteryPrizeWinner winner = prizeWinnerRepository.findById(winnerId).orElse(null);
        if(winner == null&& PrizeSendStatus.NOT_SEND.equals(winner.getStatus()) && !winner.getIsReceived()){
            return ConsumerStatus.CANCELED;
        }

        try {
            wechatLuckyDrawService.SendRedPack(new SendRedPackParam(winnerId, openid));
        }catch (Exception e){
            log.error("sendRedPackDelay error openid:{} , winnerId: {}: {}",openid,winner, e.getMessage());
            return ConsumerStatus.CANCELED;
        }
        return ConsumerStatus.SUCCESS;
    }

}
