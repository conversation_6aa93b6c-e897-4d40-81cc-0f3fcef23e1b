package cn.hanyi.cem.event.consumer.surveyaudit;

import cn.hanyi.cem.event.consumer.notifyconfig.InboxMessageConfig;
import cn.hanyi.cem.event.consumer.notifyconfig.NotifySmsConfig;
import cn.hanyi.cem.event.consumer.notifyconfig.NotifyWeChatMpConfig;
import cn.hanyi.cem.event.consumer.notifyconfig.WeWorkBotConfig;
import lombok.Getter;
import lombok.Setter;
import org.befun.extension.constant.InboxMessageType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Setter
@Getter
@Component
@ConfigurationProperties(prefix = "worker.survey-audit")
public class SurveyAuditProperties {

    private SurveyContentAudit contentAudit = new SurveyContentAudit();
    private SurveyReport report = new SurveyReport();
    private SurveyManualCheck manualCheckPass = new SurveyManualCheck();
    private SurveyManualCheck manualCheckNoPass = new SurveyManualCheck();

    @Getter
    @Setter
    public static class SurveyContentAudit implements WeWorkBotConfig {
        private String notifyUrl;
        private String adminxUrl;
        private String template;
    }

    @Getter
    @Setter
    public static class SurveyReport implements WeWorkBotConfig {
        private String notifyUrl;
        private String adminxUrl;
        private String template;
    }

    @Getter
    @Setter
    public static class SurveyManualCheck implements InboxMessageConfig, NotifySmsConfig, NotifyWeChatMpConfig {
        private String wechatMpTemplateName;
        private String smsTemplateName;
        private String inboxMessageTemplateName;
    }


}
