package cn.hanyi.cem.event.consumer.surveyaudit;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSurveyContentAuditDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.notifyconfig.NotifyConfigBuilder;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ContentAuditStatus;
import org.befun.auth.entity.ContentAuditRecord;
import org.befun.auth.entity.Organization;
import org.befun.auth.service.ContentAuditRecordService;
import org.befun.auth.service.OrganizationService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.task.mq.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SurveyContentAuditConsumer extends NotifyConfigBuilder<SurveyAuditProperties.SurveyContentAudit> implements IEventConsumer<EventSurveyContentAuditDto> {
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ContentAuditRecordService contentAuditRecordService;
    @Autowired
    private SurveyAuditProperties surveyAuditProperties;
    @Value("${adminx.survey-audit-key:}")
    private String surveyAuditKey;
    @Autowired
    private ITaskService taskService;

    @Override
    public List<EventType> types() {
        return List.of(EventType.SURVEY_CONTENT_AUDIT);
    }

    @Override
    protected SurveyAuditProperties.SurveyContentAudit getConfig() {
        return surveyAuditProperties.getContentAudit();
    }

    /**
     * 1 机器人通知
     * 2 发送到adminx
     */
    @Override
    public ConsumerStatus consumer(CemEvent event, EventSurveyContentAuditDto param) {
        Organization org;
        if ((org = organizationService.get(event.getOrgId())) == null) {
            event.setResponse("企业不存在");
            return ConsumerStatus.FAILED;
        }
        Long surveyId = param.getSurveyId();
        SimpleSurvey survey;
        if (surveyId == null || surveyId <= 0 || (survey = surveyRepository.findSimpleById(surveyId)) == null) {
            event.setResponse("问卷不存在");
            return ConsumerStatus.FAILED;
        }
        ContentAuditRecord record;
        if ((record = contentAuditRecordService.get(param.getAuditId())) == null) {
            event.setResponse("内容审核记录不存在");
            return ConsumerStatus.FAILED;
        } else if (record.getStatus() == ContentAuditStatus.pass) {
            event.setResponse("内容审核记录已处理");
            return ConsumerStatus.CANCELED;
        }
        StringBuilder reasons = new StringBuilder();
        if (record.getStatus() == ContentAuditStatus.suspected) {
            reasons.append("疑似");
        } else {
            reasons.append("审核失败");
        }
        String reason = null;
        List<String> words = JsonHelper.toList(record.getWords(), String.class);
        if (CollectionUtils.isNotEmpty(words)) {
            int index = 0;
            for (String word : words) {
                if (StringUtils.isNotEmpty(word)) {
                    word = (index == 0 ? ":" : ",") + word;
                    index++;
                    reasons.append(word);
                    int size = reasons.length();
                    if (size >= 47) {
                        reason = reasons.substring(0, 47) + "...";
                        break;
                    }
                }
            }
        }
        if (reason == null) {
            reason = reasons.toString();
        }
        sendWeWorkBot(getParams(org, survey, reason));
        // add to adminx
        addToAdminx(event, org, survey, reason, record);
        return ConsumerStatus.SUCCESS;
    }

    private Map<String, Object> getParams(Organization org, SimpleSurvey survey, String reason) {

        //  >企业代码：${orgCode}
        //  >企业名称：${orgName}
        //  >问卷ID：${surveyId}
        //  >问卷标题：${surveyTitle}
        //  >审核原因：${reason}
        Map<String, Object> params = new HashMap<>();
        params.put("orgCode", org.getCode());
        params.put("orgName", org.getName());
        params.put("surveyId", survey.getId());
        params.put("surveyTitle", survey.getTitle());
        params.put("reason", reason);
        return params;
    }

    private void addToAdminx(CemEvent event, Organization org, SimpleSurvey survey, String reason, ContentAuditRecord record) {
        AdminxSurveyContentAuditDto dto = new AdminxSurveyContentAuditDto(
                org.getId(),
                event.getUserId(),
                survey.getId(),
                record.getId(),
                reason,
                JsonHelper.toList(record.getWords(), String.class),
                DateHelper.formatDateTime(record.getCreateTime())
        );
        taskService.addTask(surveyAuditKey, dto);
    }
}
