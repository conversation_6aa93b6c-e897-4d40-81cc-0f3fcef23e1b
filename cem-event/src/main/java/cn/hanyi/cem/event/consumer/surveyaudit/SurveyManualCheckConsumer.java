package cn.hanyi.cem.event.consumer.surveyaudit;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSurveyManualCheckDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.notifyconfig.NotifyConfigBuilder;
import cn.hanyi.survey.core.projection.SimpleSurvey3;
import cn.hanyi.survey.core.repository.SurveyRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.RegHelper;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

@Slf4j
public abstract class SurveyManualCheckConsumer<C> extends NotifyConfigBuilder<C> implements IEventConsumer<EventSurveyManualCheckDto> {

    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private UserService userService;
    @Autowired
    protected SurveyAuditProperties surveyAuditProperties;

    @Override
    public List<EventType> types() {
        return List.of(EventType.SURVEY_MANUAL_CHECK);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventSurveyManualCheckDto param) {
        return apply(entity, param, this::inboxMessage);
    }

    protected ConsumerStatus apply(CemEvent event, EventSurveyManualCheckDto param, BiConsumer<SimpleSurvey3, SimpleUser> apply) {
        Long surveyId = param.getSurveyId();
        SimpleSurvey3 survey;
        if (surveyId == null || surveyId <= 0 || (survey = surveyRepository.findSimple3ById(surveyId)) == null) {
            event.setResponse("问卷不存在");
            return ConsumerStatus.FAILED;
        }
        SimpleUser user = userService.getSimple(survey.getUserId()).orElse(null);
        if (user == null) {
            event.setResponse("问卷创建人不存在");
            return ConsumerStatus.CANCELED;
        }
        apply.accept(survey, user);
        return ConsumerStatus.SUCCESS;
    }

    protected void inboxMessage(SimpleSurvey3 survey, SimpleUser user) {
        Map<String, Object> params = new HashMap<>();
        params.put("surveyId", survey.getId());
        params.put("surveyTitle", survey.getTitle());
        sendInboxMessage(survey.getOrgId(), survey.getUserId(), params);
    }

    protected void sms(SimpleSurvey3 survey, SimpleUser user) {
        if (RegHelper.isMobile(user.getMobile())) {
            Map<String, Object> params = new HashMap<>();
            params.put("surveyTitle", survey.getTitle());
            sendSms(user.getMobile(), params);
        }
    }

    protected void wechatMp(SimpleSurvey3 survey, SimpleUser user, boolean pass) {
        Map<String, Object> params = new HashMap<>();
        params.put("username", user.getTruename());
        params.put("surveyTitle", survey.getTitle());
        params.put("auditStatus", pass ? "已通过" : "已驳回");
        params.put("notifyTime", DateHelper.formatDateTime(new Date()));
        sendWechatMp(survey.getOrgId(), user.getId(), params);
    }

}
