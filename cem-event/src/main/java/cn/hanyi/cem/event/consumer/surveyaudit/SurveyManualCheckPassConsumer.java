package cn.hanyi.cem.event.consumer.surveyaudit;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.dto.event.EventSurveyManualCheckDto;
import cn.hanyi.cem.core.entity.CemEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SurveyManualCheckPassConsumer extends SurveyManualCheckConsumer<SurveyAuditProperties.SurveyManualCheck> {

    @Override
    protected SurveyAuditProperties.SurveyManualCheck getConfig() {
        return surveyAuditProperties.getManualCheckPass();
    }

    /**
     * source: contentAudit report
     * result: contentAudit(pass|noPass|disabled) report(pass|disabled)
     * 内容审核通过：站内信 短信 微信公众号
     */
    @Override
    public ConsumerStatus consumer(CemEvent event, EventSurveyManualCheckDto param) {
        if ("contentAudit".equals(param.getSource())) {
            if ("pass".equals(param.getResult())) {
                return apply(event, param, (s, u) -> {
                    inboxMessage(s, u);
                    sms(s, u);
                    wechatMp(s, u, true);
                });
            }
        }
        return ConsumerStatus.CANCELED;
    }


}
