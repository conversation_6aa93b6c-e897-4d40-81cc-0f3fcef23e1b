package cn.hanyi.cem.event.consumer.surveyaudit;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventSurveyReportDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.notifyconfig.NotifyConfigBuilder;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.entity.Organization;
import org.befun.auth.service.ContentAuditRecordService;
import org.befun.auth.service.OrganizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SurveyReportConsumer extends NotifyConfigBuilder<SurveyAuditProperties.SurveyReport> implements IEventConsumer<EventSurveyReportDto> {

    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ContentAuditRecordService contentAuditRecordService;
    @Autowired
    private SurveyAuditProperties surveyAuditProperties;

    @Override
    public List<EventType> types() {
        return List.of(EventType.SURVEY_REPORT);
    }

    @Override
    protected SurveyAuditProperties.SurveyReport getConfig() {
        return surveyAuditProperties.getReport();
    }

    /**
     * 1 机器人通知
     */
    @Override
    public ConsumerStatus consumer(CemEvent event, EventSurveyReportDto param) {
        Long surveyId = param.getSurveyId();
        Organization org;
        if ((org = organizationService.get(event.getOrgId())) == null) {
            event.setResponse("企业不存在");
            return ConsumerStatus.FAILED;
        }
        SimpleSurvey survey;
        if (surveyId == null || surveyId <= 0 || (survey = surveyRepository.findSimpleById(surveyId)) == null) {
            event.setResponse("问卷不存在");
            return ConsumerStatus.FAILED;
        }
        sendWeWorkBot(getParams(param, org, survey));
        return ConsumerStatus.SUCCESS;
    }

    private Map<String, Object> getParams(EventSurveyReportDto param, Organization org, SimpleSurvey survey) {
        //        问卷举报通知
        //        >企业代码：${orgCode}
        //        >企业名称：${orgName}
        //        >问卷ID：${surveyId}
        //        >问卷标题：${surveyTitle}
        //        >举报原因：${reason}
        //        >举报时间：${reportTime}
        //        >备注：请在半个小时内处理！[点击查看](${adminxUrl})
        Map<String, Object> params = new HashMap<>();
        params.put("orgCode", org.getCode());
        params.put("orgName", org.getName());
        params.put("surveyId", survey.getId());
        params.put("surveyTitle", survey.getTitle());
        params.put("reason", param.getReason());
        params.put("reportTime", param.getReportTime());
        return params;
    }

}
