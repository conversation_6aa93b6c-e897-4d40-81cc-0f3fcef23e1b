package cn.hanyi.cem.event.consumer.utils;

import cn.hanyi.cem.core.dto.task.TaskNotifyBaseDto;
import cn.hanyi.ctm.constant.SentimentType;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.survey.core.dto.SubmitAdditionDataDto;
import cn.hanyi.survey.core.dto.message.SurveyResponseCellMessageDto;
import cn.hanyi.survey.core.dto.message.SurveyResponseMessageDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.entity.Department;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.UserService;
import org.befun.core.constant.ResourcePermissionTypes;
import org.befun.core.service.ResourceCorporationService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.nativesql.SqlBuilder;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.befun.auth.constant.ResourcePermissionRelationType.ADMIN;
import static org.befun.core.constant.ResourceShareFlag.SHARE_FOUND_IGNORE;

@Component
@Slf4j
public class EventConsumerUtils {

    @Autowired
    private CustomerService customerService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private ResourceCorporationService resourceCorporationService;

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;
    @Autowired
    private UserService userService;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;


    public Customer getCustomerFromResponse(SurveyResponse response) {
        // 保证 customer存在或者为null 不会报错
        String customerGroupCode = null;
        String mobile = null;
        if (ObjectUtils.isNotEmpty(response.getAdditionData())) {
            SubmitAdditionDataDto addtionData = JsonHelper.toObject(response.getAdditionData(), SubmitAdditionDataDto.class);
            if (addtionData != null) {
                customerGroupCode = addtionData.getGroupCode();
                mobile = addtionData.getLinkedCustomerMobile();
            }
        }

        return customerService.getCustomerFromResponse(
                response.getOrgId(),
                response.getExternalUserId(),
                response.getCustomerId(),
                response.getDepartmentId(),
                response.getDepartmentCode(),
                mobile,
                response.getCustomerName(),
                response.getCustomerGender(),
                customerGroupCode,
                response.getTags()
        );
    }

    /**
     * 给用户保存事件权限
     */
    public void grantResourcePermission(Long orgId, Long eventId, Long userId, ResourcePermissionTypes type) {
        try {
            resourceCorporationService.shareToUser(eventId, type, ADMIN, orgId, userId, SHARE_FOUND_IGNORE);
        } catch (Exception ex) {
            log.error("grant event: {} permission error: {}", eventId, ex.getMessage());
        }
    }

    public Map<String, Object> buildNotifyParam(SimpleUser current, Event event, String targetUrl, Map<String, Object> extParams) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Map<String, Object> map = new HashMap<>();
        map.put("eventId", event.getId());
        map.put("surveyName", event.getSurveyName());
        map.put("url", targetUrl);
        map.put("warningLevel", event.getWarningLevel().getText());
        map.put("warningLevelSimple", event.getWarningLevel().getSimpleText());
        map.put("warningTitle", event.getWarningTitle());
        map.put("warningTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(event.getCreateTime()));
        map.put("closeTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("departmentName", Optional.ofNullable(departmentService.get(event.getDepartmentId())).map(Department::getTitle).orElse(""));
        if (current != null) {
            map.put("formUserName", current.getTruename());
            map.put("formUserPhone", current.getMobile());
        }
        map.put("closeTime", sdf.format(new Date()));

        if (MapUtils.isNotEmpty(extParams)) {
            map.putAll(extParams);
        }
        return map;
    }

    @SneakyThrows
    public <W extends TaskNotifyBaseDto> W buildBaseParam(Class<W> clazz, Long orgId, Long departmentId, Set<Long> roleIds, Set<Long> userIds, Long sourceId) {
        W param = clazz.getDeclaredConstructor().newInstance();
        param.setOrgId(orgId);
        param.setDepartmentId(departmentId);
        param.setRoleIds(roleIds);
        param.setUserIds(userIds);
        param.setSourceId(sourceId);
        return param;
    }

    public SentimentType getEntity(Supplier supplier) {
//        SentimentType 1
        supplier.get();

        return SentimentType.POSITIVE;
    }

    /**
     * 查询事件需要通知的用户
     */
    public List<SimpleUser> getNotifyUsers(Long orgId, Long departmentId, Set<Long> roleIds, Set<Long> externalUserIds) {

        if (CollectionUtils.isEmpty(roleIds) && CollectionUtils.isNotEmpty(externalUserIds)) {
            return userService.getSimpleByIds(externalUserIds);
        }

        if (CollectionUtils.isEmpty(roleIds) && CollectionUtils.isEmpty(externalUserIds)) {
            return null;
        }
        List<Long> departmentIds = new ArrayList<>();
        if (departmentId == null || departmentId <= 0) {
            // 事件没有部门（旧数据或者数据不完整，则查询所有的部门）
            departmentIds = Optional.ofNullable(departmentService.getRoot(orgId)).stream().map(Department::getId).collect(Collectors.toList());
        } else {
            // 顶层部门不管部门过滤，只通知角色
            // 其他部门需要通知以上的数据角色的用户
            DepartmentTreeDto tree = departmentService.treeByDepartment(orgId, departmentId, false);
            if (tree != null) {
                departmentIds.add(tree.getId());
                if (!tree.getId().equals(departmentId)) {
                    List<DepartmentTreeDto> sub = tree.getSubDepartments();
                    while (CollectionUtils.isNotEmpty(sub)) {
                        DepartmentTreeDto child = sub.get(0);
                        departmentIds.add(child.getId());
                        if (child.getId().equals(departmentId) || CollectionUtils.isEmpty(child.getSubDepartments())) {
                            break;
                        }
                        sub = child.getSubDepartments();
                    }
                }
            }

        }
        if (CollectionUtils.isEmpty(departmentIds)) {
            return null;
        }
        SqlBuilder sqlBuilder = userService.createSqlBuilder(orgId, null, roleIds, departmentIds, null, null, null, null);
        List<Long> ids = nativeSqlHelper.queryIds(sqlBuilder);
        Set<Long> allIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            allIds.addAll(ids);
        }
        if (CollectionUtils.isNotEmpty(externalUserIds)) {
            allIds.addAll(externalUserIds);
        }
        if (allIds.isEmpty()) {
            return null;
        }
        return userService.getSimpleByIds(allIds);
    }

    public <C> C buildResponseDetailDto(Survey survey, SurveyResponse response, Class<C> clazz) {
        SurveyResponseMessageDto responseMessageDto = new SurveyResponseMessageDto(survey, response, response.getCells());
        responseMessageDto.getData().forEach(SurveyResponseCellMessageDto::convertText);
        return JsonHelper.getObjectMapper().convertValue(responseMessageDto, clazz);
    }
}
