package cn.hanyi.cem.event.consumer.utils;

import org.springframework.data.domain.Page;

import java.util.function.Consumer;
import java.util.function.Function;

public  class ForeachPageUtils {
    public static  <T> void foreachPage(Function<Integer, Page<T>> getPageList, Consumer<T> consumerItem) {
        int page = 0;
        do {
            Page<T> list = getPageList.apply(page);
            if (!list.isEmpty()) {
                list.forEach(i -> {
                    try {
                        consumerItem.accept(i);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                });
                page++;
            } else {
                return;
            }
        } while (true);
    }
}
