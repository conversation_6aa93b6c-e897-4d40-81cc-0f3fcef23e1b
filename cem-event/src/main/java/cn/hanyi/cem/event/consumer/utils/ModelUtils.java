package cn.hanyi.cem.event.consumer.utils;

import cn.hanyi.ctm.constant.SentimentType;
import cn.hanyi.survey.core.dto.CellTextLabel;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.befun.nlp.core.constant.Industry;
import org.befun.nlp.core.constant.SentimentLabel;
import org.befun.nlp.core.dto.TextClassificationLabelDto;
import org.befun.nlp.core.dto.TextClassificationResultDto;
import org.befun.nlp.core.dto.TextSentimentResultDto;
import org.befun.nlp.core.service.IModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class ModelUtils {

    @Autowired
    private IModelService iModelService;

    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;


    @PostConstruct
    public void init() {
        log.info("init SentimentUtils with iModelService:{}", iModelService.getClass().getName());
    }

    /**
     * 文本情感正负分析
     * {"model":"sentiment","version":"0.1","tokens":["差"],"sentiment":"NEGATIVE","label":"NEGATIVE","score":0.9937663}
     */
    public SentimentLabel predictSentimentLabel(String text, Industry industry) {
        try {
            TextSentimentResultDto textSentimentResultDto = iModelService.predictSentiment(industry, "sentiment", text, 0.1F);
            return textSentimentResultDto.getSentiment();
        } catch (Exception e) {
            log.error("predictSentimentLabel error", e);
        }
        return null;
    }


    public List<TextClassificationLabelDto> predictClassificationLabel(String text, Industry industry, List<String> additionalText) {
        try {

            TextClassificationResultDto textSentimentResultDto = iModelService.predictClassification(industry, "sentiment", text, "", additionalText, 0.1F);
            return textSentimentResultDto.getLabels();
        } catch (Exception e) {
            log.error("predictClassificationLabel error", e);
        }
        return null;

    }

    /**
     * 更新答题里文本情感正负
     * 0-NEGATIVE, 1-POSITIVE
     */
    public void updateCellSentiment(SurveyResponseCell cell) {
        log.info("update cell:{}", cell.getId());
        var text = cell.getStrValue();
        if (StringUtils.isNotEmpty(text)) {
            SentimentLabel predictLabel = predictSentimentLabel(text, Industry.FOOD);
            Optional.ofNullable(predictLabel).ifPresent(label -> {
                var sentimentType = SentimentType.valueOf(label.name());
                Optional.ofNullable(cell.getTextLabel()).ifPresentOrElse(
                        textLabel -> JsonHelper.toObject(textLabel, CellTextLabel.class).setSentiment(sentimentType.getFlag()),
                        () -> cell.setTextLabel(JsonHelper.toJson(new CellTextLabel(sentimentType.getFlag(), null)))
                );
                surveyResponseCellRepository.save(cell);
            });
        }
    }
}
