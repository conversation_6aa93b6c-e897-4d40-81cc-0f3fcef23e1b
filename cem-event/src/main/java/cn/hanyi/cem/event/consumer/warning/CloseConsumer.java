package cn.hanyi.cem.event.consumer.warning;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventWarningCloseDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyInboxDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyOutWorkerDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import cn.hanyi.ctm.dto.event.EventReceiverDto;
import cn.hanyi.ctm.dto.event.EventWarningDto;
import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.EventRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.extension.constant.InboxMessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 预警事件关闭事件
 */
@Component
@Slf4j
public class CloseConsumer implements IEventConsumer<EventWarningCloseDto> {

    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;

    @Autowired
    private WorkerProperties workerProperties;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private EventConsumerUtils eventConsumerUtils;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Override
    public List<EventType> types() {
        return List.of(EventType.WARNING_CLOSE);
    }


    @Override
    public ConsumerStatus consumer(CemEvent entity, EventWarningCloseDto param) {

        var event = eventRepository.findById(param.getWarningId()).orElse(null);

        if (event == null) {
            return ConsumerStatus.FAILED;
        }

        List<EventWarningDto> warnings = event.getWarnings();
        if (CollectionUtils.isNotEmpty(warnings)) {
            Long departmentId = event.getDepartmentId();
            Set<Long> roleIds = new HashSet<>();
            Set<String> notificationTypes = new HashSet<>();
            warnings.forEach(warning -> {
                if (warning.getRuleId() != null && warning.getRuleId() > 0) {
                    EventMonitorRules rule = eventMonitorRulesRepository.findById(warning.getRuleId()).orElse(null);
                    if (rule != null) {
                        // 预警规则的通知规则列表
                        List<EventReceiverDto> receivers = rule.getReceiver();
                        if (CollectionUtils.isNotEmpty(receivers)) {
                            receivers.forEach(receiver -> {
                                notificationTypes.addAll(receiver.getNotifyChannel().stream().map(NotificationType::name).collect(Collectors.toList()));
                                roleIds.addAll(receiver.getRoleIds());
                            });
                        }
                    }
                }
            });
            var current = userService.getSimple(param.getUserId()).orElse(null);
            if (!roleIds.isEmpty() && current != null) {
                closeNotify(param.getWarningId(), departmentId, roleIds, current, new ArrayList<>(notificationTypes));
            }
        }
        return ConsumerStatus.SUCCESS;
    }

    private void closeNotify(Long warningId, Long departmentId, Set<Long> roleIds, SimpleUser current, List<String> notificationTypes) {

        var app = workerProperties.getNotify().getApp();
        var template = workerProperties.getNotify().getClose();
        var warning = eventRepository.findById(warningId).orElse(null);
        if (warning == null) {
            return;
        }
        var title = String.format("%s关闭了一条%s:%s", current.getTruename(), warning.getWarningLevel().getText(), warning.getWarningTitle());

        var targetUrl = StringUtils.isNotEmpty(workerProperties.getEvent().getWarning().getEventUrl()) ? String.format("%s%s", workerProperties.getEvent().getWarning().getEventUrl(), warning.getId()) : "";
        // 通知特定的人 不需要部门和角色id
        var inboxDto = eventConsumerUtils.buildBaseParam(TaskNotifyInboxDto.class, warning.getOrgId(), departmentId, roleIds, null, warning.getId());
        inboxDto.setType(InboxMessageType.ACTION.name());
        inboxDto.setTargetUrl(targetUrl);
        inboxDto.setTitle(title);

        CompletableFuture.runAsync(() -> taskProducerHelper.notifyInbox(inboxDto, warning.getOrgId(), current.getId(), warning.getId(), null));

        var params = eventConsumerUtils.buildNotifyParam(current, warning, targetUrl, null);
        var outWorker = eventConsumerUtils.buildBaseParam(TaskNotifyOutWorkerDto.class, warning.getOrgId(), departmentId, roleIds, null, warning.getId());

        outWorker.setTypes(notificationTypes);
        outWorker.setApp(app);
        outWorker.setTemplate(template);
        outWorker.setParams(params);

        CompletableFuture.runAsync(() -> taskProducerHelper.notifyOutWork(outWorker, warning.getOrgId(), current.getId(), warning.getId(), null));


    }


}
