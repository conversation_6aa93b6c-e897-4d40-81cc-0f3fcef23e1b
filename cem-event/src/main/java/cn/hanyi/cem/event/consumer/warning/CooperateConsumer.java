package cn.hanyi.cem.event.consumer.warning;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventWarningCooperateDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyInboxDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyOutWorkerDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import cn.hanyi.ctm.repository.EventRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.extension.constant.InboxMessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 预警事件协作事件
 */
@Component
@Slf4j
public class CooperateConsumer implements IEventConsumer<EventWarningCooperateDto> {

    @Autowired
    private WorkerProperties workerProperties;

    @Autowired
    private EventConsumerUtils eventConsumerUtils;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Autowired
    private UserService userService;

    @Override
    public List<EventType> types() {
        return List.of(EventType.WARNING_COOPERATE);
    }


    @Override
    public ConsumerStatus consumer(CemEvent entity, EventWarningCooperateDto param) {

        var app = workerProperties.getNotify().getApp();
        var template = workerProperties.getNotify().getCooperation();
        var warning = eventRepository.findById(param.getWarningId()).orElse(null);
        if (warning == null) {
            return ConsumerStatus.FAILED;
        }
        var simpleUser = userService.getSimple(param.getFromUserId()).orElse(new SimpleUser(null, null, null, null, null, null));
        var targetUrl = StringUtils.isNotEmpty(workerProperties.getEvent().getWarning().getEventUrl()) ? String.format("%s%s", workerProperties.getEvent().getWarning().getEventUrl(), warning.getId()) : "";
        // 通知特定的人 不需要部门和角色id
        var inboxDto = eventConsumerUtils.buildBaseParam(TaskNotifyInboxDto.class, warning.getOrgId(), null, null, param.getToUserIds(), warning.getId());
        inboxDto.setResourceType(ResourcePermissionType.EVENT.name());
        inboxDto.setSourceId(warning.getId());
        inboxDto.setType(InboxMessageType.COOPERATION.name());
        inboxDto.setTargetUrl(targetUrl);
        inboxDto.setTitle(String.format("%s协作了一条%s:%s", simpleUser.getTruename(), warning.getWarningLevel().getText(), warning.getWarningTitle()));

        CompletableFuture.runAsync(() -> taskProducerHelper.notifyInbox(inboxDto, entity.getOrgId(), entity.getUserId(), warning.getId(), null));

        var params = eventConsumerUtils.buildNotifyParam(simpleUser, warning, targetUrl, Map.of("content", param.getMark()));
        var outWorker = eventConsumerUtils.buildBaseParam(TaskNotifyOutWorkerDto.class, warning.getOrgId(), null, null, param.getToUserIds(), warning.getId());

        outWorker.setResourceType(ResourcePermissionType.EVENT.name());
        outWorker.setSourceId(warning.getId());
        outWorker.setTypes(param.getTypes());
        outWorker.setApp(app);
        outWorker.setTemplate(template);
        outWorker.setParams(params);

        CompletableFuture.runAsync(() -> taskProducerHelper.notifyOutWork(outWorker, entity.getOrgId(), entity.getUserId(), warning.getId(), null));

        return ConsumerStatus.SUCCESS;
    }


}
