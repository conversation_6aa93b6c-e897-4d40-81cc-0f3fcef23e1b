package cn.hanyi.cem.event.consumer.warning;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventRuleChangeDto;
import cn.hanyi.cem.core.dto.event.EventWarningCloseDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyInboxDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyOutWorkerDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import cn.hanyi.ctm.dto.event.EventReceiverDto;
import cn.hanyi.ctm.dto.event.EventWarningDto;
import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.repository.EventRuleRelationRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.extension.constant.InboxMessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.hanyi.cem.event.consumer.utils.ForeachPageUtils.foreachPage;

/**
 * 预警事件关闭事件
 */
@Component
@Slf4j
public class RuleChangeConsumer implements IEventConsumer<EventRuleChangeDto> {

    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;
    @Autowired
    private EventRuleRelationRepository eventRuleRelationRepository;
    @Autowired
    private EventRepository eventRepository;


    @Override
    public List<EventType> types() {
        return List.of(EventType.RULE_CHANGE);
    }


    @Override
    public ConsumerStatus consumer(CemEvent entity, EventRuleChangeDto param) {

        eventMonitorRulesRepository.findById(param.getWarningId()).ifPresent(this::updateEventRuleTitle);

        return ConsumerStatus.SUCCESS;
    }

    private void updateEventRuleTitle(EventMonitorRules eventMonitorRules) {
        foreachPage(
                page-> eventRuleRelationRepository.findAll((r,q,c)->{q.where(c.equal(r.get("ruleId"),eventMonitorRules.getId()));return q.getRestriction();}, PageRequest.of(page, 500)),
                relation->{
                    eventRepository.findById(relation.getEventId()).ifPresent(
                            event -> {
                                Optional.ofNullable(event.getWarnings())
                                        .flatMap(warnings -> warnings.stream().filter(w -> w.getRuleId().equals(eventMonitorRules.getId())).map(warnings::indexOf).findFirst())
                                        .ifPresent(index -> {
                                            String eventTitle = event.getWarningTitle();
                                            String[] spTitle = eventTitle.split(";");
                                            spTitle[index] = eventMonitorRules.getTitle();
                                            event.setWarningTitle(StringUtils.join(spTitle, ";"));
                                            eventRepository.save(event);
                                        });
                            }
                    );
                });
    }

}
