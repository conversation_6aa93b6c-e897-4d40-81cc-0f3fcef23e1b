package cn.hanyi.cem.event.consumer.wechatopen;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventWechatOpenAuthorizeDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.service.CustomerWechatService;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.OrganizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class WechatOpenAuthorizeEventConsumer implements IEventConsumer<EventWechatOpenAuthorizeDto> {

    @Autowired
    private CustomerWechatService customerWechatService;
    @Autowired
    private OrganizationService organizationService;

    @Override
    public List<EventType> types() {
        return List.of(EventType.WECHAT_OPEN_AUTHORIZE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventWechatOpenAuthorizeDto param) {
        // 同步模版
        customerWechatService.asyncTemplate(entity.getOrgId(), entity.getUserId(), param.getThirdpartyAuthId());
        // 同步客户 1.9.7 不自动同步微信客户
//        if (organizationService.enableAutoSyncCustomer(entity.getOrgId())) {
//            customerWechatService.asyncCustomer(entity.getOrgId(), entity.getUserId(), param.getThirdpartyAuthId(), false);
//        }
        return ConsumerStatus.SUCCESS;
    }

}
