package cn.hanyi.cem.event.consumer.wechatopen;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventWechatOpenDeleteDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.repository.CustomerRepository;
import cn.hanyi.ctm.repository.ThirdPartyCustomerRepository;
import cn.hanyi.ctm.service.ThirdPartyTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.entity.BaseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WechatOpenDeleteEventConsumer implements IEventConsumer<EventWechatOpenDeleteDto> {

    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;
    @Autowired
    private ThirdPartyCustomerRepository thirdPartyCustomerRepository;
    @Autowired
    private CustomerRepository customerRepository;

    @Override
    public List<EventType> types() {
        return List.of(EventType.WECHAT_OPEN_DELETE);
    }

    @Override
    @Transactional
    public ConsumerStatus consumer(CemEvent entity, EventWechatOpenDeleteDto param) {
        // 删除模版
        thirdPartyTemplateService.deleteWechatTemplate(param.getOrgId(), param.getThirdpartyAuthId());
        // 删除客户
        if (param.isDeleteUser()) {
            long orgId = entity.getOrgId();
            long thirdpartyAuthId = param.getThirdpartyAuthId();
            boolean hasNext = true;
            while (hasNext) {
                List<ThirdPartyCustomer> list = thirdPartyCustomerRepository.findByOrgIdAndThirdpartyAuthId(orgId, thirdpartyAuthId, PageRequest.of(0, 100));
                if (CollectionUtils.isNotEmpty(list)) {
                    List<Long> ids = list.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    customerRepository.deleteByOrgIdAndThirdPartyCustomerIdIn(orgId, ids);
                    thirdPartyCustomerRepository.deleteAll(list);
                } else {
                    hasNext = false;
                }
            }
        }
        return ConsumerStatus.SUCCESS;
    }

}
