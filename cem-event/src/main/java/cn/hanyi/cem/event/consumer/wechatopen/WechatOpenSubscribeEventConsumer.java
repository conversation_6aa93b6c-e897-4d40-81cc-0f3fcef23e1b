package cn.hanyi.cem.event.consumer.wechatopen;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.dto.event.EventWechatOpenSubscribeDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.event.consumer.IEventConsumer;
import cn.hanyi.ctm.service.CustomerWechatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.ThirdPartyAuthService;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class WechatOpenSubscribeEventConsumer implements IEventConsumer<EventWechatOpenSubscribeDto> {

    @Autowired
    private AuthWechatOpenService authWechatOpenService;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    @Autowired
    private CustomerWechatService customerWechatService;

    @Override
    public List<EventType> types() {
        return List.of(EventType.WECHAT_OPEN_SUBSCRIBE, EventType.WECHAT_OPEN_UNSUBSCRIBE);
    }

    @Override
    public ConsumerStatus consumer(CemEvent entity, EventWechatOpenSubscribeDto param) {
        // 关注
        // 取消关注
        List<ThirdPartyAuth> list = thirdPartyAuthService.getListBySource(authWechatOpenService.buildSource(param.getAppId()), ThirdPartyAuthType.WECHAT_OPEN, "cem");
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(i -> {
                WechatOpenConfig config = authWechatOpenService.getConfig(i);
                if (config != null && config.isAuthorized()) {
                    customerWechatService.asyncSingleCustomer(i.getOrgId(), null, config.getConfigId(), config.getAppId(), param.getOpenId());
                }
            });
        }
        return ConsumerStatus.SUCCESS;
    }

}
