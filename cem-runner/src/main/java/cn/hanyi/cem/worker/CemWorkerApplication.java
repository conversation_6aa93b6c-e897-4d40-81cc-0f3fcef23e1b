package cn.hanyi.cem.worker;

import cn.hanyi.common.ip.resolver.IpResolverAutoConfiguration;
import org.befun.auth.AuthAutoConfiguration;
import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.befun.extension.XPackAutoConfiguration;
import org.befun.task.TaskAutoConfiguration;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.sql.DataSource;
import java.util.TimeZone;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@ComponentScan(basePackages = {"cn.hanyi", "org.befun.nlp", "org.befun.core", "cn.hanyi.cem.core", "org.befun.bi"})
@SpringBootApplication(exclude = {IpResolverAutoConfiguration.class})
@EnableAsync(proxyTargetClass = true)
@EnableScheduling
public class CemWorkerApplication implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
    }

    @Configuration
    @EntityScan({
            "cn.hanyi.ctm.entity",
            "org.befun.core.entity",
            "cn.hanyi.survey.core.entity",
            "cn.hanyi.cem.core.entity",
            "org.befun.task.entity",
            "org.befun.bi.entity",
            "org.befun.nlp.core.entity",
            XPackAutoConfiguration.PACKAGE_ENTITY,
            TaskAutoConfiguration.PACKAGE_ENTITY,
            AuthAutoConfiguration.PACKAGE_ENTITY,
    })
    @EnableJpaRepositories(basePackages = {
            "cn.hanyi.ctm.repository",
            "org.befun.core.repo",
            "cn.hanyi.cem.core.repository",
            "org.befun.task.repository",
            "cn.hanyi.survey.core.repository",
            "org.befun.bi.repository",
            "org.befun.nlp.core.repository",
            XPackAutoConfiguration.PACKAGE_REPOSITORY,
            TaskAutoConfiguration.PACKAGE_REPOSITORY,
            AuthAutoConfiguration.PACKAGE_REPOSITORY,
    }, repositoryBaseClass = BaseRepositoryImpl.class)
    public class BaseJPAConfig {

    }

    @Bean("dataSource")
    @ConfigurationProperties(prefix = "spring.datasource")
    @Primary
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    public ExecutorService getThreadPool() {
        return new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),
                2 * Runtime.getRuntime().availableProcessors(), 10, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>());
    }

    public static void main(String[] args) {
        SpringApplication.run(CemWorkerApplication.class, args);
    }

}
