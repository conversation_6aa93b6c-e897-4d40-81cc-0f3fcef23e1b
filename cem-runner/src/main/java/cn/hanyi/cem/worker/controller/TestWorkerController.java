package cn.hanyi.cem.worker.controller;

import cn.hanyi.cem.core.dto.task.TaskNotifyWarningJourneyDto;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.worker.dataaccess.DataAccessBackupHelper;
import cn.hanyi.cem.worker.database.DatabaseTrimHelper;
import cn.hanyi.cem.worker.journeywarning.JourneyWarningWorker;
import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.JourneyWarningPublish;
import cn.hanyi.ctm.repository.JourneyWarningPublishRepository;
import cn.hanyi.ctm.service.EventNotifyService;
import cn.hanyi.ctm.service.JourneyWarningService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Tag(name = "测试")
@Slf4j
@Validated
@RestController
@RequestMapping("test")
public class TestWorkerController {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private JourneyWarningService journeyWarningService;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Autowired(required = false)
    private List<JourneyWarningWorker> journeyWarningWorkers;

    @Autowired
    private JourneyWarningPublishRepository journeyWarningPublishRepository;

    @Autowired
    private EventNotifyService eventNotifyService;

    @Autowired
    private DataAccessBackupHelper dataAccessBackupHelper;
    @Autowired
    private DatabaseTrimHelper databaseTrimHelper;

    private final ExecutorService executorService = Executors.newFixedThreadPool(8);
    private final HashMap<Long, Boolean> lock = new HashMap<>();

    @Operation(summary = "数据接入消息备份")
    @GetMapping("dataAccess/backup")
    public String dataAccessBackup() {
        dataAccessBackupHelper.backup();
        return "success";
    }

    @Operation(summary = "表数据裁剪")
    @GetMapping("database/trim")
    public String databaseTrim() {
        databaseTrimHelper.trim();
        return "success";
    }

    @Operation(summary = "触发旅程预警的定时任务")
    @GetMapping("notify/trigger")
    public String triggerNotify(@NotNull @Min(1) Long journeyMapId) {
        run(journeyMapId);
        return String.format("旅程 %d 开始计算，并通知", journeyMapId);
    }

    @Operation(summary = "重置旅程预警")
    @GetMapping("notify/reset")
    public String resetNotify(@NotNull @Min(1) Long journeyMapId) {
        synchronized (this) {
            if (lock.containsKey(journeyMapId)) {
                Boolean success = lock.get(journeyMapId);
                if (success) {
                    lock.remove(journeyMapId);
                    return String.format("旅程 %d 已重置", journeyMapId);
                } else {
                    return String.format("旅程 %d 正在计算中", journeyMapId);
                }
            }
        }
        return String.format("旅程 %d 已重置", journeyMapId);
    }

    @Operation(summary = "清空缓存")
    @GetMapping("cache/clear")
    public String clearCache() {
        Set<String> keys = stringRedisTemplate.opsForSet().members("stat:all-keys");
        if (CollectionUtils.isNotEmpty(keys)) {
            stringRedisTemplate.delete(keys);
        }
        stringRedisTemplate.delete(List.of(
                "stat:all-keys",
                "journey-warning:value",
                "journey-warning:notify:event_stat",
                "journey-warning:notify:experience_indicator"));
        return "ok";
    }

    public void run(Long journeyMapId) {
        if (CollectionUtils.isNotEmpty(journeyWarningWorkers)) {
            journeyWarningWorkers.forEach(i -> i.cacheData(journeyMapId));
        }
        run2(journeyMapId);
    }

    public void run2(Long journeyMapId) {
        var typeList = List.of(JourneyComponentType.experience_indicator, JourneyComponentType.event_stat);
        typeList.forEach(type -> {
            journeyWarningService.consumerWarningNotify(type, i -> journeyMapId.equals(i.getJourneyMapId()), (warning, point) -> {
                if (isWarning(warning, point)) {
                    log.info("发送通知,id={}, value={}, type={}", warning.getId(), point, type.name());
                    taskProducerHelper.journeyWarningNotify(new TaskNotifyWarningJourneyDto(warning.getId(), point));
                }
            });
        });
    }

    public Boolean isWarning(JourneyWarningPublish warning, Double point) {
        return warning.getEnableWarning() && warning.getEnableNotify() && warning.getWarningCompare().compare(point, warning.getWarningValue());
    }

}
