package cn.hanyi.cem.worker.controller;

import cn.hanyi.cem.core.constant.WorkerStatus;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.producer.WorkerAddToTaskHelper;
import cn.hanyi.cem.core.repository.CemEventRepository;
import cn.hanyi.cem.core.repository.CemTaskRepository;
import cn.hanyi.cem.worker.runners.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.bi.entity.DashboardPush;
import org.befun.bi.repository.DashboardPushRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@Tag(name = "手动")
@Slf4j
@Validated
@RestController
@RequestMapping("trigger")
public class WorkerController {

    @Autowired(required = false)
    private JourneyWarningScheduling journeyWarningScheduling;

    @Autowired
    private CustomerStatScheduling customerStatScheduling;

    @Autowired
    private WorkerAddToTaskHelper workerAddToTaskHelper;
    @Autowired
    private CemTaskRepository cemTaskRepository;
    @Autowired
    private CemEventRepository cemEventRepository;
    @Autowired(required = false)
    private ChuanglanSmsCallbackScheduling chuanglanSmsCallbackScheduling;
    @Autowired
    private BiDashboardPushScheduling dashboardPushScheduling;
    @Autowired
    private DashboardPushRepository dashboardPushRepository;
    @Autowired
    private SendManageTimerScheduling sendManageTimerScheduling;
    @Autowired(required = false)
    private EventGroupTimerScheduling eventGroupTimerScheduling;
    @Autowired
    private SendManageRemindScheduling sendManageRemindScheduling;

    @Operation(summary = "重置task")
    @GetMapping("resetTask")
    public void resetTask(long taskId) {
        CemTask task = cemTaskRepository.findById(taskId).orElseThrow();
        task.setStatus(WorkerStatus.RESET);
        cemTaskRepository.save(task);
        workerAddToTaskHelper.addToTask(new WorkerAddToTaskHelper.SimpleWorker(taskId, true, false, null, task.getType()));
    }

    @Operation(summary = "重置event")
    @GetMapping("resetEvent")
    public void resetEvent(long eventId) {
        CemEvent event = cemEventRepository.findById(eventId).orElseThrow();
        event.setStatus(WorkerStatus.RESET);
        cemEventRepository.save(event);
        workerAddToTaskHelper.addToTask(new WorkerAddToTaskHelper.SimpleWorker(eventId, true, false, null, event.getType()));
    }


    @Operation(summary = "手动触发旅程预警")
    @GetMapping("journey")
    public void triggerNotify() {
        journeyWarningScheduling.scheduledJourney();
    }


    @Operation(summary = "手动触发客户统计")
    @GetMapping("customer")
    public void trigger() {
        customerStatScheduling.scheduledCustomer();
    }

    @Operation(summary = "手动触发创蓝短信推送结果")
    @GetMapping("chuanglanSmsCallback")
    public void chuanglanSmsCallback() {
        chuanglanSmsCallbackScheduling.scheduling();
    }

    @Operation(summary = "手动触发创蓝短信推送结果")
    @GetMapping("dashboardPush/{id}")
    public void pushDashboard(@PathVariable Long id) {
        Optional<DashboardPush> dashboardPush = dashboardPushRepository.findById(id);
        dashboardPush.ifPresent(push -> dashboardPushScheduling.pushNotifyTask(push));
    }

    @Operation(summary = "手动触发送管理")
    @GetMapping("sendManageTimer")
    public void triggerSendManageTimer() {
        sendManageTimerScheduling.scheduling();
    }

    @Operation(summary = "手动触事件分组重复任务")
    @GetMapping("eventGroupTimer")
    public void EventGroupTimerScheduling() {
        eventGroupTimerScheduling.scheduling();
    }

    @Operation(summary = "手动触发送管理催答任务")
    @GetMapping("sendManageRemind/{id}")
    public void triggerSendManageRemind(@PathVariable Long id) {
        sendManageRemindScheduling.triggerSendManageRemind(id);
    }

    @Operation(summary = "手动触发送管理催答任务")
    @GetMapping("sendManageRemindScheduling")
    public void triggerSendManageRemindScheduling() {
        sendManageRemindScheduling.scheduling();
    }
}
