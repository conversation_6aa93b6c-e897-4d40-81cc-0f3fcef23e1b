package cn.hanyi.cem.worker.controller;

import cn.hanyi.cem.core.constant.EventType;
import cn.hanyi.cem.core.constant.WorkerStatus;
import cn.hanyi.cem.core.dto.event.*;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.producer.EventProducerHelper;
import cn.hanyi.cem.core.repository.CemEventRepository;
import cn.hanyi.cem.event.consumer.response.webhook.FinalSubmitWebhookConsumer;
import cn.hanyi.cem.event.consumer.survey.redpack.RefundRedPack;
import cn.hanyi.cem.event.consumer.survey.redpack.SendRedPackDelay;
import cn.hanyi.cem.worker.dto.RePushFinalSubmitWebhookDto;
import cn.hanyi.cem.worker.dto.SubmitWebhookDto;
import cn.hanyi.cem.worker.runners.JourneyWarningScheduling;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeWinnerRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.dto.order.RedPacketOrderRefundRequestDto;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Tag(name = "手动")
@Slf4j
@Validated
@RestController
@RequestMapping("event")
public class WorkerEventController {

    @Autowired(required = false)
    private JourneyWarningScheduling journeyWarningScheduling;

    @Autowired
    private EventProducerHelper eventProducerHelper;

    @Autowired
    private FinalSubmitWebhookConsumer finalSubmitWebhookConsumer;

    @Autowired
    private CemEventRepository cemEventRepository;

    @Autowired
    private RefundRedPack refundRedPack;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private OrganizationOrderService organizationOrderService;

    @Autowired
    private SurveyLotteryPrizeWinnerRepository winnerRepository;

    @Autowired
    private SendRedPackDelay sendRedPackDelay;

//    @Operation(summary = "问卷创建事件")
//    @PostMapping("survey/create")
//    public void triggerCreate(@RequestBody EventSurveyDto dto) {
//        var event = new CemEvent();
//        event.setContent(JsonHelper.toJson(dto));
//        event.setType(EventType.SURVEY_CREATE);
//
//        CemEvent cemEvent = (CemEvent) eventService.save(event);
//
//        var worker = new WorkerDto();
//        worker.setWorkerId(cemEvent.getId());
//        worker.setType(WorkerType.EVENT);
//
//        eventService.addWorkerEvent(worker);
//
//    }

    @Operation(summary = "问卷更新事件")
    @PostMapping("survey/update")
    public void triggerUpdate(@RequestBody EventSurveyDto dto) {

        eventProducerHelper.surveyUpdate(dto.getSurveyId());
    }

    @Operation(summary = "问卷启用事件")
    @PostMapping("survey/enable")
    public void triggerEnable(@RequestBody EventSurveyDto dto) {

        eventProducerHelper.surveyPublish(dto.getSurveyId());
    }

    @Operation(summary = "答卷提交事件")
    @PostMapping("response/submit")
    public void triggerSubmit(
            @RequestParam  Long orgId,
            @RequestBody EventResponseDto dto
    ) {
        eventProducerHelper.responseSubmitFinal(orgId, dto.getSurveyId(), dto.getResponseId(), dto.isFinalSubmit());
    }

    @Operation(summary = "手动触发用户注册")
    @PostMapping("register")
    public void register(@RequestBody EventUserRegisterDto dto) {
        eventProducerHelper.userRegister(dto.getOrgId(), dto.getUserId(), dto.getPassword(), dto.getCompanyName(), dto.getCustomer());
    }

    @Operation(summary = "答卷提交事件(webhook)")
    @PostMapping("response/submit/webhook")
    public void triggerSubmitWebHook(@RequestBody SubmitWebhookDto dto) {
        if (dto != null && !dto.getIds().isEmpty()) {
            dto.getIds().forEach(id -> {
                Optional<CemEvent> optional = cemEventRepository.findById(id);
                optional.ifPresent(event -> {
                    if (event.getType() == EventType.RESPONSE_SUBMIT_FINAL) {
                        EventResponseDto eventResponseDto = JsonHelper.toObject(event.getContent(), EventResponseDto.class);
                        finalSubmitWebhookConsumer.consumer(event, eventResponseDto);
                    }
                });
            });
        }
    }

    @Operation(summary = "批量答卷提交事件(webhook)")
    @PostMapping("re-push-webhook")
    public void rePush(@RequestBody RePushFinalSubmitWebhookDto dto) {
        if (dto != null && !dto.getIds().isEmpty()) {
            dto.getIds().forEach(id -> {
                Optional<CemEvent> optional = cemEventRepository.findTopByTypeAndSource(dto.getType(), dto.getSourceType() + ":" + id.toString());
                optional.ifPresent(event -> {
                    if (event.getType() == EventType.RESPONSE_SUBMIT_FINAL) {
                        EventResponseDto eventResponseDto = JsonHelper.toObject(event.getContent(), EventResponseDto.class);
                        finalSubmitWebhookConsumer.consumer(event, eventResponseDto);
                    }
                });
            });
        }
    }

    // CemEvent cemEvent, EventRefundRedPackDto dto
    @Operation(summary = "手动触发退款")
    @PostMapping("refund-red-pack/{eventId}")
    public void refund(@PathVariable Long eventId, @RequestBody EventRefundRedPackDto dto) {
        Optional<CemEvent> optional = cemEventRepository.findById(eventId);
        refundRedPack.consumer(optional.get(), dto);
    }

    @Operation(summary = "手动微信红包退款")
    @PostMapping("refund-red-pack")
    public void refund() {
            cemEventRepository.findByTypeAndStatus(EventType.REFUND_RED_PACK, WorkerStatus.FAILED).forEach(event -> {
                try {
                    log.info("开始退款 eventId:{}", event.getId());
                    EventRefundRedPackDto content = JsonHelper.toObject(event.getContent(), EventRefundRedPackDto.class);
                    surveyRepository.findById(content.getSurveyId()).ifPresent(survey -> {
                        winnerRepository.findById(content.getWinnerId()).ifPresent(winner -> {
                            organizationOrderService.refundOrder(event.getOrgId(), null, OrderType.order_red_packet,
                                    new RedPacketOrderRefundRequestDto(
                                            content.getOrderId(),
                                            content.getLotteryId(),
                                            winner.getAmount(),
                                            content.getWinnerId().toString(),
                                            "红包返还:" + survey.getTitle()));
                            winner.setStatus(PrizeSendStatus.REFUND);
                            winnerRepository.save(winner);
                            log.info("orgId:{} 退款成功 evenId，{}", event.getOrgId(), event.getId());
                            event.setStatus(WorkerStatus.SUCCESS);
                            cemEventRepository.save(event);
                        });
                    });
                }catch (Exception e){
                    log.error("退款异常", e);
                }
                });

    }

    @Operation(summary = "手动发送延迟红包")
    @PostMapping("send-red-pack-delay")
    public void sendRedPackDelay(@RequestBody EventSendRedPackDelayDto dto) {
        sendRedPackDelay.consumer(null, dto);
    }

}
