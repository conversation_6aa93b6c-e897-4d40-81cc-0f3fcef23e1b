package cn.hanyi.cem.worker.database;

import cn.hanyi.cem.worker.property.DatabaseTrimProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class DatabaseTrimHelper {

    @Lazy
    @Autowired
    private DatabaseTrimHelper self;
    @Autowired
    private DatabaseTrimProperties properties;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    public void trim() {
        trimByMaxId();
        trimByCreateTime();
    }

    private void trimByCreateTime() {
        List<DatabaseTrimProperties.TrimInfo> trimByCreateTime = properties.getTrimByCreateTime();
        if (CollectionUtils.isEmpty(trimByCreateTime)) {
            return;
        }
        trimByCreateTime.forEach(i -> {
            int retainDays = i.getRetainDays() == null ? properties.getRetainDays() : i.getRetainDays();
            LocalDateTime maxCreateTime = maxCreateTime(retainDays);
            if (maxCreateTime == null) {
                log.info("table={}, maxCreateTime=null, 不删除任何数据", i.getTrimTable());
                return;
            }
            // 校验当前数据库的最大 id 的 createTime，不能小于 maxCreateTime
            IdCreateTime lastOneData = findLastOneData(i.getTrimTable());
            if (lastOneData == null || lastOneData.getCreateTime() == null || maxCreateTime.isAfter(lastOneData.getCreateTime())) {
                log.info("table={}, 最新一条数据的 createTime 不符合要求，不删除任何数据", i.getTrimTable());
                return;
            }

            int maxLoop = 1000;
            int loop = 0;
            int count = 0;
            String nextCreateTimeSql = "select id, create_time createTime from " + i.getTrimTable() + " order by id asc limit 10000,1";
            String trimSql = "delete from " + i.getTrimTable() + " where id < ?";
            while (loop < maxLoop) {
                IdCreateTime idCreateTime = findOneIdCreateTime(nextCreateTimeSql);
                if (idCreateTime == null
                        || idCreateTime.getId() == null
                        || idCreateTime.getCreateTime() == null
                        || idCreateTime.getCreateTime().isAfter(maxCreateTime)) {
                    break;
                }
                count += self.trim(idCreateTime.getId(), trimSql);
                loop++;
            }
            log.info("已删除 createTime 小于 {} 的数据 {} 条, 循环次数 {} : {}", maxCreateTime, count, loop, trimSql);
        });
    }

    private void trimByMaxId() {
        List<DatabaseTrimProperties.TrimInfo> trimByMaxId = properties.getTrimByMaxId();
        if (CollectionUtils.isEmpty(trimByMaxId)) {
            return;
        }
        trimByMaxId.forEach(i -> {
            int retainDays = i.getRetainDays() == null ? properties.getRetainDays() : i.getRetainDays();
            long maxId = maxId(retainDays);
            if (maxId <= 0) {
                log.info("table={}, trimMaxId=0, 不删除任何数据", i.getTrimTable());
                return;
            }
            // 校验当前数据库的最大 id，不能小于 maxId
            IdCreateTime lastOneData = findLastOneData(i.getTrimTable());
            if (lastOneData == null || maxId >= lastOneData.getId()) {
                log.info("table={}, 最新一条数据的 id 不符合要求，不删除任何数据", i.getTrimTable());
                return;
            }
            // 删除小于 maxId 的数据
            String sql = "delete from " + i.getTrimTable() + " where id < ? ";
            if (StringUtils.isNotEmpty(i.getTrimCondition())) {
                sql += " and " + i.getTrimCondition();
            }
            int count = self.trim(maxId, sql);
            log.info("已删除 id 小于 {} 的数据 {} 条: {}", maxId, count, sql);
        });
    }

    @Transactional
    public int trim(Long maxId, String sql) {
        return jdbcTemplate.update((con -> {
            PreparedStatement statement = con.prepareStatement(sql);
            statement.setLong(1, maxId);
            return statement;
        }));
    }

    @Getter
    @Setter
    public static class IdCreateTime {
        private Long id;
        private LocalDateTime createTime;
    }

    public IdCreateTime findLastOneData(String table) {
        String lastOneDataSql = "select id, create_time createTime from " + table + " order by id desc limit 1";
        return findOneIdCreateTime(lastOneDataSql);
    }

    public IdCreateTime findOneIdCreateTime(String sql) {
        List<IdCreateTime> list = jdbcTemplate.query(sql, (rs, rowNum) -> {
            IdCreateTime item = new IdCreateTime();
            item.setId(rs.getLong("id"));
            item.setCreateTime(rs.getTimestamp("createTime").toLocalDateTime());
            return item;
        });
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    private LocalDateTime maxCreateTime(int retainDays) {
        if (retainDays < 7) {
            // 保留时间不能小于7天
            return null;
        }
        return LocalDate.now().minusDays(retainDays).atStartOfDay();
    }

    private long maxId(int retainDays) {
        if (retainDays < 7) {
            // 保留时间不能小于7天
            return 0;
        }
        LocalDate maxDate = LocalDate.now().minusDays(retainDays);
        long ms = DateHelper.toDate(maxDate).getTime();
        return (ms - 1619827200000L) << 16;
    }
}
