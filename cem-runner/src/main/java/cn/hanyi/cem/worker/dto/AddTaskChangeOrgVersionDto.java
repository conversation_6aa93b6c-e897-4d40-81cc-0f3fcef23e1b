package cn.hanyi.cem.worker.dto;

import cn.hanyi.cem.core.dto.task.TaskOrgChangeDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.AppVersion;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class AddTaskChangeOrgVersionDto {
    @NotNull
    private Long orgId;
    @NotNull
    private AppVersion fromVersion;
    @NotNull
    private AppVersion toVersion;

    public TaskOrgChangeDto mapToParam() {
        return new TaskOrgChangeDto(orgId, fromVersion.name(), toVersion.name());
    }
}
