package cn.hanyi.cem.worker.journeywarning;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.CalculatingFilterDto;
import cn.hanyi.ctm.dto.stat.CacheStatEventDataDto;
import cn.hanyi.ctm.dto.stat.CacheStatEventParamDto;
import cn.hanyi.ctm.entity.journey.ElementEventStatPublish;
import cn.hanyi.ctm.entity.journey.JourneyWarningPublish;
import cn.hanyi.ctm.service.journey.elements.eventstat.JourneyEventStatPublishService;
import cn.hanyi.ctm.service.stat.CacheStatEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

@Service
public class JourneyWarningEventStatWorker extends JourneyWarningWorker {

    @Autowired
    private CacheStatEventService cacheStatEventService;
    @Autowired
    private JourneyEventStatPublishService journeyEventStatPublishService;

    @Override
    public JourneyComponentType getRelationType() {
        return JourneyComponentType.event_stat;
    }

    @Override
    public Double cacheDateRange(JourneyWarningPublish warning, LocalDate start, LocalDate end) {
        ElementEventStatPublish entity = journeyEventStatPublishService.get(warning.getRelationId());
        if (entity == null) {
            return null;
        }
        CacheStatEventParamDto param = new CacheStatEventParamDto(warning.getOrgId(), entity.getEventRuleId(), entity.getStatType());
        List<CacheStatEventDataDto> list = cacheStatEventService.getData(param, start, end, new CalculatingFilterDto());
        if (list == null) {
            return null;
        }
        if (list.isEmpty()) {
            return 0.0;
        } else {
            return list.stream().map(CacheStatEventDataDto::getCount).filter(Objects::nonNull).mapToDouble(Integer::doubleValue).sum();
        }
    }
}
