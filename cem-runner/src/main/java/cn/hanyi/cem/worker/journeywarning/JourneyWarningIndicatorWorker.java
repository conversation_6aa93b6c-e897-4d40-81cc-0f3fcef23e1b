package cn.hanyi.cem.worker.journeywarning;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.CalculatingFilterDto;
import cn.hanyi.ctm.dto.journey.IndicatorDataResponseDto;
import cn.hanyi.ctm.dto.stat.CacheStatIndicatorParamDto;
import cn.hanyi.ctm.entity.journey.ExperienceIndicatorBase;
import cn.hanyi.ctm.entity.journey.JourneyWarningPublish;
import cn.hanyi.ctm.service.data.IndicatorDataService;
import cn.hanyi.ctm.service.stat.CacheStatIndicatorService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class JourneyWarningIndicatorWorker extends JourneyWarningWorker {

    @Autowired
    private CacheStatIndicatorService cacheStatIndicatorService;
    @Autowired
    private IndicatorDataService indicatorService;

    @Override
    public JourneyComponentType getRelationType() {
        return JourneyComponentType.experience_indicator;
    }

    @Override
    public Double cacheDateRange(JourneyWarningPublish warning, LocalDate start, LocalDate end) {
        ExperienceIndicatorBase indicator = indicatorService.getEntity(warning.getRelationId(), true);
        if (indicator == null || StringUtils.isEmpty(indicator.getCalculatingMethod())) {
            return null;
        }
        List<CacheStatIndicatorParamDto> params = indicatorService.parseParams(indicator, null);
        CalculatingFilterDto calculatingFilterDto = indicator.getCalculatingFilter();
        if (CollectionUtils.isEmpty(params)) {
            return null;
        }
        String type = indicator.getCalculatingMethod();
        Double current = null;
        if (type.equalsIgnoreCase("average")) {
            current = indicatorService.calculateAvg(params.get(0), start, end, calculatingFilterDto).getAllData();
        } else if (type.equalsIgnoreCase("NSS")) {
            current = indicatorService.calculateNps(params.get(0), start, end, calculatingFilterDto).getAllData();
        } else if (type.equalsIgnoreCase("NPS")) {
            current = indicatorService.calculateNps(params.get(0), start, end, calculatingFilterDto).getAllData();
        } else if (type.equalsIgnoreCase("percent")) {
            current = indicatorService.calculatePercent(params.get(0), start, end, calculatingFilterDto).getAllData();
        } else if (type.equalsIgnoreCase("WeightAvg")) {
            current = indicatorService.calculateWeightAvg(params, start, end, calculatingFilterDto).getAllData();
        }
        return IndicatorDataResponseDto.formatDouble(current);
    }
}
