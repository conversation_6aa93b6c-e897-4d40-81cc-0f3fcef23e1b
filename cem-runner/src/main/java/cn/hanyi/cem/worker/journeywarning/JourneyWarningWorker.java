package cn.hanyi.cem.worker.journeywarning;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.constant.warning.IndicatorCompare;
import cn.hanyi.ctm.constant.warning.IndicatorWarningFrequency;
import cn.hanyi.ctm.entity.journey.JourneyWarningPublish;
import cn.hanyi.ctm.service.JourneyWarningService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Slf4j
public abstract class JourneyWarningWorker {

    @Autowired
    private JourneyWarningService journeyWarningService;

    public abstract JourneyComponentType getRelationType();

    protected abstract Double cacheDateRange(JourneyWarningPublish warning, LocalDate start, LocalDate end);

    // 查询出所有开启了预警的指标
    // 把这些指标的历史数据缓存起来(通过指标规则计算需要缓存的所有日期)
    // 如果传入了指定的 journeyMapId 则只计算这个旅程的预警数据
    public void cacheData(Long journeyMapId) {
        int page = 0;
        int size = 100;
        boolean hasNext = true;
        while (hasNext) {
            List<JourneyWarningPublish> warnings = journeyWarningService.getPublishEnableWarnings(journeyMapId, getRelationType(), page, size);
            if (CollectionUtils.isNotEmpty(warnings)) {
                warnings.forEach(i -> {
                    if (journeyWarningService.isValid(i)) {
                        IndicatorWarningFrequency frequency = i.getWarningFrequency();
                        if (frequency != null) {
                            int range = Optional.ofNullable(i.getWarningRange()).orElse(-1);
                            if (range >= 0) {
                                cacheWarning(i, frequency, range);
                            }
                        }
                    }
                });
            } else {
                hasNext = false;
            }
            page++;
        }
    }

    private void cacheWarning(JourneyWarningPublish warning, IndicatorWarningFrequency frequency, int range) {
        LocalDate[] warningDate = journeyWarningService.getWarningDate(frequency, range);
        Double value = null;
        try {
            value = cacheDateRange(warning, warningDate[0], warningDate[1]);
        } catch (Exception e) {
            log.error("cache warning error, warningId: {}, frequency: {}, range: {}", warning.getId(), frequency, range, e);
        }
        if (value == null) {
            return;
        }
        journeyWarningService.cacheWarningValue(warning.getId(), value);

        // 缓存需要通知的预警规则
        Boolean enableNotify = warning.getEnableNotify();
        IndicatorCompare warningCompare = warning.getWarningCompare(); // 预警规则：0 低于 1 高于
        Double warningValue = warning.getWarningValue();
        if (enableNotify != null && warningCompare != null && warningValue != null && enableNotify) {
            boolean notify = false;
            if (IndicatorCompare.LT.equals(warningCompare)) {
                if (value < warningValue) {
                    notify = true;
                }
            } else if (IndicatorCompare.GT.equals(warningCompare)) {
                if (value > warningValue) {
                    notify = true;
                }
            }
            if (notify) {
                journeyWarningService.cacheWarningNotify(warning.getId(), value, getRelationType());
            }
        }
    }

}
