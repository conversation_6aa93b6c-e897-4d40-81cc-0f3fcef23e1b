package cn.hanyi.cem.worker.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "worker.data-access.backup")
public class DataAccessBackupProperties {

    private int retainDays;
    private int batchSize;
    private boolean trim;
    private String backupTable;
    private String sqlFormatNextId;
    private String sqlFormatBatch;
    private String sqlFormatInsert;
    private String sqlFormatDelete;
}
