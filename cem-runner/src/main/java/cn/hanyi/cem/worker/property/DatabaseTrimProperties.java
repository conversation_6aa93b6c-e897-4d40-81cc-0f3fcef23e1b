package cn.hanyi.cem.worker.property;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "worker.database.trim")
public class DatabaseTrimProperties {

    private int retainDays = 90;
    private List<TrimInfo> trimByMaxId = new ArrayList<>();
    private List<TrimInfo> trimByCreateTime = new ArrayList<>();

    @Getter
    @Setter
    public static class TrimInfo {
        private Integer retainDays;
        private String trimTable;
        private String trimCondition;
    }
}
