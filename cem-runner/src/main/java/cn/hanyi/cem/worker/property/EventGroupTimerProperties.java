package cn.hanyi.cem.worker.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = EventGroupTimerProperties.PREFIX)
public class EventGroupTimerProperties {
    public static final String PREFIX = "ctm.event-group-timer.scheduling";
    public static final String CRON = "${" + PREFIX + ".cron:-}";

    private Boolean enabled;
    private String cron;
    private Integer intervalMinutes;
    private String url;
}