package cn.hanyi.cem.worker.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "ctm.journey.scheduling")
public class JourneyWarningProperties {
    private Boolean enabled;
    private String cron;
    //    journey("客户旅程"),
    //    journey_descriptions("场景说明", (JourneyComponentType)null, false),
    //    textbox("文本框", journey_descriptions, false),
    //    experience_indicator("体验指标"),
    //    experience_matrix("体验矩阵"),
    //    experience_interaction("问卷推送"),
    //    curve("情绪曲线"),
    //    event_stat("事件统计", true),
    //    linker("连接器", true);
    private ArrayList<String> warnings;
}