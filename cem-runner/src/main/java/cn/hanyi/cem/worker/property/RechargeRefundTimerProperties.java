package cn.hanyi.cem.worker.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = RechargeRefundTimerProperties.PREFIX)
public class RechargeRefundTimerProperties {
    public static final String PREFIX = "worker.recharge-refund.scheduling";
    public static final String CRON = "${" + PREFIX + ".cron:-}";

    private Boolean enabled;
    private String cron;
}