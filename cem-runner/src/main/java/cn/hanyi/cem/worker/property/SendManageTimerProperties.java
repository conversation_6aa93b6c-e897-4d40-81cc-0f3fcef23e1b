package cn.hanyi.cem.worker.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = SendManageTimerProperties.PREFIX)
public class SendManageTimerProperties {
    public static final String PREFIX = "ctm.send-manage-timer.scheduling";
    public static final String CRON = "${" + PREFIX + ".cron:-}";

    private Boolean enabled;
    private String cron;
    private Integer intervalMinutes;
}