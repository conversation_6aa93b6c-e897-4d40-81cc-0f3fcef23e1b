package cn.hanyi.cem.worker.property;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "worker.simple-daily.scheduling")
public class SimpleDailyProperties {
    private boolean enabled;
    private String cron;
    private boolean refreshYouzanToken;
    private boolean dataAccessBackup;
    private boolean databaseTrim;
}
