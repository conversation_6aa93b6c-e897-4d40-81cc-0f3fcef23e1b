package cn.hanyi.cem.worker.runners;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;

public class BaseRunner {

    @Value("${worker.lock.prefix-name:worker.lock}")
    private String prefixLockName;

    @Value("${worker.lock.ttl:10}")
    private Integer ttl;


    @Autowired
    protected StringRedisTemplate stringRedisTemplate;


    private String key() {
        return prefixLockName + "." + this.getClass().getSimpleName().toLowerCase();
    }

    public Boolean lock() {
        return !stringRedisTemplate.opsForValue().setIfAbsent(key(), "1", Duration.ofSeconds(ttl));
    }


    public void unlock() {
        stringRedisTemplate.delete(key());
    }

}
