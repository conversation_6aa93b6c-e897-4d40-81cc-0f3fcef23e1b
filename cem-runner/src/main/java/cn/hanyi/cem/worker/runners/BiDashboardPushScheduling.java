package cn.hanyi.cem.worker.runners;

import cn.hanyi.cem.core.dto.task.TaskDashboardPushDto;
import cn.hanyi.cem.core.dto.task.TaskDashboardPushDto.PushChannel;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.core.properties.BiWorkerProperties;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

import cn.hanyi.ctm.constant.connector.ConnectorType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.constant.FeatureAction;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.service.FeatureActionService;
import org.befun.auth.service.ResourcePermissionService;
import org.befun.auth.service.RoleService;
import org.befun.bi.constant.DashboardPushContentType;
import org.befun.bi.dto.dashboard.DashboardPushChannel;
import org.befun.bi.dto.dashboard.PushMomentDto;
import org.befun.bi.entity.Dashboard;
import org.befun.bi.entity.DashboardPush;
import org.befun.bi.repository.DashboardPushRepository;
import org.befun.bi.service.DashboardLinkService;
import org.befun.bi.service.ScreenShotService;
import org.befun.core.dto.ResourcePermissionPartDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 定时任务 数据看板推送
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "bi.base.scheduling.enabled", havingValue = "true")
public class BiDashboardPushScheduling extends BaseRunner {

    private static final String PATTERN = "HH:mm";
    private static final String TEMPLATE_NAME = "dashboard-push";

    private ExecutorService executorService = new ThreadPoolExecutor(0, 10, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(10), Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.DiscardPolicy());

    @Autowired
    private DashboardPushRepository dashboardPushRepository;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Autowired
    private BiWorkerProperties biWorkerProperties;

    @Autowired
    private DashboardLinkService dashboardLinkService;

    @Autowired
    private ScreenShotService screenShotService;

    @Autowired
    private FileStorageService storageService;

    @Autowired
    private ResourcePermissionService resourcePermissionService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private EventConsumerUtils eventConsumerUtils;

    private final static String logPrefix = "定时任务-数据看板:";

    @PostConstruct
    public void init() {
        log.info("{} 定时任务启动",
                logPrefix
        );
    }


    @Scheduled(cron = "${bi.base.scheduling.cron}")
    public void scheduling() {

        if (lock()) {
            log.info("已经有任务在执行：{}", logPrefix);
            return;
        }
        int page = 0;
        int size = 10;
        boolean hasNext = true;
        Map<Long, Boolean> canRunCache = new HashMap<>();

        try {
            log.info("{} 获取系统中需要推送的数据看板", logPrefix);
            Semaphore semaphore = new Semaphore(size);
            while (hasNext) {
                Page<DashboardPush> dashboardPushDtos = dashboardPushRepository
                        .findAllByNotifyMemberOrNotifyGroup(true,
                                true, PageRequest.of(page, size));
                if (!dashboardPushDtos.hasContent()) {
                    hasNext = false;
                } else {
                    for (DashboardPush dashboardPush : dashboardPushDtos) {
                        if (!canRun(canRunCache, dashboardPush)) {
                            continue;
                        }
                        if (semaphore.tryAcquire(1, 10, TimeUnit.MINUTES)) {
                            executorService.execute(() -> {
                                try {
                                    pushNotifyTask(dashboardPush);
                                } catch (Exception ex) {
                                    log.error("推送看板失败，pushId:{}, caused by:{}", dashboardPush.getId(),
                                            ex.getStackTrace());
                                } finally {
                                    semaphore.release();
                                }
                            });
                        }
                    }
                    page++;
                }
            }
        } catch (Exception e) {
            log.error("{} 定时推送数据看板异常", logPrefix, e);
        } finally {
            log.info("{} 定时推送数据看板结束", logPrefix);
            unlock();
        }
    }

    private boolean canRun(Map<Long, Boolean> canRunCache, DashboardPush push) {
        Long orgId = null;
        if (push.getDashboard() != null) {
            orgId = push.getDashboard().getOrgId();
        }
        if (orgId == null) {
            log.debug("{}数据看板不存在或数据看板为空，未获得企业 id 不推送", push.getId());
            return false;
        }
        Boolean canRun = canRunCache.get(orgId);
        if (canRun == null) {
            canRun = FeatureActionService.canRun(orgId, FeatureAction.BI_DASHBOARD_NOTIFY, true);
            canRunCache.put(orgId, canRun);
        }
        if (!canRun) {
            log.info("{} {} 版本已降级 不推送", logPrefix, push.getId());
        }
        return canRun;
    }

    /**
     * 推送
     */
    public void pushNotifyTask(DashboardPush dashboardPush) {
        Dashboard dashboard = dashboardPush.getDashboard();
        if (dashboard == null || dashboard.getLayout().isEmpty()) {
            log.debug("{}数据看板不存在或数据看板为空不推送", dashboardPush.getId());
            return;
        }

        Map<String, Object> params = new HashMap<>(3);
        params.put("title", "报表推送通知");
        params.put("dashboardName", dashboard.getName());
        params.put("dashboardUrl",
                String.format("%s%s", biWorkerProperties.getDashboardUrlPrefix(),
                        dashboard.getId()));

        log.info("数据看板：{} 开始推送：{}", dashboard.getId(), dashboardPush.getId());
        // 成员通知
        if (dashboardPush.getNotifyMember() && verifyPushMoment(
                dashboardPush.getMemberOptions().getMoment())) {
            TaskDashboardPushDto taskDto = new TaskDashboardPushDto(dashboard.getId(),
                    TEMPLATE_NAME,
                    params, dashboard.getOrgId(), "DASHBOARD");
            taskDto.setUserIds(updatePermission(dashboard.getOrgId(), dashboard.getId(), "user", dashboardPush.getMemberOptions().getReceiverIds()));
            taskDto.setRoleIds(updatePermission(dashboard.getOrgId(), dashboard.getId(), "role", dashboardPush.getMemberOptions().getRoleIds()));
            taskDto.setChannels(dashboardPush.getMemberOptions().getPushChannel().stream()
                    .map(this::convertChannel).collect(
                            Collectors.toList()));
            if (!taskDto.getUserIds().isEmpty() || !taskDto.getRoleIds().isEmpty()) {
                Duration delay = buildDuration(dashboardPush.getMemberOptions().getMoment().getTime());
                generateOutWorkerScreenshots(taskDto);
                CompletableFuture.runAsync(() -> taskProducerHelper.pushDashboard(
                        taskDto, dashboard.getOrgId(), null, dashboard.getId(), delay));
            }
        }

        // 群组通知
        if (dashboardPush.getNotifyGroup() && verifyPushMoment(
                dashboardPush.getGroupOptions().getMoment())) {
            TaskDashboardPushDto taskDto = new TaskDashboardPushDto(dashboard.getId(),
                    TEMPLATE_NAME,
                    params, dashboard.getOrgId(), "DASHBOARD");
            taskDto.setChannels(dashboardPush.getGroupOptions().getPushChannel().stream()
                    .map(this::convertChannel).collect(
                            Collectors.toList()));
            Duration delay = buildDuration(dashboardPush.getGroupOptions().getMoment().getTime());
            generateBotScreenshot(taskDto);
            CompletableFuture.runAsync(() -> taskProducerHelper.pushDashboardToBot(
                    taskDto, dashboard.getOrgId(), null, dashboard.getId(), delay));
        }
    }

    /**
     * 更新推送用户和角色的权限
     *
     * @param dashboardId
     * @param receiverType
     * @param receiverIds
     * @return
     */
    private Set<Long> updatePermission(Long orgId, Long dashboardId, String receiverType,
                                       Set<Long> receiverIds) {
        if (receiverIds.isEmpty()) {
            return receiverIds;
        }
        TenantContext.setCurrentTenant(orgId);
        try {
            Set<Long> existReceiverIds = new HashSet<>();
            if ("user".equals(receiverType)) {
                // owner
                List<Long> ownerIds = Optional.ofNullable(ResourcePermissionType.DASHBOARD.buildOwnerSql(dashboardId)).map(sql -> jdbcTemplate.queryForList(sql, Long.class)).orElse(null);
                if (CollectionUtils.isNotEmpty(ownerIds)) {
                    existReceiverIds.add(ownerIds.get(0));
                }
                // admin
                Set<Long> adminUserIds = receiverIds.stream().filter(r -> roleService.hasSuperAdminRole(r)).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(adminUserIds)) {
                    existReceiverIds.addAll(adminUserIds);
                }
                // 协作
                Set<Long> cooperators = resourcePermissionService
                        .existsUsers(dashboardId, ResourcePermissionType.DASHBOARD).stream()
                        .map(ResourcePermissionPartDto::getId).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(cooperators)) {
                    existReceiverIds.addAll(cooperators);
                }
            } else if ("role".equals(receiverType)) {
                existReceiverIds = resourcePermissionService
                        .existsRoles(dashboardId, ResourcePermissionType.DASHBOARD).stream()
                        .map(ResourcePermissionPartDto::getId).collect(Collectors.toSet());
            }
            return existReceiverIds.stream().filter(receiverIds::contains).collect(Collectors.toSet());
        } finally {
            TenantContext.clear();
        }
    }

    /**
     * 校验推送日期，符合当日的推送
     *
     * @param momentDto
     * @return
     */
    private Boolean verifyPushMoment(PushMomentDto momentDto) {
        Calendar calendar = Calendar.getInstance();

        switch (momentDto.getPeriodType()) {
            case DOD:
                return true;
            case WOW:
                calendar.setFirstDayOfWeek(Calendar.MONDAY);
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                int normalizedDayOfWeek = dayOfWeek - calendar.getFirstDayOfWeek() + 1;
                if (normalizedDayOfWeek <= 0) {
                    normalizedDayOfWeek += 7;
                }
                return normalizedDayOfWeek == momentDto.getDayNumber();
            case MOM:
                int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
                if (momentDto.getIsLastDay()) {
                    return dayOfMonth == calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                }
                return dayOfMonth == momentDto.getDayNumber();
            default:
                throw new BadRequestException();
        }
    }

    private Duration buildDuration(String timeString) {

        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(PATTERN);
        LocalTime pushTime = LocalTime.parse(timeString, timeFormatter);
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime combinedDateTime = currentDateTime.with(pushTime);
        if (combinedDateTime.isBefore(currentDateTime)) {
            log.debug("当前时间超过任务指定时间，立刻执行");
            return null;
        }
        return Duration.between(currentDateTime, combinedDateTime);
    }

    private PushChannel convertChannel(DashboardPushChannel dashboardPushChannel) {
        return new PushChannel(
                dashboardPushChannel.getConnectorType().name(),
                dashboardPushChannel.getPushContentType().name(),
                dashboardPushChannel.getGateWay()
        );
    }

    private void generateOutWorkerScreenshots(TaskDashboardPushDto pushDto) {
        var users = eventConsumerUtils.getNotifyUsers(pushDto.getOrgId(), null, pushDto.getRoleIds(), pushDto.getUserIds());
        Map<Long, String> userScreenShotUrlMap = new HashMap<>();
        users.forEach(user -> {
            // Check if channels contain LINK_IMAGE push type for email
            if (pushDto.getChannels().stream().anyMatch(channel -> ConnectorType.EMAIL.name().equals(channel.getConnectorType())
                    && "LINK_IMAGE".equals(channel.getPushContentType()))) {
                try {
                    String url = dashboardLinkService.buildScreenShotLinkUrl(user.getId(), pushDto.getOrgId(), pushDto.getDashboardId());
                    byte[] imageBytes = screenShotService.screenShotAsBytes(url);
                    log.info("Screenshot image size: {}", imageBytes.length);

                    // Upload image and get URL
                    String fileUrl = uploadScreenshot(imageBytes);
                    userScreenShotUrlMap.put(user.getId(), fileUrl);
                } catch (Exception e) {
                    log.error("Failed to generate screenshot for user {}: {}", user.getId(), e.getMessage());
                }
            }
        });
        pushDto.setUserScreenShotUrlMap(userScreenShotUrlMap);
    }

    private void generateBotScreenshot(TaskDashboardPushDto pushDto) {
        // Check if channels contain LINK_IMAGE push type for bot
        if (pushDto.getChannels().stream().anyMatch(channel -> DashboardPushContentType.LINK_IMAGE.name().equals(channel.getPushContentType()))) {
            try {
                String url = dashboardLinkService.buildScreenShotLinkUrl(null, pushDto.getOrgId(), pushDto.getDashboardId());
                byte[] imageBytes = screenShotService.screenShotAsBytes(url);

                // Upload image and get URL
                String fileUrl = uploadScreenshot(imageBytes);
                pushDto.setBotScreenShotUrl(fileUrl);
            } catch (Exception e) {
                log.error("Failed to generate bot screenshot: {}", e.getMessage());
            }
        }
    }

    private String uploadScreenshot(byte[] imageBytes) {
        try {
            FileInfo fileInfo = storageService.of(imageBytes)
                    .setOriginalFilename(UUID.randomUUID() + ".png")
                    .upload();
            return fileInfo.getUrl();
        } catch (Exception e) {
            log.error("上传数据看板截图失败: {}", e.getMessage());
            return null;
        }
    }
}
