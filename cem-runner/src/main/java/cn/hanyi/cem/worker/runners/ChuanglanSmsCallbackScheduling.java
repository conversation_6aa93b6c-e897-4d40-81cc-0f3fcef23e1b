package cn.hanyi.cem.worker.runners;

import cn.hanyi.cem.task.consumer.properties.SmsCallbackProperties;
import cn.hanyi.survey.service.ChannelRecordService;
import lombok.extern.slf4j.Slf4j;
import org.befun.extension.sms.chuanglan.ChuanglanSmsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.AllNestedConditions;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 定时任务
 * 创蓝短信推送结果定时拉取任务
 */
@Component
@Slf4j
@Conditional(ChuanglanSmsCallbackScheduling.ChuanglanSmsCallbackSchedulingCondition.class)
public class ChuanglanSmsCallbackScheduling extends BaseRunner {

    public static class ChuanglanSmsCallbackSchedulingCondition extends AllNestedConditions {

        public ChuanglanSmsCallbackSchedulingCondition() {
            super(ConfigurationPhase.REGISTER_BEAN);
        }

        @ConditionalOnProperty(name = "worker.sms.callback.enable-pull-chuanglan", havingValue = "true")
        static class EnablePullChuanglan {
        }

        @ConditionalOnBean(ChuanglanSmsProvider.class)
        static class OnBean {
        }
    }

    @Autowired
    private SmsCallbackProperties smsCallbackProperties;
    @Autowired
    private ChuanglanSmsProvider chuanglanSmsProvider;
    @Autowired
    private ChannelRecordService channelRecordService;

    private final static String logPrefix = "定时任务-创蓝短信推送结果";

    @PostConstruct
    public void init() {
        log.info("{}：定时任务启动：{}",
                logPrefix,
                smsCallbackProperties.getCronPullChuanglan()
        );
    }

    @Scheduled(cron = "${worker.sms.callback.cron-pull-chuanglan}")
    public void scheduling() {

        if (lock()) {
            log.info("已经有任务在执行：{}", logPrefix);
            return;
        }
        try {
            chuanglanSmsProvider.pullSmsCallback(i -> channelRecordService.smsSendCallback(i));
        } catch (Exception e) {
            log.error("{} 定时拉取短信(创蓝)推送结果异常", logPrefix, e);
        } finally {
            log.info("{} 定时拉取短信(创蓝)推送结果结束", logPrefix);
            unlock();
        }
    }

}
