package cn.hanyi.cem.worker.runners;

import cn.hanyi.cem.worker.property.CustomerStatProperties;
import cn.hanyi.cem.event.consumer.response.CustomerConsumer;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.ctm.service.CustomerStatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 定时任务
 * 客户中心-指标计算
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "ctm.customer.scheduling.enabled", havingValue = "true")
public class CustomerStatScheduling extends BaseRunner {

    private final ExecutorService executorService = Executors.newCachedThreadPool();

    private final static String logPrefix = "定时任务-客户中心:";
    @Autowired
    private CustomerStatProperties customerStatProperties;
    @Autowired
    private CustomerStatService customerStatService;
    @Autowired
    private CustomerService customerService;

    @PostConstruct
    public void init() {
        log.info("{} 定时任务启动:客户中心计算：{} batchSize:{}",
                logPrefix,
                customerStatProperties.getCron(),
                customerStatProperties.getBatchSize()
        );
    }

    @Scheduled(cron = "${ctm.customer.scheduling.cron}")
    public void scheduledCustomer() {

        if (lock()) {
            log.info("已经有任务在执行：{}", logPrefix);
            return;
        }

        try {
            boolean hasNext = true;
            int maxPage = 10;
            int page = 0;
            int size = customerStatProperties.getBatchSize();
            String key = CustomerConsumer.getSyncCustomerKey(LocalDate.now().minusDays(1));
            Semaphore semaphore = new Semaphore(maxPage);
            while (hasNext) {
                log.info("{} 定时计算page:{} size:{}", logPrefix, page, size);
                List<String> syncCustomerIds = stringRedisTemplate.opsForSet().pop(key, size);
                if (CollectionUtils.isNotEmpty(syncCustomerIds)) {
                    semaphore.acquire();
                    try {
                        executorService.execute(() -> {
                            try {
                                processPage(syncCustomerIds);
                            } catch (Exception e) {
                                log.error("{}-processPage", logPrefix, e);
                            } finally {
                                semaphore.release();
                            }
                        });
                    } catch (Exception e) {
                        log.error("{}-executorService定时计算异常", logPrefix, e);
                    }

                } else {
                    hasNext = false;
                }
                page++;
            }

            semaphore.tryAcquire(maxPage, 10, TimeUnit.MINUTES);

        } catch (Exception e) {
            log.error("{} 定时计算异常", logPrefix, e);
        } finally {
            log.info("{} 定时计算结束", logPrefix);
            unlock();
        }
    }

    private void processPage(List<String> syncCustomerIds) {
        syncCustomerIds.forEach(c -> {
            try {
                if (NumberUtils.isDigits(c)) {
                    Long id = Long.parseLong(c);
                    Customer customer = customerService.get(id);
                    if (customer != null) {
                        customerStatService.updateCustomerStat(customer.getId(), customer.getOrgId());
                    }
                }
            } catch (Throwable e) {
                log.error("同步客户{}统计数据失败", c, e);
            }
        });
    }
}
