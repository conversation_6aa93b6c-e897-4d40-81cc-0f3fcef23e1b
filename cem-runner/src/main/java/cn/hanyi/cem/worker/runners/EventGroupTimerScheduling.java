package cn.hanyi.cem.worker.runners;

import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskEventGroupNotifyDto;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.worker.property.EventGroupTimerProperties;
import cn.hanyi.ctm.dto.group.EventGroupNotifyRobotDto;
import cn.hanyi.ctm.dto.group.EventGroupNotifyUserDto;
import cn.hanyi.ctm.entity.EventGroup;
import cn.hanyi.ctm.repository.EventGroupRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.service.ResourcePermissionService;
import org.befun.core.dto.ResourcePermissionPartDto;
import org.befun.core.dto.ResourcePermissionUserDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.hanyi.cem.worker.property.EventGroupTimerProperties.CRON;
import static cn.hanyi.cem.worker.property.EventGroupTimerProperties.PREFIX;

@Slf4j
@Component
@ConditionalOnProperty(prefix = PREFIX, value = "enabled", havingValue = "true")
public class EventGroupTimerScheduling extends BaseRunner {

    private final static String logPrefix = "定时任务-事件分组重复任务:";

    @Autowired
    private EventGroupTimerProperties properties;
    @Autowired
    private EventGroupRepository eventGroupRepository;
    @Autowired
    private ResourcePermissionService resourcePermissionService;
    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @PostConstruct
    public void init() {
        log.info("{} 定时任务启动：{} intervalMinutes:{}",
                logPrefix,
                properties.getCron(),
                properties.getIntervalMinutes()
        );
    }

    @Scheduled(cron = CRON)
    public void scheduling() {
        if (lock()) {
            log.info("已经有任务在执行：{}", logPrefix);
            return;
        }
        try {
            int page = 0;
            int size = 100;
            boolean hasNext = true;
            LocalDateTime start = LocalDateTime.now().withSecond(0).withNano(0);
            LocalDateTime end = start.plusMinutes(properties.getIntervalMinutes());
            while (hasNext) {
                List<EventGroup> list = eventGroupRepository.findByNotifyUserTrueOrNotifyRobotTrue(PageRequest.of(page, size));
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(i -> execute(i, start, end));
                    hasNext = list.size() == size;
                    page++;
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("{} 定时计算异常", logPrefix, e);
        } finally {
            log.info("{} 定时计算结束", logPrefix);
            unlock();
        }
    }

    private void execute(EventGroup eventGroup, LocalDateTime start, LocalDateTime end) {
        try {
            notifyUser(eventGroup, start, end);
        } catch (Throwable e) {
            log.warn("事件分组通知用户任务 {} failure", eventGroup.getId(), e);
        }
        try {
            notifyRobot(eventGroup, start, end);
        } catch (Throwable e) {
            log.warn("事件分组通知群组任务 {} failure", eventGroup.getId(), e);
        }
    }

    private void notifyUser(EventGroup eventGroup, LocalDateTime start, LocalDateTime end) {
        if (eventGroup.getNotifyUser() == null || !eventGroup.getNotifyUser() || eventGroup.getNotifyUserContent() == null) {
            return;
        }
        EventGroupNotifyUserDto content = eventGroup.getNotifyUserContent();
        content.getNotifyUserTimer().setStartDate("2024-01-01 00:00:00");
        content.getNotifyUserTimer().setEndDate("2124-01-01 00:00:00");
        if (!content.valid()) {
            return;
        }
        LocalDateTime nextTime = content.getNotifyUserTimer().findNextTime(start, end);
        if (nextTime == null) {
            return;
        }
        List<ResourcePermissionUserDto> shareUsers = resourcePermissionService.relationUsers(eventGroup.getOrgId(), eventGroup.getId(), ResourcePermissionType.EVENT_GROUP);
        if (CollectionUtils.isEmpty(shareUsers)) {
            return;
        }
        Set<Long> shareUserIds = shareUsers.stream().map(ResourcePermissionPartDto::getId).collect(Collectors.toSet());
        Set<Long> userIds = new HashSet<>();
        content.getNotifyUserIds().forEach(i -> {
            if (shareUserIds.contains(i)) {
                userIds.add(i);
            }
        });
        if (userIds.isEmpty()) {
            return;
        }
        Duration delay = Duration.between(LocalDateTime.now(), nextTime);
        taskProducerHelper.addTask(
                eventGroup.getOrgId(),
                0L,
                TaskType.EVENT_GROUP_NOTIFY_USER,
                eventGroup.getId(),
                new TaskEventGroupNotifyDto(eventGroup.getId(), properties.getUrl(), userIds),
                delay);
    }


    private void notifyRobot(EventGroup eventGroup, LocalDateTime start, LocalDateTime end) {
        if (eventGroup.getNotifyRobot() == null || !eventGroup.getNotifyRobot() || eventGroup.getNotifyRobotContent() == null) {
            return;
        }
        EventGroupNotifyRobotDto content = eventGroup.getNotifyRobotContent();
        content.getNotifyRobotTimer().setStartDate("2024-01-01 00:00:00");
        content.getNotifyRobotTimer().setEndDate("2124-01-01 00:00:00");
        if (!content.valid()) {
            return;
        }
        LocalDateTime nextTime = content.getNotifyRobotTimer().findNextTime(start, end);
        if (nextTime == null) {
            return;
        }
        Duration delay = Duration.between(LocalDateTime.now(), nextTime);
        taskProducerHelper.addTask(
                eventGroup.getOrgId(),
                0L,
                TaskType.EVENT_GROUP_NOTIFY_ROBOT,
                eventGroup.getId(),
                new TaskEventGroupNotifyDto(eventGroup.getId(), properties.getUrl()), delay);
    }
}
