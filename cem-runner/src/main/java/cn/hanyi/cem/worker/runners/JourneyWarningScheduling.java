package cn.hanyi.cem.worker.runners;

import cn.hanyi.cem.core.dto.task.TaskNotifyWarningJourneyDto;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.worker.property.JourneyWarningProperties;
import cn.hanyi.cem.worker.journeywarning.JourneyWarningWorker;
import cn.hanyi.ctm.constant.event.EventNotifyMoment;
import cn.hanyi.ctm.constant.event.EventType;
import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.EventMqDto;
import cn.hanyi.ctm.entity.journey.JourneyWarningPublish;
import cn.hanyi.ctm.service.EventNotifyService;
import cn.hanyi.ctm.service.JourneyWarningService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.FeatureAction;
import org.befun.auth.service.FeatureActionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 定时任务
 * 客户旅程-体验指标计算
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "ctm.journey.scheduling.enabled", havingValue = "true")
public class JourneyWarningScheduling extends BaseRunner {

    private ExecutorService executorService = Executors.newCachedThreadPool();

    @Autowired
    private JourneyWarningService journeyWarningService;

    @Autowired
    private JourneyWarningProperties journeyWarningProperties;

    @Autowired
    private EventNotifyService eventNotifyService;

    @Autowired
    private List<JourneyWarningWorker> journeyWarningWorkers;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    private final static String logPrefix = "定时任务-客户旅程:";

    @PostConstruct
    public void init() {
        log.info("{} 定时任务启动:{} push:{}",
                logPrefix,
                journeyWarningProperties.getWarnings().toString(),
                journeyWarningProperties.getCron()
        );
    }


    @Scheduled(cron = "${ctm.journey.scheduling.cron}")
    public void scheduledJourney() {

        if (lock()) {
            log.info("已经有任务在执行：{}", logPrefix);
            return;
        }

        try {
            if (CollectionUtils.isNotEmpty(journeyWarningWorkers)) {
                log.info("{} 开始计算需要预警的旅程", logPrefix);

                // 清空旧的缓存数据
                journeyWarningService.clearWarningValue();
                journeyWarningWorkers.forEach(w -> journeyWarningService.clearWarningNotify(w.getRelationType()));

                var maxThreads = 5;
                var semaphore = new Semaphore(maxThreads);
                journeyWarningWorkers.forEach(w -> {
                    try {
                        semaphore.acquire();
                        w.cacheData(null);
                    } catch (Exception e) {
                        log.error("{} cacheData error", logPrefix, e);
                    } finally {
                        semaphore.release();
                    }
                });
                semaphore.tryAcquire(maxThreads, 30, TimeUnit.MINUTES);
                startNotify();
            }
        } catch (Exception e) {
            log.error("{} 定时计算需要预警的旅程异常", logPrefix, e);
        } finally {
            log.info("{} 定时计算需要预警的旅程结束", logPrefix);
            unlock();
        }
    }

    public void startNotify() {
        try {
            log.info("{} 开始推送 ... ", logPrefix);
            journeyWarningProperties.getWarnings().forEach(warning -> {
                journeyWarningService.consumerWarningNotify(JourneyComponentType.valueOf(warning), (journeyWarningPublish, point) -> {
                    if (journeyWarningPublish.getEnableWarning() && journeyWarningPublish.getEnableNotify()) {
                        var isPush = isWarning(journeyWarningPublish, point);
                        log.info("{} {} 计算: 是否预警: {} 预警{} {}", logPrefix, warning, isPush, journeyWarningPublish.getId(), point);
                        if (isPush) {
                            pushNotifyTask(journeyWarningPublish, point);
                        }
                    }
                });
            });
        } catch (Exception e) {
            log.error("{} 异常: {}", logPrefix, e.getMessage());
        } finally {
            log.info("{} 定时任务完成", logPrefix);
        }

    }

    /**
     * 推送
     */
    public void pushNotify(JourneyWarningPublish journeyWarningPublish, Double point) {

        String roleIds = Objects.requireNonNull(journeyWarningPublish.getReceiver()).getRoleIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        if (StringUtils.isEmpty(roleIds)) {
            return;
        }

        var eventMqDto = new EventMqDto();
        eventMqDto.setEventId(journeyWarningPublish.getId());
        eventMqDto.setOrgId(journeyWarningPublish.getOrgId());
        eventMqDto.setRoleIds(roleIds);
        eventMqDto.setNotifyMoment(EventNotifyMoment.IMMEDIATE);
        eventMqDto.setNotifyChannel(journeyWarningPublish.getReceiver().getNotifyChannel());
        eventMqDto.setEventType(EventType.JOURNEY);
        eventMqDto.setValue(point);
        log.info("{} 发送通知: {}", logPrefix, eventMqDto.getEventId());
        eventNotifyService.notifyImmediately(eventMqDto);
    }

    /**
     * 推送
     */
    public void pushNotifyTask(JourneyWarningPublish warning, Double point) {
        boolean canRun = true;
        if (warning.getRelationType() == JourneyComponentType.experience_indicator) {
            canRun = FeatureActionService.canRun(warning.getOrgId(), FeatureAction.JOURNEY_INDICATOR_WARNING, true);
        } else if (warning.getRelationType() == JourneyComponentType.event_stat) {
            canRun = FeatureActionService.canRun(warning.getOrgId(), FeatureAction.JOURNEY_EVENT_WARNING, true);
        }
        if (!canRun) {
            log.info("{} {} 版本已降级 取消预警{} {}", logPrefix, warning, warning.getId(), point);
            return;
        }
        var dto = new TaskNotifyWarningJourneyDto(warning.getId(), point);
        taskProducerHelper.journeyWarningNotify(dto);
    }

    /**
     * 校验是否满足预警规则
     */
    public Boolean isWarning(JourneyWarningPublish journeyWarningPublish, Double point) {

        return (
                journeyWarningPublish.getEnableNotify()
                        && journeyWarningPublish.getWarningCompare().compare(point, journeyWarningPublish.getWarningValue())
        );
    }
}
