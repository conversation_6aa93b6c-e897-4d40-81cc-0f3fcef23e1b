package cn.hanyi.cem.worker.runners;

import cn.hanyi.cem.worker.property.RechargeRefundTimerProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.pay.constant.RechargeRefundStatus;
import org.befun.auth.pay.entity.OrganizationRecharge;
import org.befun.auth.pay.entity.OrganizationRechargeRefund;
import org.befun.auth.pay.repository.OrganizationRechargeRefundRepository;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

import static cn.hanyi.cem.worker.property.RechargeRefundTimerProperties.CRON;
import static cn.hanyi.cem.worker.property.RechargeRefundTimerProperties.PREFIX;

@Slf4j
@Component
@ConditionalOnProperty(prefix = PREFIX, value = "enabled", havingValue = "true")
public class RechargeRefundTimerScheduling extends BaseRunner {

    private final static String logPrefix = "定时任务-充值退款:";
    @Autowired
    private RechargeRefundTimerProperties properties;
    @Autowired
    private OrganizationRechargeRefundRepository organizationRechargeRefundRepository;
    @Autowired
    private OrganizationRechargeService organizationRechargeService;

    @PostConstruct
    public void init() {
        log.info("{} 定时任务启动：{}",
                logPrefix,
                properties.getCron()
        );
    }

    @Scheduled(cron = CRON)
    public void scheduling() {
        if (lock()) {
            log.info("已经有任务在执行：{}", logPrefix);
            return;
        }
        try {
            int page = 0;
            int size = 100;
            boolean hasNext = true;
            while (hasNext) {
                List<OrganizationRechargeRefund> list = organizationRechargeRefundRepository.findByStatus(RechargeRefundStatus.init, PageRequest.of(page, size));
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(this::execute);
                    hasNext = list.size() == size;
                    page++;
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("{} 定时计算异常", logPrefix, e);
        } finally {
            log.info("{} 定时计算结束", logPrefix);
            unlock();
        }
    }

    private void execute(OrganizationRechargeRefund refund) {
        try {
            // 需要在1分钟之后才执行
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime create = DateHelper.toLocalDateTime(refund.getCreateTime());
            if (Duration.between(create, now).getSeconds() >= 60) {
                OrganizationRecharge recharge = organizationRechargeService.get(refund.getRechargeId());
                if (recharge == null) {
                    return;
                }
                organizationRechargeService.refundCallback(recharge, refund);
            }
        } catch (Throwable e) {
            log.error("recharge refund ({}) error", refund.getId(), e);
        }

    }
}
