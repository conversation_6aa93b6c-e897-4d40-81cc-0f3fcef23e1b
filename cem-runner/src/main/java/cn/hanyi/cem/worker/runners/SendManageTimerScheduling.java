package cn.hanyi.cem.worker.runners;

import cn.hanyi.cem.worker.property.SendManageTimerProperties;
import cn.hanyi.ctm.constant.SendManageTriggerType;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.sendmanage.SendMangeJourneyHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;

import static cn.hanyi.cem.worker.property.SendManageTimerProperties.CRON;
import static cn.hanyi.cem.worker.property.SendManageTimerProperties.PREFIX;

@Slf4j
@Component
@ConditionalOnProperty(prefix = PREFIX, value = "enabled", havingValue = "true")
public class SendManageTimerScheduling extends BaseRunner {

    private final static String logPrefix = "定时任务-发送管理重复任务:";
    @Autowired
    private SendManageTimerProperties properties;
    @Autowired
    private SendMangeJourneyHelper sendMangeJourneyHelper;
    @Autowired
    private SendManageRepository sendManageRepository;

    @PostConstruct
    public void init() {
        log.info("{} 定时任务启动：{} intervalMinutes:{}",
                logPrefix,
                properties.getCron(),
                properties.getIntervalMinutes()
        );
    }

    @Scheduled(cron = CRON)
    public void scheduling() {
        if (lock()) {
            log.info("已经有任务在执行：{}", logPrefix);
            return;
        }
        try {
            int page = 0;
            int size = 100;
            boolean hasNext = true;
            LocalDateTime start = LocalDateTime.now().withSecond(0).withNano(0);
            LocalDateTime end = start.plusMinutes(properties.getIntervalMinutes());
            while (hasNext) {
                List<SendManage> list = sendManageRepository.findByTriggerTypeAndEnable(SendManageTriggerType.TIMER, true, PageRequest.of(page, size));
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(i -> execute(i, start, end));
                    hasNext = list.size() == size;
                    page++;
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("{} 定时计算异常", logPrefix, e);
        } finally {
            log.info("{} 定时计算结束", logPrefix);
            unlock();
        }
    }

    private void execute(SendManage sendManage, LocalDateTime start, LocalDateTime end) {
        if (sendManage.getTriggerTimer() == null) {
            log.warn("发送管理重复任务 {} 触发时间 为空 skip", sendManage.getId());
            return;
        }
        if (sendManage.getTriggerTimerTarget() == null) {
            log.warn("发送管理重复任务 {} 触发目标 为空 skip", sendManage.getId());
        }
        try {
            sendManage.getTriggerTimer().check();
            sendManage.getTriggerTimerTarget().check();
        } catch (Throwable e) {
            log.warn("发送管理重复任务 {} 配置错误 skip，{}", sendManage.getId(), e.getMessage());
            return;
        }

        try {
            LocalDateTime nextTime = sendManage.getTriggerTimer().findNextTime(start, end);
            if (nextTime != null) {
                LocalDateTime lastTime = DateHelper.toLocalDateTime(sendManage.getLastTriggerTimer());
                if (lastTime == null || lastTime.isBefore(nextTime)) {
                    sendMangeJourneyHelper.addCustomerJourneyByTimer(sendManage, nextTime);
                }
            }
        } catch (Throwable e) {
            log.warn("发送管理重复任务 {} 添加失败 skip", sendManage.getId(), e);
        }
    }
}
