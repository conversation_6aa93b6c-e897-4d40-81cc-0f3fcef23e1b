package cn.hanyi.cem.worker.runners;

import cn.hanyi.cem.worker.dataaccess.DataAccessBackupHelper;
import cn.hanyi.cem.worker.database.DatabaseTrimHelper;
import cn.hanyi.cem.worker.property.SimpleDailyProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.repository.ThirdPartyAuthRepository;
import org.befun.auth.service.auth.AuthYouzanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 每日定时任务
 * 这里只处理简单的不耗时的任务
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "worker.simple-daily.scheduling.enabled", havingValue = "true")
public class SimpleDailyScheduling extends BaseRunner {

    private final static String logPrefix = "定时任务-客户中心:";
    @Autowired
    private SimpleDailyProperties simpleDailyProperties;
    @Autowired
    private ThirdPartyAuthRepository thirdPartyAuthRepository;
    @Autowired
    private AuthYouzanService authYouzanService;
    @Autowired
    private DataAccessBackupHelper dataAccessBackupHelper;
    @Autowired
    private DatabaseTrimHelper dataBaseTrimHelper;

    @PostConstruct
    public void init() {
        log.info("{} 定时任务启动:每日执行一次：{}",
                logPrefix,
                simpleDailyProperties.getCron()
        );
    }

    @Scheduled(cron = "${worker.simple-daily.scheduling.cron}")
    public void scheduled() {

        if (lock()) {
            log.info("已经有任务在执行：{}", logPrefix);
            return;
        }
        try {
            try {
                if (simpleDailyProperties.isDataAccessBackup()) {
                    dataAccessBackupHelper.backup();
                }
            } catch (Throwable e) {
                log.error("{} 定时计算异常 data access backup", logPrefix, e);
            }
            try {
                if (simpleDailyProperties.isRefreshYouzanToken()) {
                    refreshYouzanAccessToken();
                }
            } catch (Throwable e) {
                log.error("{} 定时计算异常 refresh you zan access token ", logPrefix, e);
            }
            try {
                if (simpleDailyProperties.isDatabaseTrim()) {
                    dataBaseTrimHelper.trim();
                }
            } catch (Throwable e) {
                log.error("{} 定时计算异常 database trim ", logPrefix, e);
            }
        } catch (Exception e) {
            log.error("{} 定时计算异常", logPrefix, e);
        } finally {
            log.info("{} 定时计算结束", logPrefix);
            unlock();
        }
    }

    private void refreshYouzanAccessToken() {
        try {
            foreachPage(
                    page -> thirdPartyAuthRepository.findByAuthType(ThirdPartyAuthType.YOUZAN, PageRequest.of(page, 100)),
                    i -> {
                        if (StringUtils.isNotEmpty(i.getSource())) {
                            String[] as = i.getSource().split("-");
                            String kdtId = as[as.length - 1];
                            authYouzanService.getAccessToken(kdtId);
                        }
                    }
            );
        } catch (Throwable e) {
            log.error("有赞定时刷新token失败");
        }
    }

    private <T> void foreachPage(Function<Integer, List<T>> getPageList, Consumer<T> consumerItem) {
        int page = 0;
        do {
            List<T> list = getPageList.apply(page);
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(i -> {
                    try {
                        consumerItem.accept(i);
                    } catch (Throwable e) {
                        log.error("", e);
                    }
                });
                page++;
            } else {
                return;
            }
        } while (true);
    }
}
