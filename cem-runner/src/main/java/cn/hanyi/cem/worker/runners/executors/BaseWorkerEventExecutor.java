package cn.hanyi.cem.worker.runners.executors;

import cn.hanyi.cem.core.dto.WorkerDto;
import cn.hanyi.cem.core.entity.CemEvent;
import cn.hanyi.cem.core.repository.CemEventRepository;
import cn.hanyi.cem.event.consumer.EventConsumerHelper;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class BaseWorkerEventExecutor<T extends WorkerDto> extends BaseTaskExecutor<T> {

    @Autowired
    private CemEventRepository cemEventRepository;

    protected abstract EventConsumerHelper getEventConsumerHelper();

    private CemEvent require(Long workerId) {
        return cemEventRepository.findById(workerId).orElseThrow(EntityNotFoundException::new);
    }

    @Override
    public void run(T detailDto, TaskContextDto context) {
        getEventConsumerHelper().consumer(detailDto);
    }
}
