package cn.hanyi.cem.worker.runners.executors;

import cn.hanyi.cem.core.dto.WorkerDto;
import cn.hanyi.cem.core.repository.CemTaskRepository;
import cn.hanyi.cem.task.consumer.TaskConsumerHelper;
import org.befun.task.BaseTaskExecutor;
import org.befun.task.dto.TaskContextDto;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class BaseWorkerTaskExecutor<T extends WorkerDto> extends BaseTaskExecutor<T> {

    @Autowired
    private CemTaskRepository cemTaskRepository;

    protected abstract TaskConsumerHelper getTaskConsumerHelper();

    @Override
    public void run(T detailDto, TaskContextDto context) {
        getTaskConsumerHelper().consumer(detailDto);
    }
}
