package cn.hanyi.cem.worker.runners.executors;

import cn.hanyi.cem.core.constant.WorkerQueueName;
import cn.hanyi.cem.core.dto.WorkerDto;
import cn.hanyi.cem.event.consumer.EventConsumerHelper;
import lombok.extern.slf4j.Slf4j;
import org.befun.task.annotation.Task;
import org.befun.task.annotation.TaskRetryable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Slf4j
@Task(WorkerQueueName.EVENT)
@TaskRetryable(maxAttempts = 3, disabled = false)
@Component
@ConditionalOnProperty(prefix = "worker.event", value = "enabled", havingValue = "true")
public class WorkerEvent000Executor extends BaseWorkerEventExecutor<WorkerDto> {

    @Autowired
    private WorkerEvent000ConsumerHelper eventConsumerHelper;

    @Override
    protected EventConsumerHelper getEventConsumerHelper() {
        return eventConsumerHelper;
    }
}
