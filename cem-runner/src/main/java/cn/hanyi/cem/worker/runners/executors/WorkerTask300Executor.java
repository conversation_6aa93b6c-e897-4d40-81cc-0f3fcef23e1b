package cn.hanyi.cem.worker.runners.executors;

import cn.hanyi.cem.core.constant.WorkerQueueName;
import cn.hanyi.cem.core.dto.WorkerDto;
import cn.hanyi.cem.task.consumer.TaskConsumerHelper;
import lombok.extern.slf4j.Slf4j;
import org.befun.task.annotation.Task;
import org.befun.task.annotation.TaskRetryable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Slf4j
@Task(WorkerQueueName.TASK300)
@TaskRetryable(maxAttempts = 3, disabled = false)
@Component
@ConditionalOnProperty(prefix = "worker.task", value = "enabled", havingValue = "true")
public class WorkerTask300Executor extends BaseWorkerTaskExecutor<WorkerDto> {

    @Autowired
    private WorkerTask300ConsumerHelper taskConsumerHelper;

    @Override
    protected TaskConsumerHelper getTaskConsumerHelper() {
        return taskConsumerHelper;
    }
}
