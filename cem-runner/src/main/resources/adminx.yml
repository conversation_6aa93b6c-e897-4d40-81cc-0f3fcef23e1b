adminx:
  response-submit: ${RESPONSE_SUBMIT_URI:https://dev.xmplus.cn/api/adminx/open/response-submit-final}
  response-view: ${RESPONSE_VIEW_URI:https://dev.xmplus.cn/api/adminx/open/response-view}
  channel-operation: ${CHANNEL_OPERATION_URI:https://dev.xmplus.cn/api/adminx/open/channel-operation}
  response-submit-key: ${RESPONSE_SUBMIT_REDIS_KEY:adminx-response-submit}
  response-view-key: ${RESPONSE_VIEW_REDIS_KEY:adminx-response-view}
  channel-operation-key: ${CHANNEL_OPERATION_KEY:adminx-channel-operation}
  survey-audit-key: ${ADMINX_SURVEY_AUDIT_KEY:adminx-survey-audit}
  order-pay-key: ${ADMINX_ORDER_PAY_KEY:adminx-order-pay}
worker:
  adminx-channel:
    request:
      notify-url: ${system-notify.url.wework.adminx-channel}
      adminx-url: ${system-notify.url.adminx}
      template: |
        订单提交通知
        >跟进人：${contacts}
        >问卷ID：${surveyId}
        >问卷标题：${surveyTitle}
        >渠道ID：${channelId}
        >订单总价：${amount}
        >提交时间：${requestTime}
        >备注：请在半个小时内处理！[点击查看](${adminxUrl})
    paid:
      contacts: ${ADMINX_CHANNEL_ORDER_PAID_CONTACTS:瑞华}
      notify-url: ${system-notify.url.wework.adminx-channel}
      adminx-url: ${system-notify.url.adminx}
      template: |
        订单支付通知
        >跟进人：${contacts}
        >问卷ID：${surveyId}
        >问卷标题：${surveyTitle}
        >渠道ID：${channelId}
        >订单总价：${amount}
        >支付时间：${paidTime}
        >备注：请在半个小时内处理！[点击查看](${adminxUrl})
    quoted:
      wechat-mp-template-name: adminx-channel-order
      sms-template-name: adminx-channel-order
      inbox-message-template-name: adminx-channel-order-quoted
      params:
        orderType: 样本订单已报价
        statusDesc: 服务已报价
        orderDesc: 样本服务报价已更新
    reject:
      wechat-mp-template-name: adminx-channel-order
      sms-template-name: adminx-channel-order
      inbox-message-template-name: adminx-channel-order-reject
      params:
        orderType: 样本订单已驳回
        statusDesc: 订单已驳回
        orderDesc: 样本服务需求被驳回
    refund-success:
      wechat-mp-template-name: adminx-channel-order-refund-success
      sms-template-name: adminx-channel-order
      inbox-message-template-name: adminx-channel-order-refund-success
      notify-url: ${system-notify.url.wework.adminx-channel}
      adminx-url: ${system-notify.url.adminx}
      template: |
        退款成功通知
        >跟进人：瑞华
        >问卷ID：${surveyId}
        >问卷标题：${surveyTitle}
        >渠道ID：${channelId}
        >退款金额：${refundAmount}
        >退款时间：${refundTime}
        >备注：请在半个小时内处理！[点击查看](${adminxUrl})
      params:
        orderType: 样本订单已退款
        statusDesc: 已退款
        orderDesc: 已按原支付方式成功退款
    refund-failure:
      wechat-mp-template-name: adminx-channel-order-refund-failure
      sms-template-name: adminx-channel-order
      inbox-message-template-name: adminx-channel-order-refund-failure
      notify-url: ${system-notify.url.wework.adminx-channel}
      adminx-url: ${system-notify.url.adminx}
      template: |
        退款失败通知
        >跟进人：瑞华
        >问卷ID：${surveyId}
        >问卷标题：${surveyTitle}
        >渠道ID：${channelId}
        >退款金额：${refundAmount}
        >退款时间：${refundTime}
        >备注：请在半个小时内处理！[点击查看](${adminxUrl})
      params:
        orderType: 样本订单退款失败
        statusDesc: 退款失败
        orderDesc: 第三方原因导致退款失败，需人工重新退款
    start:
      wechat-mp-template-name: adminx-channel-order
      sms-template-name: adminx-channel-order
      params:
        orderType: 样本服务已开始
        statusDesc: 服务已开始
        orderDesc: 数据已开始回收
    end:
      wechat-mp-template-name: adminx-channel-order
      sms-template-name: adminx-channel-order
      params:
        orderType: 样本服务已完成
        statusDesc: 服务已完成
        orderDesc: 数据已回收完成

