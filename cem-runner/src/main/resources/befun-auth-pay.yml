befun:
  auth:
    pay:
      service-rate:
        wallet: ${PAY_SERVICE_RATE_WALLET:0.01}
        order_red_packet: ${PAY_SERVICE_RATE_ORDER_RED_PACKET:0.01}
        order_ai_point: ${PAY_SERVICE_RATE_ORDER_AI_POINT:0.01}
        order_sms: ${PAY_SERVICE_RATE_ORDER_SMS:0.01}
    recharge:
      enabled:
        recharge_wechat: true
        recharge_alipay: true
      expire-minute: ${AUTH_PAY_EXPIRE_MINUTE:120}
      mock-pay-url: ${AUTH_PAY_MOCK_URL:${xmplus.domain}/api/auth/organization/wallet/recharge/mockPay?rechargeId=%d&rechargeType=%s}
    order:
      sms:
        price: ${AUTH_PAY_SMS_PRICE:8}
      version:
        limit-left-days: 60
        days-of-year: 365
        support-versions:
          - version: BASE
            price: 169900
          - version: UPDATE
            price: 599900
          - version: PROFESSION
            price: 5000000


