befun:
  extension:
    wechat-pay:
      enable: ${WECHAT_PAY_ENABLE:true}
      app-id: ${WECHAT_PAY_APP_ID:wx38eb115cf5014c74}
      mch-id: ${WECHAT_PAY_MCH_ID:1521879911}
      v2-mch-key: ${WECHAT_PAY_V2_MCH_KEY:LDPpsBzLOqo6dVjfGj2wWXYZHa9UdcO7}
      v3-mch-key: ${WECHAT_PAY_V3_MCH_KEY:c0R7uY7aiRljM6eXLFYB2HBqZ3kw0KTN}
      cert-p12-path: ${WECHAT_PAY_CERT_P12_PATH:/config/cert/wx/apiclient_cert.p12}
      private-key-path: ${WECHAT_PAY_PRIVATE_KEY_PATH:/config/cert/wx/apiclient_key.pem}
      cert-pem-path: ${WECHAT_PAY_CERT_PEM_PATH:/config/cert/wx/apiclient_cert.pem}
      use-sandbox: ${WECHAT_PAY_USE_SANDBOX:false}
      pay-notify-url: ${WECHAT_PAY_NOTIFY_URL:${xmplus.domain}/api/auth/wechatPay/placeOrder/callback/cem}
      pay-refund-notify-url: ${WECHAT_PAY_REFUND_NOTIFY_URL:${xmplus.domain}/api/auth/wechatPay/refund/callback/cem}


