worker:
  notify:
    test: ss
    bots:
      - channel: FEI_SHU
        templates:
          - name: dashboard-push
            msg-type: post
            content:
              '
              {
                "post": {
                  "zh_cn": {
                    "title": "报表推送通知",
                    "content": [
                      [
                        {
                          "tag": "text",
                          "text": "Hi, 您收到体验家定时推送的仪表盘数据"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "仪表盘名称：${dashboardName}"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "推送时间：${pushTime}"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "备注："
                        },
                        {
                          "tag": "a",
                          "text": "点击查看详情",
                          "href": "${dashboardUrl}"
                        }
                      ]
                    ]
                  }
                }
              }
              '
          - name: event
            msg-type: markdown
            content:
              '
              {
                "post": {
                  "zh_cn": {
                    "title": "⚠️${title}",
                    "content": [
                      [
                        {
                          "tag": "text",
                          "text": "预警等级：${warningLevel}"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "预警名称：${warningName}"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "所属问卷：${surveyName}"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "客户名称：${customerName}"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "手机号码：${customerPhone}"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "邮箱：${customerEmail}"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "客户编号：${customerExternalUserId}"
                        }
                      ],
                      [
                        {
                          "tag": "text",
                          "text": "备注：请尽快登录到体验家XM处理！"
                        },
                        {
                          "tag": "a",
                          "text": "点击查看",
                          "href": "${targetUrl}"
                        }
                      ]
                    ]
                  }
                }
              }
              '
          - name: event-group-notify
            msg-type: post
            content:
              '
             {
               "post": {
                 "zh_cn": {
                   "title": "报表推送通知",
                   "content": [
                     [
                       {
                         "tag": "text",
                         "text": "Hi, 您收到体验家定时推送的报表数据"
                       }
                     ],
                     [
                       {
                         "tag": "text",
                         "text": "事件分组名称：${groupName}"
                       }
                     ],
                     [
                       {
                         "tag": "text",
                         "text": "推送时间：${pushTime}"
                       }
                     ],
                     [
                       {
                         "tag": "text",
                         "text": "数据详情：预警事件数是${countAll}条、其中有${countWait}条待处理事件、${countApplying}条进行中事件、${countSuccess}条已完成事件"
                       }
                     ],
                     [
                       {
                         "tag": "text",
                         "text": "详情入口："
                       },
                       {
                         "tag": "a",
                         "text": "点击查看详情",
                         "href": "${url}"
                       }
                     ]
                   ]
                 }
               }
             }
             '   
      - channel: DING_DING
        templates:
          - name: dashboard-push
            msg-type: markdown
            content: |
              ## 报表推送通知
              > Hi, 您收到体验家定时推送的仪表盘数据
              >
              > 仪表盘名称：${dashboardName}
              >
              > 推送时间：${pushTime}
              >
              > 备注：[点击查看详情](${dashboardUrl})
          - name: event
            msg-type: markdown
            content: |
              ## ⚠️${title}
              > 预警等级：${warningLevel}
              >
              > 预警名称：${warningName}
              >
              > 所属问卷：${surveyName}
              >
              > 客户名称：${customerName}
              >
              > 手机号码：${customerPhone}
              >
              > 邮箱：${customerEmail}
              >
              > 客户编号：${customerExternalUserId}
              >
              > 备注：请尽快登录到体验家XM处理！[点击查看](${targetUrl})
          - name: event-group-notify
            msg-type: markdown
            content: |
              ## 报表推送通知
              > Hi，您收到体验家定时推送的报表数据
              >
              > 事件分组名称：${groupName}
              >
              > 推送时间：${pushTime}
              >
              > 数据详情：预警事件数是${countAll}条、其中有${countWait}条待处理事件、${countApplying}条进行中事件、${countSuccess}条已完成事件
              >
              > 详情入口：[点击查看详情](${url})
      - channel: WECHAT_WORK
        templates:
          - name: dashboard-push
            msg-type: markdown
            content: |
              ## 报表推送通知
              > Hi, 您收到体验家定时推送的仪表盘数据
              > 仪表盘名称：${dashboardName}
              > 推送时间：${pushTime}
              > 备注：[点击查看详情](${dashboardUrl})
          - name: event
            msg-type: markdown
            content: |
              ## ⚠️${title}
              > 预警等级：${warningLevel}
              > 预警名称：${warningName}
              > 所属问卷：${surveyName}
              > 客户名称：${customerName}
              > 手机号码：${customerPhone}
              > 邮箱：${customerEmail}
              > 客户编号：${customerExternalUserId}
              > 备注：请尽快登录到体验家XM处理！[点击查看](${targetUrl})
          - name: event-group-notify
            msg-type: markdown
            content: |
              ## 报表推送通知
              > Hi，您收到体验家定时推送的报表数据
              > 事件分组名称：${groupName}
              > 推送时间：${pushTime}
              > 数据详情：预警事件数是${countAll}条、其中有${countWait}条待处理事件、${countApplying}条进行中事件、${countSuccess}条已完成事件
              > 详情入口：[点击查看详情](${url})
