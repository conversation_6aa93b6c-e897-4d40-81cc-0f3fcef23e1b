data-access:
  gmp:
    datahub:
      endpoint: ${GMP_DATAHUB_ENDPOINT:}
      project: ${GMP_DATAHUB_PROJECT:}
      topic: ${GMP_DATAHUB_TOPIC:}
      key: ${GMP_DATAHUB_KEY:}
      secret: ${GMP_DATAHUB_SECRET:}
    jsondata: ${GMP_JSONDATA:{"firstName":{"binaryListValues":[],"dataType":"String","stringListValues":[],"stringValue":"${firstName}"},"lastName":{"binaryListValues":[],"dataType":"String","stringListValues":[],"stringValue":"${lastName}"},"surveyLink":{"binaryListValues":[],"dataType":"String","stringListValues":[],"stringValue":"${surveyUrl}"},"IsNewMessage":{"binaryListValues":[],"dataType":"Number","stringListValues":[],"stringValue":"1"},"phone2":{"binaryListValues":[],"dataType":"String","stringListValues":[],"stringValue":"${phone2}"},"survey":{"binaryListValues":[],"dataType":"String","stringListValues":[],"stringValue":"修理"},"id":{"binaryListValues":[],"dataType":"Number","stringListValues":[],"stringValue":"34858380"},"brand":{"binaryListValues":[],"dataType":"Number","stringListValues":[],"stringValue":"${brand}"},"phone1":{"binaryListValues":[],"dataType":"String","stringListValues":[],"stringValue":"${phone1}"},"risnumber":{"binaryListValues":[],"dataType":"Number","stringListValues":[],"stringValue":"${risnumber}"}}}


