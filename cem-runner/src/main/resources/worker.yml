worker:
  lock:
    prefix-name: ${LOCK_NAME_PREFIX:worker.lock}
    ttl: ${LOCK_TTL:10}
  event:
    enabled: ${ENABLE_EVENT_WORKER:true}
    warning:
      enable-notify: ${ENABLE_WARNING_NOTIFY:true}
      event-url: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}cem/event/operateData/
      journey-url: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}cem/journeymap/
    order:
      version:
        notify-url: ${system-notify.url.wework.buy-version}
        adminx-url: ${system-notify.url.adminx}
  task:
    enabled: ${ENABLE_TASK_WORKER:true}
  notify:
    enabled: ${ENABLE_NOTIFY_WORKER:true}
    app: cem
    warning: event-action-warning
    cooperation: event-action-cooperation
    close: event-action-close
    journey: journey-indicator-warning
    default-sms-template: default_sms_event
    org-change-register: org-change-notify-register
    org-change-version: org-change-notify-change-version
  sms:
    callback:
      enable-pull-chuanglan: ${SMS_CALLBACK_ENABLE_PULL_CHUANGLAN:false}
      cron-pull-chuanglan: '9 */5 * * * *'
  simple-daily:
    scheduling:
      enabled: true
      cron: '5 11 3 * * *'
      refresh-youzan-token: ${SIMPLE_DAILY_SCHEDULING_REFRESH_YOUZAN_TOKEN:false}
      data-access-backup: ${SIMPLE_DAILY_SCHEDULING_DATA_ACCESS_BACKUP:true}
      database-trim: ${SIMPLE_DAILY_SCHEDULING_DATABASE_TRIM:true}
  data-access:
    backup:
      retain-days: 30
      batch-size: 500
      trim: true
      backup-table: data_access_cell_backup
      sqlFormatNextId: select id, create_time createTime from %s order by id desc limit 1
      sqlFormatBatch: select id, create_time createTime from data_access_cell where id > %d and id < %d order by id asc limit %d
      sqlFormatInsert: insert into %s (`id`, `access_id`, `status`, `message_id`, `message_data`, `extra_data`, `parsed_params`, `create_time`, `modify_time`) select * from data_access_cell where id >= %d and id <= %d
      sqlFormatDelete: delete from data_access_cell where id >= %d and id <= %d
  database:
    trim:
      retain-days: 90
      trim-by-max-id:
        - retain-days: 90
          trim-table: link
          trim-condition: source = 2
      trim-by-create-time:
        - retain-days: 150
          trim-table: worker_event
        - retain-days: 150
          trim-table: worker_task
  converter:
    json-to-sav:
      url: ${CONVERTER_SPSS_URL:http://converter-backend-svc:5001/converter/spss}
  recharge-refund:
    scheduling:
      enabled: true
      cron: '30 0/10 * * * *'
