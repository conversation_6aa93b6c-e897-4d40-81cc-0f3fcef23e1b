adminx:
  response-submit: ${RESPONSE_SUBMIT_URI:https://dev.xmplus.cn/api/adminx/open/response-submit-final}
  response-view: ${RESPONSE_VIEW_URI:https://dev.xmplus.cn/api/adminx/open/response-view}
  channel-operation: ${CHANNEL_OPERATION_URI:https://dev.xmplus.cn/api/adminx/open/channel-operation}
  response-submit-key: ${RESPONSE_SUBMIT_REDIS_KEY:befun.task.queue.adminx-response-submit}
  response-view-key: ${RESPONSE_VIEW_REDIS_KEY:befun.task.queue.adminx-response-view}
  channel-operation-key: ${CHANNEL_OPERATION_KEY:befun.task.queue.adminx-channel-operation}
