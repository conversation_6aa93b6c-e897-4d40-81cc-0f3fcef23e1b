xmplus:
  domain: ${XMPLUS_DOMAIN:https://dev.xmplus.cn}
  short: ${XMPLUS_SHORT:https://dev-t.xmplus.cn}
server:
  shutdown: graceful
  error:
    whitelabel:
      enabled: false
  port: ${PORT:8089}
  servlet:
    context-path: /api/worker

spring:
  lifecycle:
    timeout-per-shutdown-phase: 60s
  config:
    import:
      - classpath:auth.yml
      - classpath:auth-pay.yml
      - classpath:extension.yml
      - classpath:file.yml
      - classpath:mail.yml
#      - classpath:sms.yml
      - classpath:befun-sms-chuanglan.yml
      - classpath:task.yml
      - classpath:webservice.yml
      - classpath:worker.yml
      - classpath:bi-worker.yml
      - classpath:befun-xpack-wechatopen.yml
      - classpath:befun-xpack-wechatpay.yml
      - classpath:nlp.yml
      - classpath:bot.yml
      - classpath:adminx.yml
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  datasource:
    driverClassName: org.h2.Driver
    jdbc-url: jdbc:h2:mem:CEM_PLATFORM;DB_CLOSE_DELAY=-1
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create
      generate-ddl: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        enable_lazy_load_no_trans: true
data:
  redis.repositories.enabled: false
  jdbc.repositories.enabled: false
  rest:
    default-media-type: application/json
redis:
  host: ${REDIS_HOST:*********}
  port: ${REDIS_PORT:6379}
  password: ${REDIS_PASSWORD:bsxrzQ2l2zm}

hanyi:
  shorturl: ${SHORTURL:https://dev-t.xmplus.cn}

wechat:
  open:
    app_id: ${WECHAT_OPEN_APP_ID:wxbb2e0ad30fee2502}
    app_secret: ${WECHAT_OPEN_APP_SECRET:372012a24728ce35610b37e8eb539538}
    token: ${WECHAT_OPEN_TOKEN:surveyplus}
    aes_key: ${WECHAT_OPEN_AES_KEY:0F68wpOxqNJs4bL5xUoCyrlWinYF6QNPtmYVx8Ex0BB}
    callback: ${WECHAT_OPEN_CALLBACK:https://dev.xmplus.cn/cem/setting/account?from=wechat_open}

logging:
  level:
    root: ${LOG_LEVEL:info}
    org.befun: ${LOG_LEVEL_BEFUN:info}
    org.befun.task.lock: error
    org.befun.task.service: error
    org.hibernate:
      SQL: ${LOG_LEVEL_SQL:error}
      type.descriptor.sql: ${LOG_LEVEL_SQL:error}
    me.chanjar.weixin.mp.api.impl.BaseWxMpServiceImpl: ${LOG_LEVEL_WECHAT:error}


feige:
  template:
    customer: default_sms_customer
ctm:
  info:
    domain: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}
  send-manage-timer:
    scheduling:
      enabled: ${CTM_SENDMANAGETIMER_SCHEDULING_ENABLE:true}
      cron: ${CTM_SENDMANAGETIMER_SCHEDULING_CRON:-}
      interval-minutes: ${CTM_SENDMANAGETIMER_SCHEDULING_INTERVAL:15}
  customer:
    scheduling:
      enabled: ${CTM_CUSTOMER_SCHEDULING_ENABLE:true}
      cron: ${CTM_CUSTOMER_SCHEDULING_CRON:0 0 1 * * *}
      batch-size: ${CTM_CUSTOMER_SCHEDULING_SIZE:100}
  journey:
    scheduling:
      enabled: ${CTM_JOURNEY_SCHEDULING_ENABLE:true}
      cron: ${CTM_JOURNEY_SCHEDULING_CRON:0 1 0 * * *}
      warnings: [ experience_indicator, event_stat ]
    journey-url: ${ctm.info.domain}cem/journeymap/
    email-template-add: journey-add-user
    email-template-remove: journey-remove-user
    email-template-indicator: journey-indicator-warning
    journey-size:
      empty: 0
      base: 1
      update: 5
      profession: 99999
  event:
    enable-notify: ${ENABLE_NOTIFY:true}
    default-group: ctm
    target-url: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}cem/event/operateData/
    notify-topic: ctm_notify_event
    survey-response-topic: survey_response
    survey-change-topic: survey_change
    user-create-topic: queuing-user-create
  notify:
    app: cem
    warning: event-action-warning
    cooperation: event-action-cooperation
    close: event-action-close
    journey: journey-indicator-warning
    default-sms-template: default_sms_event
survey:
  client.enabled: false
  survey-url-prefix:
    root: ${LITE_INFO_DOMAIN:https://dev.xmplus.cn/lite/}

