befun:
  extension:
    wechat-pay:
      enable: ${WECHAT_PAY_ENABLE:false}
      app-id: ${WECHAT_PAY_APP_ID:wx38eb115cf5014c74}
      mch-id: ${WECHAT_PAY_MCH_ID:1521879911}
      v2-mch-key: ${WECHAT_PAY_V2_MCH_KEY:LDPpsBzLOqo6dVjfGj2wWXYZHa9UdcO7}
      v3-mch-key: ${WECHAT_PAY_V3_MCH_KEY:c0R7uY7aiRljM6eXLFYB2HBqZ3kw0KTN}
      cert-p12-path: ${WECHAT_PAY_CERT_P12_PATH:/config/cert/wx/apiclient_cert.p12}
      private-key-path: ${WECHAT_PAY_PRIVATE_KEY_PATH:/config/cert/wx/apiclient_key.pem}
      cert-pem-path: ${WECHAT_PAY_CERT_PEM_PATH:/config/cert/wx/apiclient_cert.pem}
      use-sandbox: ${WECHAT_PAY_USE_SANDBOX:false}
      pay-notify-url: ${WECHAT_PAY_NOTIFY_URL:${xmplus.domain}/api/auth/wechatPay/placeOrder/callback/cem}
  auth:
    recharge:
      expire-minute: ${AUTH_PAY_EXPIRE_MINUTE:120}
      mock-pay-url: ${AUTH_PAY_MOCK_URL:${xmplus.domain}/api/auth/organization/wallet/recharge/mockPay?rechargeId=%d&rechargeType=%s}
      service-rate:
        recharge_wechat: ${AUTH_PAY_WECHAT_SERVICE_RATE:0.01}
        recharge_alipay: ${AUTH_PAY_ALIPAY_SERVICE_RATE:0.01}
      enabled:
        recharge_wechat: false
        recharge_alipay: false
  order:
    sms:
      price: ${AUTH_PAY_SMS_PRICE:8}


