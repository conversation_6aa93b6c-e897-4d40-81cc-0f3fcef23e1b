prod-env: ${PROD_ENV:false}
befun:
  auth:
    youzan:
      client-id: ${YOUZAN_CLIENT_ID:165f362a0b87ffca6f}
      client-secret: ${YOUZAN_SECRET_ID:c1e3aeb4dd25a4341dc0f8575dfb98c1}
    apps:
      - cem
    wechat-mp-sources:
      - name: cem
        app-id: ${WECHAT_MP_CEM_APP_ID:wx6b8f8ad170862e83}
        app-secret: ${WECHAT_MP_CEM_APP_SECRET:b0e23de9f72b7578e9537845f07b3a98}
        token: ${WECHAT_MP_CEM_TOKEN:hanyidata}
        templates:
          - id: ${WECHAT_MP_CEM_TEMPLATE_ID_WARNING:uSJ3SHBhhxE7qttyoO3NTRk69IMNBtW7pcNmug3bmVU}
            name: event-action-warning
            parameters:
              - name: first
                value: 您收到了一条事件预警
              - name: keyword1
                value: ${warningTime}
              - name: keyword2
                value: 事件预警
              - name: keyword3
                value: ${warningTitle}
              - name: remark
                value: 请尽快登录体验家XM处理!
          - id: ${WECHAT_MP_CEM_TEMPLATE_ID_COOPERATION:8fNbp1YQ1qkWULjnzYiFj-aEXdswimiH0OrFk4B6L9E}
            name: event-action-cooperation
            parameters:
              - name: first
                value: 您收到了一条协作邀请通知
              - name: keyword1
                value: ${content}
              - name: keyword2
                value: ${formUserName}
              - name: keyword3
                value: ${formUserPhone}
              - name: remark
                value: 可登录体验家XM处理!
          - id: ${WECHAT_MP_CEM_TEMPLATE_ID_CLOSE:tNnrom_phf424_u4-UliQXtJctBBPMTDle4786IFYvs}
            name: event-action-close
            parameters:
              - name: first
                value: ${formUserName}关闭了一条预警事件
              - name: keyword1
                value: ${eventTime}
              - name: keyword2
                value: ${closeTime}
              - name: keyword3
                value: 处理完成${warningTitle}
              - name: remark
                value: 可登录体验家XM查看事件处理详情!
          - id: ${WECHAT_MP_CEM_TEMPLATE_ID_WARNING:uSJ3SHBhhxE7qttyoO3NTRk69IMNBtW7pcNmug3bmVU}
            name: journey-indicator-warning
            parameters:
              - name: first
                value: 您收到了一条事件预警
              - name: keyword1
                value: ${warningTime}
              - name: keyword2
                value: 事件预警
              - name: keyword3
                value: ${JourneyWarningTitle}
              - name: remark
                value: 请尽快登录体验家XM处理!
    wechat-work:
      corp-id: ${WECHAT_WORK_CORP_ID:ww87224825795b8b14}
      corp-secret: ${WECHAT_WORK_CORP_SECRET:A9yEb8ehnR9T9P5x7-e0b05HGTAp-5iNrlHXTQ2U9CnrUa8Qiy20LzOZt0yAOEnU}
      suite-id: ${WECHAT_WORK_SUITE_ID:wwa6d398bb65010e5e}
      suite-secret: ${WECHAT_WORK_SUITE_SECRET:UNiPlpLcTXAN35klwB6zZnOOQqlbF3QX7BtbsEUTiIo}
      templates:
        - name: event-action-warning
          id: ${WECHAT_WORK_TEMPLATE_WARNING_ID:tta6EfDQAAmgJMaHf-At_0n_eWiUH4zQ}
          url: ${ctm.info.domain}cem-m/event/operateData/${eventId}
          parameters:
            - name: 预警内容
              value: ${warningTitle}
            - name: 预警时间
              value: ${warningTime}
            - name: 备注
              value: 请尽快登录体验家XM处理！
        - name: event-action-cooperation
          id: ${WECHAT_WORK_TEMPLATE_COOPERATION_ID:tta6EfDQAAPWSlSn2a-XpBugmZEIYDdA}
          url: ${ctm.info.domain}cem-m/event/operateData/${eventId}
          parameters:
            - name: 预警内容
              value: ${warningTitle}
            - name: 协作发起人
              value: ${formUserName}
            - name: 备注
              value: 请尽快登录体验家XM处理！
        - name: event-action-close
          id: ${WECHAT_WORK_TEMPLATE_CLOSE_ID:tta6EfDQAAyYb79gx54U2NSiAolelAdA}
          url: ${ctm.info.domain}cem-m/event/operateData/${eventId}
          parameters:
            - name: 预警内容
              value: ${warningTitle}
            - name: 事件关闭人
              value: ${formUserName}
            - name: 备注
              value: 请尽快登录体验家XM处理！
        - name: journey-indicator-warning
          id: ${WECHAT_WORK_TEMPLATE_WARNING_ID:tta6EfDQAAmgJMaHf-At_0n_eWiUH4zQ}
          url: ${ctm.info.domain}cem-m/event/index
          parameters:
            - name: 预警内容
              value: ${JourneyWarningTitle}
            - name: 预警时间
              value: ${warningTime}
            - name: 备注
              value: 请尽快登录体验家XM处理！
        - name: dashboard-push
          id: ${WECHAT_WORK_TEMPLATE_DSHBOARD_ID:tta6EfDQAAE-7uHhhMPVSxGP7XlFTN9Q}
          url: ${dashboardUrl}
          parameters:
            - name: 仪表盘名称
              value: ${dashboardName}
            - name: 推送时间
              value: ${pushTime}
