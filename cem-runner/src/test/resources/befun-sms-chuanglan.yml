befun:
  extension:
    sms:
      vendor: chuanglan
      enable: ${SMS_ENABLE:false}
      enable-limit: ${ENABLE_LIMIT_SMS:true}
      enable-chuanglan: ${SMS_ENABLE_CHAUNGLAN:false}
      enable-feige: ${SMS_ENABLE_FEIGE:false}
      providers:
        - name: chuang<PERSON>
          chuanglan:
            app-id: ${CHUANGLAN_APP_ID:N429678_N9266634}
            app-secret: ${CHUANGLAN_APP_SECRET:hCgkn67VGxb6d2}
            signature: ${CHUANGLAN_SIGNATURE:【瀚一数据DEV】}
            real-signature: ${CHUANGLAN_REAL_SIGNATURE:【瀚一数据DEV】}
          templates:
            - name: verify-code
              id: ${CHUANGLAN_TEMPLATE_ID_VERIFY_CODE:116170}
              content: 您的验证码是${code}，请于5分钟内填写。如非本人操作，请忽略本短信
              pattern: ^您的验证码是(\d+)，请于5分钟内填写。如非本人操作，请忽略本短信$
              origin-template: '您的验证码是{$var}，请于5分钟内填写。如非本人操作，请忽略本短信'
              variables: '{$var}'
            - name: event-action-warning
              id: ${CHUANGLAN_TEMPLATE_ID_EVENT_WARNING:121745}
              content: ${targetTruename}，您好！您收到一条${warningLevelSimple}预警事件：${warningTitle}，请尽快登录体验家XM处理！
              pattern: ^(.+)，您好！您收到一条(.+)预警事件：(.+)，请尽快登录体验家XM处理！$
              origin-template: '{$var}，您好！您收到一条{$var}预警事件：{$var}，请尽快登录体验家XM处理！'
              variables: '{$var},{$var},{$var}'
            - name: event-action-cooperation
              id: ${CHUANGLAN_TEMPLATE_ID_EVENT_COOPERATION:121746}
              content: ${targetTruename}，您好！您收到一条${formUserName}的事件协作邀请，请尽快登录体验家XM处理！
              pattern: ^(.+)，您好！您收到一条(.+)的事件协作邀请，请尽快登录体验家XM处理！$
              origin-template: '{$var}，您好！您收到一条{$var}的事件协作邀请，{$var}，请尽快登录体验家XM处理！'
              variables: '{$var},{$var}'
            - name: journey-indicator-warning
              id: ${CHUANGLAN_TEMPLATE_ID_EVENT_JOURNEY:123638}
              content: ${targetTruename}，您好！您收到一条指标预警：${indicatorName}的值为${currentValue}(${indicatorCompareLabel})，请尽快登录体验家XM处理！
              pattern: ^(.+)，您好！您收到一条指标预警：(.+)的值为(.+)\((.+)\)，请尽快登录体验家XM处理！$
              origin-template: '{$var}，您好！您收到一条指标预警：{$var}的值为{$var}({$var})，请尽快登录体验家XM处理！'
              variables: '{$var},{$var},{$var},{$var}'
            - name: event-notify-customer
              id: ${CHUANGLAN_TEMPLATE_ID_EVENT_NOTIFY_CUSTOMER:116172}
              content: 尊敬的${customer.username}，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。
              pattern: ^尊敬的(.+)，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。$
              origin-template: '尊敬的{$var}，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。'
              variables: '{$var}'
            - name: org-change-notify-register
              id: ${CHUANGLAN_TEMPLATE_ID_ORG_CHANGE_NOTIFY_REGISTER:116172}
              content: 感谢您注册体验家CEM，您目前使用的是免费版，我们的客户代表稍后会致电您，了解您的使用场景和需求，可为您开通高级版本试用，期待和您的沟通！
              pattern: ^感谢您注册体验家CEM，您目前使用的是免费版，我们的客户代表稍后会致电您，了解您的使用场景和需求，可为您开通高级版本试用，期待和您的沟通！$
              origin-template: '感谢您注册体验家CEM，您目前使用的是免费版，我们的客户代表稍后会致电您，了解您的使用场景和需求，可为您开通高级版本试用，期待和您的沟通！'
              variables: ''
            - name: org-change-notify-change-version
              id: ${CHUANGLAN_TEMPLATE_ID_ORG_CHANGE_NOTIFY_CHANGE_VERSION:116172}
              content: 您的体验家系统已经升级为${appVersion}版本，体验家能够帮助您自动化收集-预警-分析客户体验，实时管理客户体验，祝您生活愉快。
              pattern: ^您的体验家系统已经升级为(.+)版本，体验家能够帮助您自动化收集-预警-分析客户体验，实时管理客户体验，祝您生活愉快。$
              origin-template: '您的体验家系统已经升级为{$var}版本，体验家能够帮助您自动化收集-预警-分析客户体验，实时管理客户体验，祝您生活愉快。'
              variables: '{$var}'
            - name: PLATFORM_FEEDBACK
              id: ${CHUANGLAN_TEMPLATE_ID_PLATFORM_FEEDBACK:116174}
              content: 尊敬的${customer.username}，您好！感谢您的光临，为了带给您更好的服务体验，请对我们做出评价：${xmplus.short}/${url.code}
              pattern: ^尊敬的(.+)，您好！感谢您的光临，为了带给您更好的服务体验，请对我们做出评价：${xmplus.short}/(\w+)$
              origin-template: '尊敬的{$var}，您好！感谢您的光临，为了带给您更好的服务体验，请对我们做出评价：${xmplus.short}/{$var}'
              variables: '{$var},{$var}'
              parameters: '[{"name": "customer.username", "title": "姓名"}]'
            - name: PLATFORM_EVENT_ACTION
              id: ${CHUANGLAN_TEMPLATE_ID_PLATFORM_EVENT_ACTION:116172}
              content: 尊敬的${customer.username}，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。
              pattern: ^尊敬的(.+)，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。$
              origin-template: '尊敬的{$var}，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。'
              variables: '{$var}'
              parameters: '[{"name": "customer.username", "title": "姓名"}]'
            - name: PLATFORM_RESEARCH
              id: ${CHUANGLAN_TEMPLATE_ID_PLATFORM_RESEARCH:119569}
              content: 尊敬的${customer.username}，您好！特邀您参加本次问卷调查，点击链接马上填答：${xmplus.short}/${url.code}
              pattern: ^尊敬的(.+)，您好！特邀您参加本次问卷调查，点击链接马上填答：${xmplus.short}/(\w+)$
              origin-template: '尊敬的{$var}，您好！特邀您参加本次问卷调查，点击链接马上填答：${xmplus.short}/{$var}'
              variables: '{$var},{$var}'
              parameters: '[{"name": "customer.username", "title": "姓名"}]'
