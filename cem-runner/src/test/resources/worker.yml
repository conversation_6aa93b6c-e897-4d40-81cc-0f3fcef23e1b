worker:
  lock:
    prefix-name: ${LOCK_NAME_PREFIX:worker.lock}
    ttl: ${LOCK_TTL:10}
  event:
    enabled: ${ENABLE_EVENT_WORKER:false}
    warning:
      enable-notify: ${ENABLE_WARNING_NOTIFY:true}
      event-url: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}cem/event/operateData/
      journey-url: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}cem/journeymap/
  task:
    enabled: ${ENABLE_TASK_WORKER:false}
  notify:
    enabled: ${ENABLE_NOTIFY_WORKER:false}
    app: cem
    warning: event-action-warning
    cooperation: event-action-cooperation
    close: event-action-close
    journey: journey-indicator-warning
    default-sms-template: default_sms_event
    org-change-register: org-change-notify-register
    org-change-version: org-change-notify-change-version
  sms:
    callback:
      enable-pull-chuanglan: false
      cron-pull-chuanglan: '9 */5 * * * *'
  simple-daily:
    scheduling:
      enabled: false
      cron: '5 11 3 * * *'
      refresh-youzan-token: ${SIMPLE_DAILY_SCHEDULING_REFRESH_YOUZAN_TOKEN:false}