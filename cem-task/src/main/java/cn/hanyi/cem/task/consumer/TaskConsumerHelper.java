package cn.hanyi.cem.task.consumer;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.consumer.WorkerConsumerHelper;
import cn.hanyi.cem.core.dto.task.progress.TaskProgressiveDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.repository.CemTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.service.UserTaskService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 每一个队列都有自己独立的 帮助类
 */
@Slf4j
public abstract class TaskConsumerHelper extends WorkerConsumerHelper<CemTask, CemTaskRepository, ITaskConsumer<Object>> {

    @Autowired(required = false)
    private List<ITaskConsumer<?>> taskConsumers;
    @Autowired
    private UserTaskService userTaskService;

    private final Map<TaskType, ITaskConsumer<Object>> allConsumerMap = new HashMap<>();

    @PostConstruct
    @SuppressWarnings("unchecked")
    public void init() {
        Optional.ofNullable(taskConsumers).ifPresent(list -> {
            list.forEach(consumer -> {
                ITaskConsumer<?> existConsumer = allConsumerMap.get(consumer.type());
                if (existConsumer != null) {
                    throw new RuntimeException(String.format("任务类型 %s, 有多个处理器。%s, %s", consumer.type().name(), consumer.name(), existConsumer.name()));
                }
                allConsumerMap.put(consumer.type(), (ITaskConsumer<Object>) consumer);
            });
        });
    }

    @Override
    public List<ITaskConsumer<Object>> getAllConsumers(CemTask entity) {
        return Optional.ofNullable(allConsumerMap.get(entity.getType())).map(List::of).orElse(null);
    }

    @Override
    public Object parseParam(CemTask entity) {
        return JsonHelper.toObject(entity.getContent(), entity.getType().getParamClass());
    }

    @Override
    protected void autoUpdateProgress(ITaskConsumer<Object> consumer, CemTask entity, ConsumerStatus consumerStatus, Object param) {
        if (consumer instanceof ITaskAutoUpdateProgressConsumer && param instanceof TaskProgressiveDto) {
            TaskProgressiveDto p = (TaskProgressiveDto) param;
            if (p.getTaskProgressId() != null && p.getTaskProgressId() > 0) {
                int successSize;
                int failedSize;
                if (consumerStatus == ConsumerStatus.SUCCESS) {
                    successSize = p.getSuccessSize() > 0 ? p.getSuccessSize() : p.getTotalSize();
                    failedSize = p.getTotalSize() - successSize;
                } else {
                    failedSize = p.getFailedSize() > 0 ? p.getFailedSize() : p.getTotalSize();
                    successSize = p.getTotalSize() - failedSize;
                }
                userTaskService.updateTaskActiveTime(p.getTaskProgressId());
                if ((successSize != 0 || failedSize != 0)) {
                    userTaskService.updateTaskProgress(p.getTaskProgressId(), NumberUtils.max(0, successSize), NumberUtils.max(0, failedSize));
                }
            }
        }
    }

}
