package cn.hanyi.cem.task.consumer.customer;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerBatchAddJourneyDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.ctm.dto.customer.CustomerAddJourneyDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.entity.SendManageRecord;
import cn.hanyi.ctm.sendmanage.SendMangeJourneyHelper;
import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.ctm.service.SendManageService;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.UserTaskService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.sms.ISmsAccountService;
import org.befun.task.constant.TaskStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

@Component
public class CustomerBatchAddJourneyTaskConsumer implements ITaskConsumer<TaskCustomerBatchAddJourneyDto> {

    @Autowired
    private CustomerService customerService;
    @Autowired
    private SendMangeJourneyHelper sendMangeJourneyHelper;
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private ISmsAccountService smsAccountService;
    @Autowired
    private SendManageService sendManageService;

    @Override
    public TaskType type() {
        return TaskType.CUSTOMER_BATCH_ADD_JOURNEY;
    }

    @Override
    @Transactional
    public ConsumerStatus consumer(CemTask entity, TaskCustomerBatchAddJourneyDto param) {
        CustomerAddJourneyDto data = JsonHelper.toObject(param.getData(), CustomerAddJourneyDto.class);
        Long sendManageId;
        if (data == null || (sendManageId = data.getSendManageId()) == null) {
            entity.setResponse("参数解析失败");
            userTaskService.failedTask(param.getTaskProgressId(), entity.getResponse());
            return ConsumerStatus.FAILED;
        }
        SendManage sendManage = sendManageService.get(sendManageId);
        if (sendManage == null || sendManage.getEnable() == null || !sendManage.getEnable()) {
            entity.setResponse("发送管理已关闭");
            userTaskService.failedTask(param.getTaskProgressId(), entity.getResponse());
            return ConsumerStatus.FAILED;
        }
        Long orgId = entity.getOrgId();
        Long userId = entity.getUserId();
        Long taskProgressId = param.getTaskProgressId();
        AtomicInteger countSuccess = new AtomicInteger();
        foreachCustomers(orgId, userId, taskProgressId, data, countSuccess);
        if (taskProgressId != null) {
            if (countSuccess.get() > 0) {
                userTaskService.updateTaskStatus(taskProgressId, TaskStatus.RUNNING);
            } else {
                userTaskService.updateTaskStatus(taskProgressId, TaskStatus.SUCCESS);
            }
        }
        return ConsumerStatus.SUCCESS;
    }


    private void foreachCustomers(Long orgId,
                                  Long userId,
                                  Long taskProgressId,
                                  CustomerAddJourneyDto data,
                                  AtomicInteger countSuccess) {
        customerService.consumerSelectCustomerIds(orgId, userId, data,
                ids -> ids.forEach(customerId -> triggerJourneyByCustomerId(orgId, userId, taskProgressId, data, countSuccess, customerId)),
                mockCustomer -> triggerJourneyByMockCustomer(orgId, userId, taskProgressId, data, countSuccess, mockCustomer));
    }

    private void triggerJourneyByCustomerId(Long orgId,
                                            Long userId,
                                            Long taskProgressId,
                                            CustomerAddJourneyDto data,
                                            AtomicInteger countSuccess,
                                            Long customerId) {
        Map<String, Object> params = data.getUrlCustomParams() == null ? new HashMap<>() : data.getUrlCustomParams();
        triggerJourneyByCustomer(orgId, taskProgressId, countSuccess,
                () -> sendMangeJourneyHelper.sendByJourney(
                        orgId,
                        userId,
                        data.getSendManageId(),
                        data.getJourneyId(),
                        customerId,
                        data.getDepartmentId(),
                        taskProgressId,
                        params));
    }

    private void triggerJourneyByMockCustomer(Long orgId,
                                              Long userId,
                                              Long taskProgressId,
                                              CustomerAddJourneyDto data,
                                              AtomicInteger countSuccess,
                                              Customer customer) {
        Map<String, Object> params = data.getUrlCustomParams() == null ? new HashMap<>() : data.getUrlCustomParams();
        // 通过客户编号查询一遍，是否存在这个客户
        if (StringUtils.isNotEmpty(customer.getExternalUserId())) {
            Customer replaceCustomer = customerService.findCustomer(orgId, null, customer.getExternalUserId(), null, null);
            if (replaceCustomer != null) {
                customer.setId(replaceCustomer.getId());
                if (StringUtils.isEmpty(customer.getUsername())) {
                    customer.setUsername(replaceCustomer.getUsername());
                }
                if (StringUtils.isEmpty(customer.getMobile())) {
                    customer.setMobile(replaceCustomer.getMobile());
                }
                if (StringUtils.isEmpty(customer.getEmail())) {
                    customer.setEmail(replaceCustomer.getEmail());
                }
            }
        }
        triggerJourneyByCustomer(orgId, taskProgressId, countSuccess,
                () -> sendMangeJourneyHelper.sendByJourney(
                        orgId,
                        userId,
                        data.getSendManageId(),
                        data.getJourneyId(),
                        customer,
                        data.getDepartmentId(),
                        taskProgressId,
                        params));
    }

    private void triggerJourneyByCustomer(Long orgId,
                                          Long taskProgressId,
                                          AtomicInteger countSuccess,
                                          Supplier<SendManageRecord> sendByJourney) {
        SendManageRecord record = sendByJourney.get();
        boolean success = record != null && !record.getStatus().isCompleted();
        if (success) {
            countSuccess.incrementAndGet();
        } else if (taskProgressId != null) {
            userTaskService.appendTaskFailedSize(taskProgressId, 1);
        }
    }
}
