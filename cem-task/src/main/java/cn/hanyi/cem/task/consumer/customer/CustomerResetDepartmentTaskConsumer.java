package cn.hanyi.cem.task.consumer.customer;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerResetDepartmentDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.ctm.service.CustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class CustomerResetDepartmentTaskConsumer implements ITaskConsumer<TaskCustomerResetDepartmentDto> {


    @Autowired
    private CustomerService customerService;

    @Override
    public TaskType type() {
        return TaskType.CUSTOMER_RESET_DEPARTMENT;
    }

    @Override
    @Transactional
    public ConsumerStatus consumer(CemTask entity, TaskCustomerResetDepartmentDto param) {
        customerService.resetCustomerDepartment(param.getOrgId());
        return ConsumerStatus.SUCCESS;
    }


}
