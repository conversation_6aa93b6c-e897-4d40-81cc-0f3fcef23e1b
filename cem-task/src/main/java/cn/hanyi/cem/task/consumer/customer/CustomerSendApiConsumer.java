package cn.hanyi.cem.task.consumer.customer;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerSendApiDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.customer.send.CustomerSendApiHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CustomerSendApiConsumer extends CustomerSendConsumer<TaskCustomerSendApiDto> {

    @Autowired
    private CustomerSendApiHelper customerSendApiHelper;

    @Override
    public TaskType type() {
        return TaskType.CUSTOMER_SEND_API;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskCustomerSendApiDto param) {
        return consumer(entity, param, result -> customerSendApiHelper.run(entity.getOrgId(), entity, param.getTaskProgressId(), param.getFromType(), param.getFromId(), param, false, result));
    }

}
