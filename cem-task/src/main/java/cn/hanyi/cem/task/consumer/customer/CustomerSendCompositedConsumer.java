package cn.hanyi.cem.task.consumer.customer;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerSendCompositedDto;
import cn.hanyi.cem.core.dto.task.send.SendSmsInfo;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.customer.send.CustomerSendApiHelper;
import cn.hanyi.cem.task.consumer.customer.send.CustomerSendEmailHelper;
import cn.hanyi.cem.task.consumer.customer.send.CustomerSendSmsHelper;
import cn.hanyi.cem.task.consumer.customer.send.CustomerSendWechatHelper;
import lombok.extern.slf4j.Slf4j;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Component
public class CustomerSendCompositedConsumer extends CustomerSendConsumer<TaskCustomerSendCompositedDto> {

    @Autowired
    private CustomerSendSmsHelper customerSendSmsHelper;
    @Autowired
    private CustomerSendWechatHelper customerSendWechatHelper;
    @Autowired
    private CustomerSendEmailHelper customerSendEmailHelper;
    @Autowired
    private CustomerSendApiHelper customerSendApiHelper;
    @Autowired
    private ISmsAccountService smsAccountService;

    @Override
    public TaskType type() {
        return TaskType.CUSTOMER_SEND_COMPOSITED;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskCustomerSendCompositedDto param) {
        SendDetailResult sendDetailResult = new SendDetailResult();
        return consumerWithSms(entity, param,
                sendDetailResult,
                result -> {
                    boolean success = false;
                    if (param.checkCurrentTime()) {
                        success = run(entity.getOrgId(), entity, param, result, sendDetailResult);
                        result.setSuccess(success);
                    } else {
                        entity.setResponse(String.format("当前时间（%s）已超过发送截止时间（%s）", LocalDateTime.now(), param.getDeadTime()));
                    }
                    return success;
                },
                result -> {
                    SendSmsInfo smsInfo = param.getSms().size() == 1 ? param.getSms().get(0) : null;
                    if (smsInfo != null) {
                        boolean smsSuccess = Optional.ofNullable(result.getCompositedSuccess().get("SMS")).orElse(false);
                        customerSendSmsHelper.smsCost(entity.getOrgId(), entity.getUserId(), smsSuccess, param.isSmsPostPaid(), param.getTaskProgressId(), param.getFromType(), param.getFromId(), smsInfo);
                    }
                });
    }

    private boolean run(Long orgId, CemTask entity, TaskCustomerSendCompositedDto dto, CustomerSendResult result, SendDetailResult sendDetailResult) {
        return dto.prioritySend(sms -> {
            boolean s = customerSendSmsHelper.run(orgId, entity, dto.isSmsPostPaid(), dto.getTaskProgressId(), dto.getFromType(), dto.getFromId(), sms, new CustomerSendResult(), sendDetailResult);
            result.getCompositedSuccess().put("SMS", s);
            return s;
        }, wechat -> {
            boolean s = customerSendWechatHelper.run(orgId, entity, dto.getTaskProgressId(), dto.getFromType(), dto.getFromId(), wechat, new CustomerSendResult(), sendDetailResult);
            result.getCompositedSuccess().put("WECHAT", s);
            return s;
        }, email -> {
            boolean s = customerSendEmailHelper.run(orgId, entity, dto.getTaskProgressId(), dto.getFromType(), dto.getFromId(), email, new CustomerSendResult(), sendDetailResult);
            result.getCompositedSuccess().put("EMAIL", s);
            return s;
        }, api -> {
            boolean s = customerSendApiHelper.run(orgId, entity, dto.getTaskProgressId(), dto.getFromType(), dto.getFromId(), api, dto.isRemind(), new CustomerSendResult());
            result.getCompositedSuccess().put("APP-" + api.getConnectorId(), s);
            return s;
        });
    }
}
