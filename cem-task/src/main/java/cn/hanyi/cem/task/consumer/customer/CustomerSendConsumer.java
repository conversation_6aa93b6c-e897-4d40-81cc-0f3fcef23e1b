package cn.hanyi.cem.task.consumer.customer;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.progress.TaskProgressiveDto;
import cn.hanyi.cem.core.dto.task.send.SendFromInfo;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskAutoUpdateProgressConsumer;
import cn.hanyi.ctm.constant.SendManageRecordStatus;
import cn.hanyi.ctm.entity.CustomerAnswers;
import cn.hanyi.ctm.entity.SendManageRecord;
import cn.hanyi.ctm.repository.CustomerAnswersRepository;
import cn.hanyi.ctm.repository.SendManageRecordRepository;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.service.EventActionService;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import cn.hanyi.survey.core.entity.SurveySendRecord;
import cn.hanyi.survey.core.repository.SurveySendRecordRepository;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.UserTaskService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.constant.TaskStatus;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

@Slf4j
public abstract class CustomerSendConsumer<P extends TaskProgressiveDto & SendFromInfo> implements ITaskAutoUpdateProgressConsumer<P> {

    @Autowired
    private SurveySendRecordRepository surveySendRecordRepository;
    @Autowired
    private SendManageRecordRepository sendManageRecordRepository;
    @Autowired
    private SendManageRepository sendManageRepository;
    @Autowired
    private CustomerAnswersRepository customerAnswersRepository;
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private EventActionService eventActionService;

    protected ConsumerStatus consumer(CemTask entity, P param, Function<CustomerSendResult, Boolean> run) {
        ConsumerStatus status;
        CustomerSendResult result = new CustomerSendResult(false, null);
        if (isTaskCanceled(param.getTaskProgressId())) {
            status = ConsumerStatus.CANCELED;
        } else {
            status = run.apply(result) ? ConsumerStatus.SUCCESS : ConsumerStatus.FAILED;
        }
        afterSendCompleted(entity.getOrgId(), entity.getUserId(), result, param.getFromType(), param.getFromId(), new SendDetailResult());
        return status;
    }

    protected ConsumerStatus consumerWithSms(CemTask entity, P param, SendDetailResult sendDetailResult, Function<CustomerSendResult, Boolean> run, Consumer<CustomerSendResult> finallyRun) {
        ConsumerStatus status;
        CustomerSendResult result = new CustomerSendResult(false, null);
        try {
            if (isTaskCanceled(param.getTaskProgressId())) {
                status = ConsumerStatus.CANCELED;
            } else {
                status = run.apply(result) ? ConsumerStatus.SUCCESS : ConsumerStatus.FAILED;
            }
            afterSendCompleted(entity.getOrgId(), entity.getUserId(), result, param.getFromType(), param.getFromId(), sendDetailResult);
        } finally {
            finallyRun.accept(result);
        }
        return status;
    }

    protected boolean isTaskCanceled(Long taskProgressId) {
        if (taskProgressId != null && taskProgressId > 0) {
            return Optional.ofNullable(userTaskService.progress(taskProgressId).getStatus()).orElse(TaskStatus.NONE) == TaskStatus.CANCELED;
        }
        return false;
    }

    protected void afterSendCompleted(Long orgId, Long userId, CustomerSendResult result, CustomerSendFromType fromType, Long fromId, SendDetailResult sendDetailResult) {
        if (fromType == null || fromId == null || fromId <= 0) {
            return;
        }
        Long surveyId = null;
        Long customerId = null;
        String clientId = null;
        if (fromType == CustomerSendFromType.SURVEY_CHANNEL) {
            SurveySendRecord record = surveySendRecordRepository.findById(fromId).orElse(null);
            if (record != null) {
                surveyId = record.getSurveyId();
                customerId = record.getCustomerId();
                clientId = record.getClientId();
                record.setSendStatus(result.isSuccess() ? SendStatus.SEND_SUCCESS : SendStatus.SEND_FAIL);
                surveySendRecordRepository.save(record);
            }
        } else if (fromType == CustomerSendFromType.SEND_MANAGE) {
            SendManageRecord record = sendManageRecordRepository.findById(fromId).orElse(null);
            if (record != null) {
                surveyId = record.getSurveyId();
                customerId = record.getCustomerId();
                clientId = record.getClientId();
                record.setSendStatus(result.success ? SendStatus.SEND_SUCCESS : SendStatus.SEND_FAIL);
                record.setStatus(SendManageRecordStatus.COMPLETE);
                record.setSendChannelStatus(JsonHelper.toJson(result.getCompositedSuccess()));

                // bi用于计算的字段 其他无用
                record.setAccount(sendDetailResult.getAccount());

                sendManageRecordRepository.save(record);
            }
        } else if (fromType == CustomerSendFromType.EVENT_NOTIFY_CUSTOMER) {
            if (type() == TaskType.CUSTOMER_SEND_SMS) {
                eventActionService.addSendSmsAction(orgId, userId, fromId, result.getContent(), result.isSuccess());
            } else if (type() == TaskType.CUSTOMER_SEND_WECHAT) {
                eventActionService.addSendWechatAction(orgId, userId, fromId, result.getContent(), result.isSuccess());
            }
        }
        if (surveyId != null && customerId != null && StringUtils.isNotEmpty(clientId)) {
            String sid = surveyId.toString();
            String utmCampaign = clientId;
            String utmMedium = fromType.name();
            Optional<CustomerAnswers> customerAnswer = customerAnswersRepository.findFirstByCustomerIdAndSidAndUtmMediumAndUtmCampaign(customerId, sid, utmMedium, utmCampaign);
            if (customerAnswer.isPresent()) {
                CustomerAnswers answer = customerAnswer.get();
                if (answer.getSendStatus() != null && (
                        answer.getSendStatus() == cn.hanyi.ctm.constant.SendStatus.UNCHECKED || answer.getSendStatus() == cn.hanyi.ctm.constant.SendStatus.FAILED
                )) {
                    answer.setSendStatus(result.isSuccess() ? cn.hanyi.ctm.constant.SendStatus.SUCCESS : cn.hanyi.ctm.constant.SendStatus.FAILED);
                    customerAnswersRepository.save(answer);
                }
            }
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class CustomerSendResult {
        private boolean success;
        private String content;
        private Map<String, Boolean> compositedSuccess = new HashMap<>();

        public CustomerSendResult(boolean success, String content) {
            this.success = success;
            this.content = content;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class SendDetailResult {
        @Schema(description = "记录最后一次发送成功的账号bi统计 其他无用")
        private String account;
    }

}