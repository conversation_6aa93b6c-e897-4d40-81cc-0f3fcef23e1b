package cn.hanyi.cem.task.consumer.customer;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerSendEmailDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.customer.send.CustomerSendEmailHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CustomerSendEmailConsumer extends CustomerSendConsumer<TaskCustomerSendEmailDto> {

    @Autowired
    private CustomerSendEmailHelper customerSendEmailHelper;

    @Override
    public TaskType type() {
        return TaskType.CUSTOMER_SEND_EMAIL;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskCustomerSendEmailDto param) {
        return consumer(entity, param, result -> customerSendEmailHelper.run(entity.getOrgId(), entity, param.getTaskProgressId(), param.getFromType(), param.getFromId(), param, result));
    }

}