package cn.hanyi.cem.task.consumer.customer;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerSendSmsDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.customer.send.CustomerSendSmsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CustomerSendSmsConsumer extends CustomerSendConsumer<TaskCustomerSendSmsDto> {

    @Autowired
    private CustomerSendSmsHelper customerSendSmsHelper;

    @Override
    public TaskType type() {
        return TaskType.CUSTOMER_SEND_SMS;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskCustomerSendSmsDto param) {
        return consumerWithSms(entity, param,
                new SendDetailResult(),
                result -> customerSendSmsHelper.run(entity.getOrgId(), entity, false, param.getTaskProgressId(), param.getFromType(), param.getFromId(), param, result),
                result -> customerSendSmsHelper.smsCost(entity.getOrgId(), entity.getUserId(), result.isSuccess(), false, param.getTaskProgressId(), param.getFromType(), param.getFromId(), param));
    }
}