package cn.hanyi.cem.task.consumer.customer;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskCustomerSendWechatDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.customer.send.CustomerSendWechatHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CustomerSendWechatConsumer extends CustomerSendConsumer<TaskCustomerSendWechatDto> {

    @Autowired
    private CustomerSendWechatHelper customerSendWechatHelper;

    @Override
    public TaskType type() {
        return TaskType.CUSTOMER_SEND_WECHAT;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskCustomerSendWechatDto param) {
        return consumer(entity, param, result -> customerSendWechatHelper.run(entity.getOrgId(), entity, param.getTaskProgressId(), param.getFromType(), param.getFromId(), param,result));
    }

}

