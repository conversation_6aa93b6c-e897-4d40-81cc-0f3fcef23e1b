//package cn.hanyi.cem.task.consumer.customer;
//
//import cn.hanyi.cem.core.constant.ConsumerStatus;
//import cn.hanyi.cem.core.constant.TaskType;
//import cn.hanyi.cem.core.dto.task.TaskCustomerThirdpartyAddJourneyDto;
//import cn.hanyi.cem.core.entity.CemTask;
//import cn.hanyi.cem.task.consumer.ITaskConsumer;
//import cn.hanyi.cem.task.consumer.wechatopen.WechatOpenSyncCustomerInfoTaskConsumer;
//import cn.hanyi.ctm.entity.Customer;
//import cn.hanyi.ctm.entity.ThirdPartyCustomer;
//import cn.hanyi.ctm.repository.CustomerRepository;
//import cn.hanyi.ctm.repository.ThirdPartyCustomerRepository;
//import cn.hanyi.ctm.sendmanage.SendMangeJourneyHelper;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.befun.auth.constant.ThirdPartyAuthType;
//import org.befun.auth.entity.ThirdPartyAuth;
//import org.befun.auth.entity.ThirdPartyMessageYouzan;
//import org.befun.auth.repository.ThirdPartyMessageYouzanRepository;
//import org.befun.auth.service.DepartmentService;
//import org.befun.auth.service.ThirdPartyAuthService;
//import org.befun.auth.service.auth.AuthWechatOpenService;
//import org.befun.auth.service.auth.config.WechatOpenConfig;
//import org.befun.core.utils.JsonHelper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Component
//public class CustomerThirdpartyMessageAddJourneyTaskConsumer implements ITaskConsumer<TaskCustomerThirdpartyAddJourneyDto> {
//
//    @Autowired
//    private DepartmentService departmentService;
//    @Autowired
//    private SendMangeJourneyHelper sendMangeJourneyHelper;
//    @Autowired
//    private ThirdPartyMessageYouzanRepository thirdPartyMessageYouzanRepository;
//    @Autowired
//    private ThirdPartyAuthService thirdPartyAuthService;
//    @Autowired
//    private AuthWechatOpenService authWechatOpenService;
//    @Autowired
//    private ThirdPartyCustomerRepository thirdPartyCustomerRepository;
//    @Autowired
//    private CustomerRepository customerRepository;
//    @Autowired
//    private WechatOpenSyncCustomerInfoTaskConsumer wechatOpenSyncCustomerInfoTaskConsumer;
//
//    @Override
//    public TaskType type() {
//        return TaskType.CUSTOMER_THIRDPARTY_MESSAGE_ADD_JOURNEY;
//    }
//
//    @Override
//    @Transactional
//    public ConsumerStatus consumer(CemTask entity, TaskCustomerThirdpartyAddJourneyDto param) {
//        Map<String, Object> urlParams = new HashMap<>();
//        Long customerId = getCustomerId(param, urlParams);
//        if (customerId == null) {
//            log.info("未找到客户");
//            return ConsumerStatus.CANCELED;
//        }
//        Long orgId = entity.getOrgId();
//        Long userId = entity.getUserId();
//        sendMangeJourneyHelper.sendByJourney(orgId, userId, param.getSceneId(), customerId, null, null, urlParams);
//        return ConsumerStatus.SUCCESS;
//    }
//
//    private Long getCustomerId(TaskCustomerThirdpartyAddJourneyDto param, Map<String, Object> urlParams) {
//        List<ThirdPartyAuth> wechatOpens = thirdPartyAuthService.getListByOrg(param.getOrgId(), ThirdPartyAuthType.WECHAT_OPEN, "cem");
//        List<WechatOpenConfig> wechatOpenConfigs = null;
//        if (CollectionUtils.isNotEmpty(wechatOpens)) {
//            wechatOpenConfigs = wechatOpens.stream().map(i -> authWechatOpenService.getConfig(i)).filter(WechatOpenConfig::isAuthorized).collect(Collectors.toList());
//        }
//        if (CollectionUtils.isEmpty(wechatOpenConfigs)) {
//            // 没有绑定微信公众号
//            return null;
//        }
//        String openId = null;
//        if ("youzan".equals(param.getThirdpartyMessageType()) && param.getThirdpartyMessageId() != null && param.getThirdpartyMessageId() > 0) {
//            ThirdPartyMessageYouzan youzan = thirdPartyMessageYouzanRepository.findById(param.getThirdpartyMessageId()).orElse(null);
//            if (youzan != null) {
//                openId = youzan.getWxOpenId();
//                String parsedFields = youzan.getParsedFields();
//                if (StringUtils.isEmpty(openId) && StringUtils.isNotEmpty(youzan.getKdtId()) && StringUtils.isNotEmpty(youzan.getTradeId())) {
//                    // 查找同一个订单的其他消息，看看是否有 openId
//                    youzan = thirdPartyMessageYouzanRepository.findFirstByKdtIdAndTradeIdAndWxOpenIdNotNull(youzan.getKdtId(), youzan.getTradeId()).orElse(null);
//                    if (youzan != null) {
//                        openId = youzan.getWxOpenId();
//                        parsedFields = youzan.getParsedFields();
//                    }
//                }
//                Map<String, Object> parsedFieldMap = JsonHelper.toMap(parsedFields);
//                if (parsedFieldMap != null) {
//                    List.of("orderNo", "payment", "title").forEach(k -> {
//                        Object s = parsedFieldMap.get(k);
//                        if (s != null) {
//                            urlParams.put("_" + k, s.toString());
//                        }
//                    });
//                }
//            }
//        }
//        return getOrCreateCustomerId(param, wechatOpenConfigs, openId);
//    }
//
//    private Long getOrCreateCustomerId(TaskCustomerThirdpartyAddJourneyDto param, List<WechatOpenConfig> wechatOpenConfigs, String openId) {
//        if (StringUtils.isEmpty(openId)) {
//            return null;
//        }
//        ThirdPartyCustomer thirdPartyCustomer = thirdPartyCustomerRepository.findFirstByOrgIdAndOpenId(param.getOrgId(), openId);
//        if (thirdPartyCustomer == null) {
//            // 同步这个客户
//            Customer customer = null;
//            for (WechatOpenConfig wechatOpenConfig : wechatOpenConfigs) {
//                customer = wechatOpenSyncCustomerInfoTaskConsumer.syncOpenId(param.getOrgId(), wechatOpenConfig.getConfigId(), wechatOpenConfig.getAppId(), openId);
//                if (customer != null) {
//                    break;
//                }
//            }
//            if (customer != null) {
//                return customer.getId();
//            }
//        } else {
//            // 找到这个客户
//            List<Customer> customers = customerRepository.findByOrgIdAndThirdPartyCustomerId(param.getOrgId(), thirdPartyCustomer.getId());
//            if (CollectionUtils.isNotEmpty(customers)) {
//                return customers.get(0).getId();
//            }
//        }
//        return null;
//    }
//}
