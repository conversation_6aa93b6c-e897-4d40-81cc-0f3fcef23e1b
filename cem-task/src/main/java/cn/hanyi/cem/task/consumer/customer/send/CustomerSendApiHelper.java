package cn.hanyi.cem.task.consumer.customer.send;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.send.SendApiInfo;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.customer.CustomerSendConsumer;
import cn.hanyi.cem.task.consumer.properties.SendGMPProperties;
import cn.hanyi.ctm.constant.PushStatus;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Push;
import cn.hanyi.ctm.repository.ConnectorRepository;
import cn.hanyi.ctm.repository.PushRepository;
import com.aliyun.datahub.client.model.RecordEntry;
import com.aliyun.datahub.client.model.RecordSchema;
import com.aliyun.datahub.client.model.TupleRecordData;
import com.aliyun.datahub.clientlibrary.config.ProducerConfig;
import com.aliyun.datahub.clientlibrary.producer.DatahubProducer;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

@Slf4j
@Component
public class CustomerSendApiHelper {

    @Autowired
    private ConnectorRepository connectorRepository;

    @Autowired
    private PushRepository pushRepository;

    @Autowired
    private SendGMPProperties sendGMPProperties;

    public boolean run(Long orgId,
                       CemTask entity,
                       Long taskProgressId,
                       CustomerSendFromType fromType,
                       Long fromId,
                       SendApiInfo detailDto,
                       Boolean isRemind,
                       CustomerSendConsumer.CustomerSendResult customerSendResult) {
        log.info("connector {} send api message task start ", detailDto.getConnectorId());
        try {
            Connector connector = connectorRepository.findById(detailDto.getConnectorId()).orElse(null);
            if (connector == null) {
                entity.setResponse(String.format("connector(%d) 不存在", detailDto.getConnectorId()));
                return customerSendResult.isSuccess();
            }
            PushStatus status = PushStatus.SUCCESS;
            String response = null;
            AtomicReference<String> data = new AtomicReference<>();
            try {
                if (sendGMPProperties != null) {
                    response = pushGMP(detailDto.getBody(), data::set, isRemind, fromId);
                } else {
                    data.set(detailDto.getBody());
                    response = httpPost(connector.getGateway(), data.get());
                }
                if (response != null && !response.isEmpty()) {
                    response = response.length() > 300 ? response.substring(0, 300) : response;
                }
            } catch (Exception ex) {
                log.warn("api: {} push error, caused by :{}", connector.getId(), ex.getMessage());
                status = PushStatus.FAILED;
                response = ex.getMessage();
                entity.setResponse(response);
            } finally {
                Push push = new Push();
                push.setOrgId(orgId);
                push.setName(connector.getName());
                push.setConnector(connector);
                push.setType(ConnectorType.API);
                push.setAddress(connector.getGateway());
                push.setContent(data.get());
                push.setResponse(response);
                push.setStatus(status);
                pushRepository.save(push);
                customerSendResult.setSuccess(status == PushStatus.SUCCESS);
                customerSendResult.setContent(data.get());
            }
        } catch (Exception ex) {
            log.error("api-send error, caused by:{}", ex.getMessage());
        }
        log.info("connector {} send api message task finished", detailDto.getConnectorId());
        return customerSendResult.isSuccess();
    }

    public String httpPost(String url, String body) throws IOException {
        return Request.Post(url)
                .connectTimeout(1000 * 30)
                .socketTimeout(1000 * 30)
                .bodyByteArray(body.getBytes(StandardCharsets.UTF_8), ContentType.APPLICATION_JSON)
                .execute().returnContent().asString();
    }


    public String pushGMP(String body, Consumer<String> consumer, Boolean isRemind, Long fromId) {
        SendGMPProperties.DatahubProperties datahub = sendGMPProperties.getDatahub();
        String projectName = datahub.getProject();
        String topicName = datahub.getTopic();
        log.info("datahub: {} {} {}", projectName, topicName, datahub.getEndpoint());

        ProducerConfig producerConfig = new ProducerConfig(datahub.getEndpoint(), datahub.getKey(), datahub.getSecret());
        DatahubProducer producer = new DatahubProducer(projectName, topicName, producerConfig);

        Map<String, Object> data = JsonHelper.toMap(body);
        // uuid 使用时间戳
        String uuid = fromId == null ? UUID.randomUUID().toString() : String.valueOf(fromId);
        data.put("id", uuid);
        data.put("isNewMessage", Boolean.TRUE.equals(isRemind) ? "0" : "1");
        String info = buildGMPDataInfo(data);
        consumer.accept(info);
        log.info("pushGMP: {}", info);
        RecordSchema schema = producer.getTopicSchema();
        List<RecordEntry> entries = new ArrayList<>();
        TupleRecordData recordData = new TupleRecordData(schema);
        recordData.setField("data_info", info);
        recordData.setField("data_time", DateHelper.formatDateTime(new Date()));
        recordData.setField("data_seq", uuid);
        RecordEntry entry = new RecordEntry();
        entry.setRecordData(recordData);
        entries.add(entry);

        String shardId = producer.send(entries);
        producer.flush(true);
        producer.close();
        return shardId;
    }

    private String buildGMPDataInfo(Map<String, Object> data) {

        String brand = (String) data.get("brand");
        String surveyUrl = (String) data.get("surveyUrl");

        if ("Bosch".equals(brand)) {
            brand = "1";
            surveyUrl += "?utm_source=SMS&utm_medium=NPS";
        } else if ("Siemens".equals(brand)) {
            brand = "2";
            surveyUrl += "?utm_source=SMS&utm_medium=NPS";
        } else if ("Gaggenau".equals(brand)) {
            brand = "3";
        } else {
            brand = "4";
        }

        GMPDto dto = new GMPDto();
        dto.getFirstName().setStringValue((String) data.get("firstName"));
        dto.getLastName().setStringValue((String) data.get("lastName"));
        dto.getSurveyLink().setStringValue(surveyUrl);

        GMPDataDto isNewMessage = dto.getIsNewMessage();
        isNewMessage.setDataType("Number");
        isNewMessage.setStringValue((String) data.get("isNewMessage"));

        dto.getPhone2().setStringValue((String) data.get("phone2"));
        dto.getSurvey().setStringValue((String) data.get("surveyName"));

        GMPDataDto id = dto.getId();
        id.setStringValue((String) data.get("id"));
        id.setDataType("Number");

        GMPDataDto brd = dto.getBrand();
        brd.setStringValue(brand);
        brd.setDataType("Number");

        dto.getPhone1().setStringValue((String) data.get("phone1"));

        GMPDataDto ris = dto.getRisnumber();
        ris.setStringValue((String) data.get("risnumber"));
        ris.setDataType("Number");
        return JsonHelper.toJson(dto);
    }

    @Setter
    @Getter
    public static class GMPDto {

        private GMPDataDto firstName = new GMPDataDto();
        private GMPDataDto lastName = new GMPDataDto();
        private GMPDataDto surveyLink = new GMPDataDto();
        private GMPDataDto isNewMessage = new GMPDataDto();
        private GMPDataDto phone2 = new GMPDataDto();
        private GMPDataDto survey = new GMPDataDto();
        private GMPDataDto id = new GMPDataDto();
        private GMPDataDto brand = new GMPDataDto();
        private GMPDataDto phone1 = new GMPDataDto();
        private GMPDataDto risnumber = new GMPDataDto();

    }

    @Setter
    @Getter
    public static class GMPDataDto {
        private List<Object> binaryListValues = new ArrayList<>();
        private String stringValue;
        private String dataType = "String";
        private List<Object> stringListValues = new ArrayList<>();

    }

}
