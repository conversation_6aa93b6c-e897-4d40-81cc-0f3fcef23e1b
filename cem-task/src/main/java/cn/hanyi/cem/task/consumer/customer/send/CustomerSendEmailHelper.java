package cn.hanyi.cem.task.consumer.customer.send;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.send.SendEmailInfo;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.customer.CustomerSendConsumer;
import cn.hanyi.cem.task.consumer.properties.EmailWrapProperties;
import cn.hanyi.ctm.constant.PushStatus;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.entity.Push;
import cn.hanyi.ctm.repository.PushRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.auth.AuthEmailSenderService;
import org.befun.auth.service.auth.config.EmailSenderConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.internet.MimeMessage;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class CustomerSendEmailHelper {

    @Autowired
    private AuthEmailSenderService authEmailSenderService;
    @Autowired
    private PushRepository pushRepository;
    @Autowired
    private EmailWrapProperties emailWrapProperties;

    public boolean run(Long orgId,
                       CemTask entity,
                       Long taskProgressId,
                       CustomerSendFromType fromType,
                       Long fromId,
                       SendEmailInfo detailDto,
                       CustomerSendConsumer.CustomerSendResult customerSendResult) {
        log.info("send email task start, email:{}", detailDto.getEmail());
        try {
            PushStatus status = PushStatus.FAILED;
            try {
                EmailSenderConfig config = authEmailSenderService.getConfig(orgId);
                if (config != null) {
                    JavaMailSender mailSender = authEmailSenderService.mailSender(config).orElse(null);
                    if (mailSender != null) {
                        MimeMessage mimeMessage = mailSender.createMimeMessage();
                        MimeMessageHelper message = new MimeMessageHelper(mimeMessage, "utf-8");
                        message.setFrom(config.getFrom(), detailDto.getSender());
                        message.setTo(detailDto.getEmail());
                        message.setSubject(detailDto.getTitle());
                        message.setText(wrapContent(detailDto.getContent(), emailWrapProperties), true);
                        mailSender.send(mimeMessage);
                        status = PushStatus.SUCCESS;
                    }
                }
            } catch (Throwable e) {
                entity.setResponse(e.getMessage());
                log.error("send email task exception, email:{}", detailDto.getEmail(), e);
            }
            Push push = new Push();
            push.setOrgId(orgId);
            push.setType(ConnectorType.EMAIL);
            push.setContent(detailDto.getContent());
            push.setResponse(null);
            push.setStatus(status);
            pushRepository.save(push);
            customerSendResult.setSuccess(status == PushStatus.SUCCESS);
            customerSendResult.setContent(detailDto.getContent());
        } catch (Exception ex) {
            log.error("send email task exception, email:{}", detailDto.getEmail(), ex);
        }
        log.info("send email task end, email:{}", detailDto.getEmail());
        return customerSendResult.isSuccess();
    }

    public boolean run(Long orgId,
                       CemTask entity,
                       Long taskProgressId,
                       CustomerSendFromType fromType,
                       Long fromId,
                       SendEmailInfo detailDto,
                       CustomerSendConsumer.CustomerSendResult customerSendResult, CustomerSendConsumer.SendDetailResult sendDetailResult) {
        log.info("send email task start, email:{}", detailDto.getEmail());
        try {
            PushStatus status = PushStatus.FAILED;
            try {
                EmailSenderConfig config = authEmailSenderService.getConfig(orgId);
                if (config != null) {
                    JavaMailSender mailSender = authEmailSenderService.mailSender(config).orElse(null);
                    if (mailSender != null) {
                        MimeMessage mimeMessage = mailSender.createMimeMessage();
                        MimeMessageHelper message = new MimeMessageHelper(mimeMessage, "utf-8");
                        message.setFrom(config.getFrom(), detailDto.getSender());
                        message.setTo(detailDto.getEmail());
                        message.setSubject(detailDto.getTitle());
                        message.setText(wrapContent(detailDto.getContent(), emailWrapProperties), true);
                        mailSender.send(mimeMessage);
                        status = PushStatus.SUCCESS;
                    }
                }
            } catch (Throwable e) {
                entity.setResponse(e.getMessage());
                log.error("send email task exception, email:{}", detailDto.getEmail(), e);
            }
            Push push = new Push();
            push.setOrgId(orgId);
            push.setType(ConnectorType.EMAIL);
            push.setContent(detailDto.getContent());
            push.setResponse(null);
            push.setStatus(status);
            pushRepository.save(push);
            sendDetailResult.setAccount(detailDto.getEmail());
            customerSendResult.setSuccess(status == PushStatus.SUCCESS);
            customerSendResult.setContent(detailDto.getContent());
        } catch (Exception ex) {
            log.error("send email task exception, email:{}", detailDto.getEmail(), ex);
        }
        log.info("send email task end, email:{}", detailDto.getEmail());
        return customerSendResult.isSuccess();
    }

    private static String wrapContent(String content, EmailWrapProperties properties) {
        if (!properties.isEnable() || StringUtils.isEmpty(content)) {
            return content;
        }
        String wrapContent = content;
        if (content.contains(properties.getLinkWrapFlag())) {
            Pattern pattern = Pattern.compile(properties.getLinkPattern());
            Matcher matcher = pattern.matcher(content);
            StringBuilder sb = new StringBuilder();
            while (matcher.find()) {
                String link = String.format(properties.getLinkWrap(), matcher.group());
                matcher.appendReplacement(sb, link);
            }
            matcher.appendTail(sb);
            wrapContent = sb.length() == 0 ? content : sb.toString();
        }
        if (wrapContent.startsWith(properties.getContentWrapFlag())) {
            return wrapContent;
        } else {
            return String.format(properties.getContentWrap(), wrapContent);
        }
    }

}
