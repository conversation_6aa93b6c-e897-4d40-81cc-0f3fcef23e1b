package cn.hanyi.cem.task.consumer.customer.send;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.send.SendSmsInfo;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.customer.CustomerSendConsumer;
import cn.hanyi.cem.task.consumer.properties.SmsCallbackProperties;
import cn.hanyi.ctm.constant.PushStatus;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.entity.Push;
import cn.hanyi.ctm.repository.PushRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.OrganizationSmsRecordService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.dto.MessageSendResponseInfo;
import org.befun.extension.dto.SmsNotifyInfo;
import org.befun.extension.service.SmsService;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class CustomerSendSmsHelper {

    @Autowired
    private SmsService smsService;
    @Autowired
    private PushRepository pushRepository;
    @Autowired
    private ISmsAccountService smsAccountService;
    @Autowired
    private OrganizationSmsRecordService organizationSmsRecordService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private SmsCallbackProperties smsCallbackProperties;

    public void smsCost(Long orgId, Long userId, boolean success,
                        boolean smsPostPaid, Long taskProgressId,
                        CustomerSendFromType fromType, Long fromId,
                        SendSmsInfo smsInfo) {
        if (smsInfo != null) {
            if (smsPostPaid) {
                if (success) {
                    int cost = cost(smsInfo);
                    int balance = smsAccountService.consumer(orgId, cost);
                    smsAccountService.addSmsRecordBySource(orgId, userId, fromType.name(), smsInfo.getTemplateContent(), cost, balance);
                }
            } else {
                smsCost(orgId, userId, success, taskProgressId, fromId, smsInfo);
            }
        }
    }

    private void smsCost(Long orgId, Long userId, boolean success, Long taskProgressId, Long jobId, SendSmsInfo param) {
        if (taskProgressId == null) {
            return;
        }
        if (success) {
            int cost = cost(param);
            smsAccountService.successSmsTask(orgId, userId, taskProgressId, jobId, cost);
        } else {
            smsAccountService.failureSmsTask(orgId, userId, taskProgressId, jobId);
        }
    }

    public int cost(SendSmsInfo detailDto) {
        String fullContent = StringUtils.isNotEmpty(detailDto.getRealSign()) ? detailDto.getRealSign() + detailDto.getContent() : detailDto.getContent();
        return smsAccountService.calcNumberByText(fullContent);
    }

    public boolean run(Long orgId,
                       CemTask entity,
                       boolean smsPostPaid,
                       Long taskProgressId,
                       CustomerSendFromType fromType,
                       Long fromId,
                       SendSmsInfo detailDto,
                       CustomerSendConsumer.CustomerSendResult customerSendResult) {
        log.info("sendSms task start, mobile：{}，content：{}", detailDto.getMobile(), detailDto.getContent());
        try {
            int cost = cost(detailDto);
            boolean checkSmsCost = checkSmsCost(orgId, smsPostPaid, taskProgressId, detailDto, cost);
            if (!checkSmsCost) {
                return customerSendResult.isSuccess();
            }
            Boolean ignoreSend = stringRedisTemplate.opsForSet().isMember("sms.send-ignore", orgId.toString());
            MessageSendResponseInfo responseInfo;
            if (ignoreSend != null && ignoreSend) {
                // orgId 配置了忽略发送短信的模式，直接构造发送成功
                responseInfo = new MessageSendResponseInfo(true, 0, detailDto.getContent(), "mock send success");
            } else {
                SmsNotifyInfo info = new SmsNotifyInfo();
                info.setMobile(detailDto.getMobile());
                info.setContent(detailDto.getContent());
                info.setTemplateId(detailDto.getTemplateId());
                info.setTemplateName(detailDto.getTemplateName());
                info.setSignature(detailDto.getSignId());
                info.setRealSignature(detailDto.getRealSign());
                responseInfo = smsService.sendMessage2(info);
            }
            Map<String, Object> result = new HashMap<>();
            result.put("SmsMessageStatus", responseInfo.isSuccess());
            if (responseInfo.isSuccess()) {
                // 缓存短信的第三方消息id
                cacheSmsMessageId(fromType, fromId, responseInfo);
                log.info("sendSms task finished, mobile：{}，content：{}", detailDto.getMobile(), responseInfo.getContent());
            } else {
                result.put("error", responseInfo.getResponse());
                entity.setResponse(responseInfo.getResponse());
            }
            Push push = new Push();
            push.setOrgId(orgId);
            push.setType(ConnectorType.SMS);
            push.setContent(detailDto.getContent());
            push.setResponse(JsonHelper.toJson(result));
            PushStatus status = responseInfo.isSuccess() ? PushStatus.SUCCESS : PushStatus.FAILED;
            push.setStatus(status);
            pushRepository.save(push);
            customerSendResult.setSuccess(responseInfo.isSuccess());
            customerSendResult.setContent(detailDto.getContent());
        } catch (Exception e) {
            log.error("sms send error", e);
        }
        return customerSendResult.isSuccess();
    }

    public boolean run(Long orgId,
                       CemTask entity,
                       boolean smsPostPaid,
                       Long taskProgressId,
                       CustomerSendFromType fromType,
                       Long fromId,
                       SendSmsInfo detailDto,
                       CustomerSendConsumer.CustomerSendResult customerSendResult, CustomerSendConsumer.SendDetailResult sendDetailResult) {
        log.info("sendSms task start, mobile：{}，content：{}", detailDto.getMobile(), detailDto.getContent());
        try {
            int cost = cost(detailDto);
            boolean checkSmsCost = checkSmsCost(orgId, smsPostPaid, taskProgressId, detailDto, cost);
            if (!checkSmsCost) {
                return customerSendResult.isSuccess();
            }
            Boolean ignoreSend = stringRedisTemplate.opsForSet().isMember("sms.send-ignore", orgId.toString());
            MessageSendResponseInfo responseInfo;
            if (ignoreSend != null && ignoreSend) {
                // orgId 配置了忽略发送短信的模式，直接构造发送成功
                responseInfo = new MessageSendResponseInfo(true, 0, detailDto.getContent(), "mock send success");
            } else {
                SmsNotifyInfo info = new SmsNotifyInfo();
                info.setMobile(detailDto.getMobile());
                info.setContent(detailDto.getContent());
                info.setTemplateId(detailDto.getTemplateId());
                info.setTemplateName(detailDto.getTemplateName());
                info.setSignature(detailDto.getSignId());
                info.setRealSignature(detailDto.getRealSign());
                responseInfo = smsService.sendMessage2(info);
            }
            Map<String, Object> result = new HashMap<>();
            result.put("SmsMessageStatus", responseInfo.isSuccess());
            if (responseInfo.isSuccess()) {
                // 缓存短信的第三方消息id
                cacheSmsMessageId(fromType, fromId, responseInfo);
                log.info("sendSms task finished, mobile：{}，content：{}", detailDto.getMobile(), responseInfo.getContent());
            } else {
                result.put("error", responseInfo.getResponse());
                entity.setResponse(responseInfo.getResponse());
            }
            Push push = new Push();
            push.setOrgId(orgId);
            push.setType(ConnectorType.SMS);
            push.setContent(detailDto.getContent());
            push.setResponse(JsonHelper.toJson(result));
            PushStatus status = responseInfo.isSuccess() ? PushStatus.SUCCESS : PushStatus.FAILED;
            push.setStatus(status);
            pushRepository.save(push);
            sendDetailResult.setAccount(detailDto.getMobile());
            customerSendResult.setSuccess(responseInfo.isSuccess());
            customerSendResult.setContent(detailDto.getContent());
        } catch (Exception e) {
            log.error("sms send error", e);
        }
        return customerSendResult.isSuccess();
    }


    private boolean checkSmsCost(Long orgId, boolean smsPostPaid, Long taskProgressId, SendSmsInfo detailDto, int cost) {
        if (!smsPostPaid && taskProgressId != null) {
            if (!smsAccountService.hasBalance(orgId, taskProgressId, cost)) {
                // 余额不足
                log.info("sendSms task cancel, no balance, taskId:{}, mobile：{}，content：{}", taskProgressId, detailDto.getMobile(), detailDto.getContent());
                return false;
            }
        } else {
            if (!smsAccountService.hasBalance(orgId, cost)) {
                // 余额不足
                log.info("sendSms task cancel, no balance, mobile：{}，content：{}", detailDto.getMobile(), detailDto.getContent());
                return false;
            }
        }
        return true;
    }

    private void cacheSmsMessageId(CustomerSendFromType fromType, Long fromId, MessageSendResponseInfo responseInfo) {
        if (StringUtils.isNotEmpty(responseInfo.getThirdpartyMessageType())
                && StringUtils.isNotEmpty(responseInfo.getThirdpartyMessageId())) {
            // key = sms-status:#{thirdpartyMessageType}:#{thirdpartyMessageId}  value = #{fromType}:#{fromId}
            // key = sms-status:#{fromType}:#{fromId}  value = #{thirdpartyMessageType}:#{thirdpartyMessageId}
            // 只要这个key还在，就说明还没有获取到最终的状态
            // 没有了这个key，就直接忽略
            String key1 = key1(responseInfo.getThirdpartyMessageType(), responseInfo.getThirdpartyMessageId());
            String value1 = value1(fromType, fromId);
            String key2 = key2(fromType, fromId);
            String value2 = value2(responseInfo.getThirdpartyMessageType(), responseInfo.getThirdpartyMessageId());
            stringRedisTemplate.opsForValue().set(key1, value1, Duration.ofHours(smsCallbackProperties.getValidHours()));
            stringRedisTemplate.opsForValue().set(key2, value2, Duration.ofHours(smsCallbackProperties.getValidHours()));
        }
    }

    public static String key1(String thirdpartyMessageType, String thirdPartyMessageId) {
        return String.format("sms-status:%s:%s", thirdpartyMessageType, thirdPartyMessageId);
    }

    public static String value1(CustomerSendFromType fromType, Long fromId) {
        return String.format("%s:%d", fromType.name(), fromId);
    }

    public static String key2(CustomerSendFromType fromType, Long fromId) {
        return String.format("sms-status:%s:%d", fromType.name(), fromId);
    }

    public static String value2(String thirdpartyMessageType, String thirdPartyMessageId) {
        return String.format("%s:%s", thirdpartyMessageType, thirdPartyMessageId);
    }
}
