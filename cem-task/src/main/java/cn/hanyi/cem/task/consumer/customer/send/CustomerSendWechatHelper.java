package cn.hanyi.cem.task.consumer.customer.send;

import cn.hanyi.cem.core.constant.CustomerSendFromType;
import cn.hanyi.cem.core.dto.task.send.SendWechatInfo;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.customer.CustomerSendConsumer;
import cn.hanyi.ctm.constant.PushStatus;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.entity.Push;
import cn.hanyi.ctm.repository.PushRepository;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.error.WxOpenErrorMsgEnum;
import me.chanjar.weixin.mp.api.WxMpTemplateMsgService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import me.chanjar.weixin.open.api.WxOpenMpService;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.WeChatOpenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class CustomerSendWechatHelper {

    @Autowired
    private WeChatOpenService weChatOpenService;
    @Autowired
    private AuthWechatOpenService authWechatOpenService;
    @Autowired
    private PushRepository pushRepository;

    public boolean run(Long orgId,
                       CemTask entity,
                       Long taskProgressId,
                       CustomerSendFromType fromType,
                       Long fromId,
                       SendWechatInfo detailDto,
                       CustomerSendConsumer.CustomerSendResult customerSendResult) {
        log.info("connector send WeChat template task start, appId:{}", detailDto.getAppId());
        try {
            String appId = detailDto.getAppId();
            String openId = detailDto.getOpenId();
            String message = detailDto.getMessage();
            Map<String, Object> result = new HashMap<>();
            PushStatus status = PushStatus.SUCCESS;
            try {
                WxOpenMpService wxOpenMpService = weChatOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId);
                if (fromType == CustomerSendFromType.EVENT_NOTIFY_CUSTOMER) {
                    log.info("send text message to user {}", openId);
                    boolean success = wxOpenMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().content(message).toUser(openId).build());
                    result.put("WechatOpenTextMessage", success);
                    if (!success) {
                        status = PushStatus.FAILED;
                    }
                } else {
                    WxMpTemplateMessage templateMessage = JsonHelper.toObject(message, WxMpTemplateMessage.class);
                    WxMpTemplateMsgService templateMsgService = wxOpenMpService.getTemplateMsgService();
                    log.info("send template message to user {}", openId);
                    String msgId = templateMsgService.sendTemplateMsg(templateMessage);
                    result.put("WechatOpenTemplateMessageId", msgId);
                }
            } catch (WxErrorException ex) {
                status = PushStatus.FAILED;
                result.put("error", ex.getMessage());
                entity.setResponse(ex.getMessage());
                log.error("sendMessage got exception: {}", ex.getMessage());
                if (ex.getError() != null && ex.getError().getErrorCode() == WxOpenErrorMsgEnum.CODE_61007.getCode()) {
                    authWechatOpenService.eventUnauthorized("cem", detailDto.getAppId());
                }
            } catch (Exception ex) {
                status = PushStatus.FAILED;
                result.put("error", ex.getMessage());
                entity.setResponse(ex.getMessage());
                log.error("sendMessage got Exception: {}", ex.getMessage());
            }
            Push push = new Push();
            push.setOrgId(orgId);
            push.setType(ConnectorType.PLATFORM);
            push.setContent(message);
            push.setResponse(JsonHelper.toJson(result));
            push.setStatus(status);
            pushRepository.save(push);
            customerSendResult.setSuccess(status == PushStatus.SUCCESS);
            customerSendResult.setContent(message);
        } catch (Exception ex) {
            log.error("send Message got exception: {}", ex.getMessage());
        }
        log.info("send WeChatOpen task finished, appId:{}", detailDto.getAppId());
        return customerSendResult.isSuccess();
    }

    public boolean run(Long orgId,
                       CemTask entity,
                       Long taskProgressId,
                       CustomerSendFromType fromType,
                       Long fromId,
                       SendWechatInfo detailDto,
                       CustomerSendConsumer.CustomerSendResult customerSendResult, CustomerSendConsumer.SendDetailResult sendDetailResult) {
        log.info("connector send WeChat template task start, appId:{}", detailDto.getAppId());
        try {
            String appId = detailDto.getAppId();
            String openId = detailDto.getOpenId();
            String message = detailDto.getMessage();
            Map<String, Object> result = new HashMap<>();
            PushStatus status = PushStatus.SUCCESS;
            try {
                WxOpenMpService wxOpenMpService = weChatOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId);
                if (fromType == CustomerSendFromType.EVENT_NOTIFY_CUSTOMER) {
                    log.info("send text message to user {}", openId);
                    boolean success = wxOpenMpService.getKefuService().sendKefuMessage(WxMpKefuMessage.TEXT().content(message).toUser(openId).build());
                    result.put("WechatOpenTextMessage", success);
                    if (!success) {
                        status = PushStatus.FAILED;
                    }
                } else {
                    WxMpTemplateMessage templateMessage = JsonHelper.toObject(message, WxMpTemplateMessage.class);
                    WxMpTemplateMsgService templateMsgService = wxOpenMpService.getTemplateMsgService();
                    log.info("send template message to user {}", openId);
                    String msgId = templateMsgService.sendTemplateMsg(templateMessage);
                    result.put("WechatOpenTemplateMessageId", msgId);
                }
            } catch (WxErrorException ex) {
                status = PushStatus.FAILED;
                result.put("error", ex.getMessage());
                entity.setResponse(ex.getMessage());
                log.error("sendMessage got exception: {}", ex.getMessage());
                if (ex.getError() != null && ex.getError().getErrorCode() == WxOpenErrorMsgEnum.CODE_61007.getCode()) {
                    authWechatOpenService.eventUnauthorized("cem", detailDto.getAppId());
                }
            } catch (Exception ex) {
                status = PushStatus.FAILED;
                result.put("error", ex.getMessage());
                entity.setResponse(ex.getMessage());
                log.error("sendMessage got Exception: {}", ex.getMessage());
            }
            Push push = new Push();
            push.setOrgId(orgId);
            push.setType(ConnectorType.PLATFORM);
            push.setContent(message);
            push.setResponse(JsonHelper.toJson(result));
            push.setStatus(status);
            pushRepository.save(push);
            sendDetailResult.setAccount(openId);
            customerSendResult.setSuccess(status == PushStatus.SUCCESS);
            customerSendResult.setContent(message);
        } catch (Exception ex) {
            log.error("send Message got exception: {}", ex.getMessage());
        }
        log.info("send WeChatOpen task finished, appId:{}", detailDto.getAppId());
        return customerSendResult.isSuccess();
    }

}
