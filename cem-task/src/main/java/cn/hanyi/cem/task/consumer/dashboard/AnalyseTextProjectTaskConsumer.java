package cn.hanyi.cem.task.consumer.dashboard;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskProcessTextProjectDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.UserService;
import org.befun.bi.constant.TextProjectStatus;
import org.befun.bi.entity.TextProject;
import org.befun.bi.entity.TextProjectDto;
import org.befun.bi.repository.TextProjectRepository;
import org.befun.bi.service.TextService;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.MapperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class AnalyseTextProjectTaskConsumer implements ITaskConsumer<TaskProcessTextProjectDto> {

    @Autowired
    private TextProjectRepository textProjectRepository;

    @Autowired
    private TextService textService;

    @Autowired
    private MapperService mapperService;

    @Autowired
    private UserService userService;

    @Override
    public TaskType type() {
        return TaskType.BI_ANALYSE_TEXT_PROJECT;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskProcessTextProjectDto dto) {
        log.info("analyse text project: {}", dto.getTextProjectId());
        Optional<TextProject> projectOptional = textProjectRepository.findById(dto.getTextProjectId());
        if (projectOptional.isEmpty() || projectOptional.get().getStatus() == TextProjectStatus.STOPPED) {
            log.info("文本分析任务取消：{}", dto.getTextProjectId());
            return ConsumerStatus.CANCELED;
        }
        TextProjectDto projectDto = mapperService.map(projectOptional.get(), TextProjectDto.class);
        try {
            TenantContext.setCurrentTenant(dto.getOrgId());
            TenantContext.setCurrentUserId(dto.getUserId());
            textService.runTask(projectDto, false, dto.getIsRestart());
        } catch (Exception ex) {
            log.info("文本分析任务失败: {}", ex.getMessage());
            ex.printStackTrace();
        } finally {
            TenantContext.clear();
        }
        return ConsumerStatus.SUCCESS;
    }
}

