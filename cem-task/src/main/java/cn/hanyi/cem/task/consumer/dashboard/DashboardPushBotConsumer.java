package cn.hanyi.cem.task.consumer.dashboard;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskDashboardPushDto;
import cn.hanyi.cem.core.dto.task.TaskDashboardPushDto.PushChannel;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.cem.task.consumer.notify.NotifyBaseHelper;
import cn.hanyi.cem.task.consumer.notify.bot.AbsWarningBot;
import cn.hanyi.common.file.storage.FileStorageService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.befun.bi.constant.DashboardPushContentType;
import org.befun.bi.service.DashboardLinkService;
import org.befun.bi.service.ScreenShotService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DashboardPushBotConsumer extends NotifyBaseHelper implements
        ITaskConsumer<TaskDashboardPushDto> {

    @Autowired
    private List<AbsWarningBot> bots;

    @Autowired
    private DashboardLinkService dashboardLinkService;

    @Autowired
    private ScreenShotService screenShotService;

    @Autowired
    private FileStorageService fileStorageService;

    @Override
    public TaskType type() {
        return TaskType.BI_DASHBOARD_PUSH_BOT;
    }

    private Map<String, AbsWarningBot> botsMap = new HashMap<>();

    @PostConstruct
    private void setup() {
        botsMap = bots.stream()
                .collect(Collectors.toMap(c -> c.getType().name(), Function.identity()));
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskDashboardPushDto dto) {
        String url;
        byte[] imageBytes = null;
        // 是否需要发送图片，提前判断，避免多渠道多次生成
        if (dto.getChannels().stream().anyMatch(c -> DashboardPushContentType.LINK_IMAGE.name().equals(c.getPushContentType()))) {
            if (dto.getBotScreenShotUrl() != null) {
                imageBytes = fileStorageService.download(dto.getBotScreenShotUrl()).bytes();
            } else {
                url = dashboardLinkService.buildScreenShotLinkUrl(null, dto.getOrgId(), dto.getDashboardId());
                imageBytes = screenShotService.screenShotAsBytes(url);
            }
        }
        dto.getParams().put("pushTime",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        for (PushChannel c : dto.getChannels()) {
            AbsWarningBot bot = botsMap.get(c.getConnectorType());
            try {
                var body = bot.buildBody(dto.getTemplate(), dto.getParams());
                switch (c.getPushContentType()) {
                    case "LINK":
                        bot.send(JsonHelper.toJson(body), c.getGateWay());
                        break;
                    case "LINK_IMAGE":
                        bot.send(JsonHelper.toJson(body), c.getGateWay());
                        bot.sendImage(imageBytes, c.getGateWay());
                        break;
                    default:
                        log.info("暂不支持的推送类型，{}", c.getPushContentType());
                        break;
                }
            } catch (Exception ex) {
                log.warn("bot: {} 推送失败: {}", bot.getType().name(), ex.getMessage());
            }
        }
        return ConsumerStatus.SUCCESS;
    }

}
