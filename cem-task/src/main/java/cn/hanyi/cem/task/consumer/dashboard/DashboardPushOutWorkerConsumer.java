package cn.hanyi.cem.task.consumer.dashboard;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskDashboardPushDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.cem.task.consumer.notify.NotifyBaseHelper;
import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.service.SystemNotificationService;
import org.befun.bi.service.DashboardLinkService;
import org.befun.bi.service.ScreenShotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DashboardPushOutWorkerConsumer extends NotifyBaseHelper implements ITaskConsumer<TaskDashboardPushDto> {
    private static final String EMAIL_IMAGE_TAG = "<tr><td style=\"height: 42px;\"></td><td style=\"width: 650px;\">"
            + "<img width=\"630\" height=\"100%\"src=\"${screenShotUrl}\" alt=\"\"></td></tr><tr><td style=\"height: 20px;\"></td>\n"
            + "</tr>";

    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private WorkerProperties workerProperties;

    @Autowired
    private SystemNotificationService systemNotificationService;

    @Autowired
    private DashboardLinkService dashboardLinkService;

    @Autowired
    private FileStorageService storageService;

    @Autowired
    private ScreenShotService screenShotService;

    @Override
    public TaskType type() {
        return TaskType.BI_DASHBOARD_PUSH;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskDashboardPushDto pushDto) {
        var params = pushDto.getParams();
        var users = getNotifyUsers(pushDto.getOrgId(), null, pushDto.getRoleIds(), pushDto.getUserIds());
        var types = pushDto.getChannels().stream().map(c->NotificationType.valueOf(c.getConnectorType())).toArray(NotificationType[]::new);
        params.put("pushTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATE_TIME_PATTERN)));
        users.forEach(u -> {
            if (pushDto.getChannels().stream().anyMatch(c -> ConnectorType.EMAIL.name().equals(c.getConnectorType())
                    && "LINK_IMAGE".equals(c.getPushContentType()))) {
                String fileUrl = pushDto.getUserScreenShotUrlMap().get(u.getId());
                if (fileUrl == null) {
                    String url = dashboardLinkService.buildScreenShotLinkUrl(u.getId(), pushDto.getOrgId(), pushDto.getDashboardId());
                    byte[] imageBytes = screenShotService.screenShotAsBytes(url);
                    log.info("generate screenshot image size:,{}",imageBytes.length);
                    FileInfo fileInfo = storageService.of(imageBytes).setOriginalFilename(UUID.randomUUID() + ".png").upload();
                    fileUrl = fileInfo.getUrl();
                }
                params.put("screenShotUrl", fileUrl);
                params.put("emailImageTag", EMAIL_IMAGE_TAG.replace("${screenShotUrl}", fileUrl));
            }
            log.info("type: {} outworker notify: {} - {}:{}", type().name(), pushDto.getTemplate(), u.getId(), u.getTruename());
            params.put("targetTruename", u.getTruename() == null ? "" : u.getTruename());

            if (workerProperties.getNotify().getEnabled() && types.length > 0) {
                systemNotificationService.notifyToUser(
                        "cem",
                        u.getId(),
                        types,
                        pushDto.getTemplate(),
                        params,
                        true);
            }
        });
        return ConsumerStatus.SUCCESS;
    }
}
