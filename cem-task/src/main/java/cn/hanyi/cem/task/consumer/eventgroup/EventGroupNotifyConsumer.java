package cn.hanyi.cem.task.consumer.eventgroup;

import cn.hanyi.cem.core.dto.task.TaskEventGroupNotifyDto;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.entity.EventGroup;
import cn.hanyi.ctm.repository.EventGroupRepository;
import cn.hanyi.ctm.service.EventQueryService;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.entity.User;
import org.befun.auth.service.UserService;
import org.befun.core.dto.CountDto;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public abstract class EventGroupNotifyConsumer implements ITaskConsumer<TaskEventGroupNotifyDto> {
    @Autowired
    private UserService userService;
    @Autowired
    private EventQueryService eventQueryService;
    @Autowired
    protected EventGroupRepository eventGroupRepository;
    protected static final String TEMPLATE_NAME = "event-group-notify";

    protected Map<String, Object> buildParams(EventGroup eventGroup, Long userId, String url, boolean withDepartment) {
        List<Long> departmentIds = null;
        String truename = null;
        if (userId != null) {
            User user = userService.get(userId);
            if (user != null) {
                departmentIds = user.parseDepartmentIds2();
                truename = user.getTruename();
            }
        }
        if (departmentIds == null) {
            departmentIds = new ArrayList<>();
        }
        CountDto countDto = eventQueryService.countByGroupId(eventGroup.getOrgId(), userId, departmentIds, eventGroup.getId(), i -> withDepartment);
        AtomicLong wait = new AtomicLong(0);
        AtomicLong applying = new AtomicLong(0);
        AtomicLong success = new AtomicLong(0);
        countDto.getItems().forEach(i -> {
            if (EventStatusType.WAIT.name().equals(i.getName())) {
                wait.set(Optional.ofNullable(i.getCount()).orElse(0L));
            } else if (EventStatusType.APPLYING.name().equals(i.getName())) {
                applying.set(Optional.ofNullable(i.getCount()).orElse(0L));
            } else if (EventStatusType.SUCCESS.name().equals(i.getName())) {
                success.set(Optional.ofNullable(i.getCount()).orElse(0L));
            }
        });
        Map<String, Object> params = new HashMap<>();
        params.put("targetTruename", truename);
        params.put("groupName", eventGroup.getTitle());
        params.put("countAll", countDto.getTotal());
        params.put("countWait", wait.intValue());
        params.put("countApplying", applying.intValue());
        params.put("countSuccess", success.intValue());
        params.put("url", url);
        params.put("pushTime", DateHelper.formatDateTime(LocalDateTime.now()));
        params.put("title","Hi，您收到体验家定时推送的报表数据");
        return params;
    }

    protected Map<String, Object> buildTargetTruename(Long userId, Map<String, Object> params) {
        String truename = null;
        if (userId != null) {
            User user = userService.get(userId);
            if (user != null) {
                truename = user.getTruename();
            }
        }
        params.put("targetTruename", truename);
        return params;
    }

}
