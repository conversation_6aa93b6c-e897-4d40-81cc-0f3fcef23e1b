package cn.hanyi.cem.task.consumer.eventgroup;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskEventGroupNotifyDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.notify.bot.AbsWarningBot;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.group.EventGroupNotifyRobotDto;
import cn.hanyi.ctm.dto.group.EventGroupRobotChannelDto;
import cn.hanyi.ctm.entity.EventGroup;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class EventGroupNotifyRobotConsumer extends EventGroupNotifyConsumer {

    @Autowired(required = false)
    private List<AbsWarningBot> bots = new ArrayList<>();
    private final Map<ConnectorType, AbsWarningBot> botMap = new HashMap<>();

    @PostConstruct
    public void init() {
        bots.forEach(i -> botMap.put(i.getType(), i));
    }

    @Override
    public TaskType type() {
        return TaskType.EVENT_GROUP_NOTIFY_ROBOT;
    }

    @Override
    @Transactional(readOnly = true)
    public ConsumerStatus consumer(CemTask entity, TaskEventGroupNotifyDto param) {
        notifyRobots(param);
        return ConsumerStatus.SUCCESS;
    }

    public void notifyRobots(TaskEventGroupNotifyDto dto) {
        EventGroup eventGroup = eventGroupRepository.findById(dto.getEventGroupId()).orElse(null);
        if (eventGroup == null) {
            return;
        }
        EventGroupNotifyRobotDto content = eventGroup.getNotifyRobotContent();
        if (content == null) {
            return;
        }
        Map<String, Object> params = buildParams(eventGroup, null, dto.getUrl(), false);
        for (EventGroupRobotChannelDto channel : content.getNotifyRobotChannel()) {
            if (channel.isEnabled()) {
                AbsWarningBot bot = botMap.get(channel.getType());
                bot.send(JsonHelper.toJson(bot.buildBody(TEMPLATE_NAME, params)), channel.getUrl());
            }
        }
    }

}
