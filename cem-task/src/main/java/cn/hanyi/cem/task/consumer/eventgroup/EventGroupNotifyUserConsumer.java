package cn.hanyi.cem.task.consumer.eventgroup;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskEventGroupNotifyDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.ctm.dto.group.EventGroupNotifyUserDto;
import cn.hanyi.ctm.entity.EventGroup;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.service.SystemNotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Slf4j
@Component
public class EventGroupNotifyUserConsumer extends EventGroupNotifyConsumer {

    @Autowired
    private SystemNotificationService systemNotificationService;

    @Override
    public TaskType type() {
        return TaskType.EVENT_GROUP_NOTIFY_USER;
    }

    @Override
    @Transactional(readOnly = true)
    public ConsumerStatus consumer(CemTask entity, TaskEventGroupNotifyDto param) {
        notifyUsers(param);
        return ConsumerStatus.SUCCESS;
    }

    private void notifyUsers(TaskEventGroupNotifyDto dto) {
        EventGroup eventGroup = eventGroupRepository.findById(dto.getEventGroupId()).orElse(null);
        if (eventGroup == null) {
            return;
        }
        EventGroupNotifyUserDto content = eventGroup.getNotifyUserContent();
        if (content == null) {
            return;
        }
        Map<String, Object> params = null;
        for (Long userId : dto.getUserIds()) {
            try {
                if (eventGroup.isWithDepartment()) {
                    // 开启了部门过滤，每次都要重新查询 统计数据
                    params = buildParams(eventGroup, userId, dto.getUrl(), true);
                } else {
                    if (params == null) {
                        params = buildParams(eventGroup, null, dto.getUrl(), false);
                    }
                    params = buildTargetTruename(userId, params);
                }
                systemNotificationService.notifyToUser("cem", userId, content.getNotifyUserChannel().toArray(new NotificationType[0]), TEMPLATE_NAME, params, true);
            } catch (Throwable e) {
                log.warn("event group {} notify {} error", eventGroup.getId(), userId, e);
            }
        }
    }

}
