package cn.hanyi.cem.task.consumer.notify;

import cn.hanyi.cem.core.dto.task.TaskNotifyBaseDto;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.UserService;
import org.befun.core.service.ResourceCorporationService;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

import static org.befun.core.constant.ResourceShareFlag.SHARE_FOUND_IGNORE;

@Component
@Slf4j
public class NotifyBaseHelper {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private UserService userService;

    @Autowired
    private ResourceCorporationService resourceCorporationService;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;

    @Autowired
    private EventConsumerUtils eventConsumerUtils;


    /**
     * 查询事件需要通知的用户
     */
    public List<SimpleUser> getNotifyUsers(Long orgId, Long departmentId, Set<Long> roleIds, Set<Long> externalUserIds) {
        return eventConsumerUtils.getNotifyUsers(orgId, departmentId, roleIds, externalUserIds);
    }
    public void addResourcePermission(TaskNotifyBaseDto param, Long userId) {
        if (param.getResourceType() != null && param.getSourceId() != null) {
            grantEventPermission(param.getOrgId(), param.getSourceId(), userId, param.getResourceType());
        }
    }


    /**
     * 添加资源权限
     *
     * @param orgId
     * @param relationId
     * @param userId
     * @param type
     */
    public void grantEventPermission(Long orgId, Long relationId, Long userId, String type) {
        switch (type) {
            case "EVENT":
                resourceCorporationService.shareToUser(relationId, type, "ADMIN", orgId, userId, SHARE_FOUND_IGNORE);
                break;
            case "JOURNEY":
                resourceCorporationService.shareToUser(relationId, type, "ADMIN", orgId, userId, SHARE_FOUND_IGNORE);
                break;
            case "SURVEY":
                resourceCorporationService.shareToUser(relationId, type, "ADMIN", orgId, userId, SHARE_FOUND_IGNORE);
                break;
            default:
                log.error("不支持的资源类型：{}", type);
                return;
        }
    }

}
