package cn.hanyi.cem.task.consumer.notify;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskBotNotifyDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.cem.task.consumer.notify.bot.AbsWarningBot;
import cn.hanyi.cem.task.consumer.notify.bot.ContentDto;
import cn.hanyi.ctm.constant.EventActionStatusType;
import cn.hanyi.ctm.constant.EventActionType;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import cn.hanyi.ctm.constant.connector.ConnectorPushType;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.repository.CustomerRepository;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import cn.hanyi.ctm.service.EventActionService;
import cn.hanyi.ctm.service.EventService;
import cn.hanyi.survey.core.constant.question.FormatType;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.SystemNotificationService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hanyi.survey.core.constant.question.QuestionType.*;

@Slf4j
@Component
public class NotifyBotWarningConsumer extends NotifyBaseHelper implements ITaskConsumer<TaskBotNotifyDto> {

    @Autowired
    private WorkerProperties workerProperties;
    @Autowired
    private SystemNotificationService systemNotificationService;
    @Autowired
    private ConnectorConsumerService connectorConsumerService;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;
    @Autowired
    private CustomerRepository customerRepository;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;
    @Autowired
    private List<AbsWarningBot> bots;
    @Autowired
    private EventService eventService;
    @Autowired
    private EventActionService actionService;
    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;
    private final static String TEMPLATE_NAME = "event";

    private final static Pattern PARAMETER_PATTERN = Pattern.compile("\\$\\{parameters\\.(\\w+)");
    private final static Pattern VALUE_PATTERN = Pattern.compile("\\$\\{value\\.(\\w+)");


    @Override
    public TaskType type() {
        return TaskType.WARNING_BOT_NOTIFY;

    }


    @Override
    public ConsumerStatus consumer(CemTask entity, TaskBotNotifyDto dto) {
        var ruleIdsSet = dto.getWarningRuleIds();
        var botContentDto = new ContentDto();
        log.info("bot waringRule id: {}", ruleIdsSet);
        if (ruleIdsSet == null || ruleIdsSet.isEmpty()) {
            log.error("bot waringRule id is empty");
            return ConsumerStatus.CANCELED;
        }
        var targetUrl = StringUtils.isNotEmpty(workerProperties.getEvent().getWarning().getEventUrl()) ? String.format("%s%s", workerProperties.getEvent().getWarning().getEventUrl(), dto.getWarningId()) : "";
        botContentDto.setTargetUrl(targetUrl);
        botContentDto.setEventId(dto.getWarningId());
        botContentDto.setResponseId(dto.getResponseId());
        surveyResponseRepository.findById(dto.getResponseId()).ifPresent(r -> {
            var simpleSurvey = surveyRepository.findSimpleById(r.getSurveyId());

            botContentDto.setSurveyName(simpleSurvey.getTitle());
            botContentDto.setParameters(r.getParameters());

            if (StringUtils.isNotEmpty(r.getDepartmentCode())) botContentDto.setDepartmentCode(r.getDepartmentCode());
            if (StringUtils.isNotEmpty(r.getDepartmentName())) botContentDto.setDepartmentName(r.getDepartmentName());

            customerRepository.findFirstByOrgIdAndIdOrExternalUserId(dto.getOrgId(), r.getCustomerId(),
                    StringUtils.isNotEmpty(r.getExternalUserId()) ? r.getExternalUserId() : null
            ).ifPresent(c -> {
                //如果不为空就给botContentDto赋值
                if (StringUtils.isNotEmpty(c.getEmail())) botContentDto.setCustomerEmail(c.getEmail());
                if (StringUtils.isNotEmpty(c.getUsername())) botContentDto.setCustomerName(c.getUsername());
                if (StringUtils.isNotEmpty(c.getExternalUserId())) botContentDto.setCustomerExternalUserId(c.getExternalUserId());
                if (StringUtils.isNotEmpty(c.getMobile())) botContentDto.setCustomerPhone(c.getMobile());
            });

            var eventMonitorRules = eventMonitorRulesRepository.findAllById(ruleIdsSet);
            // 排除不需要发送的规则
            ruleIdsSet.removeAll(eventMonitorRules.stream().filter(rule -> !rule.getNotifyConsumer()).collect(Collectors.toSet()));
            StringBuffer botName = new StringBuffer();
            connectorConsumerService.webhookConnector(ruleIdsSet, ConnectorPushType.WARNING, ConnectorPushCondition.WARNING)
                    .stream().filter(c -> c.getProviderType().equals(ConnectorProviderType.BOT))
                    .forEach(c -> {
                        log.info("bot notify: connectorId:{} with responseId:{}", c.getId(), dto.getResponseId());
                        var connectorConsumer = connectorConsumerService.getConsumer(c);
                        var warningLevel = eventMonitorRules.stream().filter(e -> e.getId().equals(connectorConsumer.getRelationId())).map(rl -> rl.getLevel().getSimpleText()).collect(Collectors.joining(";"));
                        var warningName = eventMonitorRules.stream().filter(e -> e.getId().equals(connectorConsumer.getRelationId())).map(rl -> rl.getTitle()).collect(Collectors.joining(";"));

                        botContentDto.setWarningLevel(warningLevel);
                        botContentDto.setWarningName(warningName);
                        botContentDto.setValue(buildRsponseMap(r, connectorConsumer.getContent()));

                        bots.stream().filter(b -> c.getType().equals(b.getType())).forEach(b -> {
                            botName.append(b.getType().getName()).append("、");

                            String content = connectorConsumer.getContent();
                            regParametersKey(content).forEach(key -> {
                                if (!botContentDto.getParameters().containsKey(key)) {
                                    botContentDto.getParameters().put(key, "--/--");
                                }
                            });

                            Map body = b.buildBody(TEMPLATE_NAME, JsonHelper.toMap(botContentDto), content);
                            b.send(JsonHelper.toJson(body), c.getGateway());
                        });

                    });
            if (botName.length() > 0) {
                Event event = eventService.require(dto.getWarningId());
                actionService.addActionSingle(
                        new SimpleUser(null, "系统", null, null, null, null),
                        EventActionType.ACTION_TYPE_NOTICE,
                        "已推送：" + botName.substring(0, botName.length() - 1),
                        EventActionStatusType.SUCCESS,
                        event
                );
            }
        });


        return ConsumerStatus.SUCCESS;
    }


    private Map<String, String> buildRsponseMap(SurveyResponse response, String content) {
        Map<String, String> map = new HashMap<>();
        List<String> codes = matchKeys(VALUE_PATTERN, content);
        if (codes.isEmpty()) {
            return map;
        }
        Map<Long, String> questionIdCodeMap = new HashMap<>();
        Map<Long, Map<String, String>> questionItemMap = new HashMap<>();
        Map<Long, Integer> questionIdAreaTypeMap = new HashMap<>();
        buildQuestionItemMap(response.getSurveyId(), codes, questionIdCodeMap, questionItemMap, questionIdAreaTypeMap);
        if (questionIdCodeMap.isEmpty()) {
            return map;
        }
        List<QuestionType> labelTypes = List.of(
                SINGLE_CHOICE,
                MULTIPLE_CHOICES,
                COMBOBOX,
                EVALUATION,
                SCORE_EVALUATION
        );
        List<SurveyResponseCell> cells = surveyResponseCellRepository.findBySurveyIdAndResponseIdAndQuestionIdIn(response.getSurveyId(), response.getId(), questionIdCodeMap.keySet());
        cells.forEach(cell -> {
            String code = questionIdCodeMap.get(cell.getQuestionId());
            if (StringUtils.isNotEmpty(code)) {
                Object value = cell.getValue();
                if (value != null) {
                    if (labelTypes.contains(cell.getType())) {
                        parseValueToLabel(cell, value, questionItemMap, map, code);
                    } else {
                        parseValue(cell, questionIdAreaTypeMap.get(cell.getQuestionId()), value, map, code);
                    }
                }
            }
        });
        return map;
    }

    private void parseValue(SurveyResponseCell cell, Integer areaType, Object value, Map<String, String> map, String code) {
        if (value instanceof List) {
            map.put(code, JsonHelper.toJson(value));
        } else {
            String v = value.toString();
            if (cell.getType() == TEXT && v.length() > 500) {
                // 文本题长度不可控，截取前500个字
                map.put(code, v.substring(0, 500));
            } else if (cell.getType() == DATE) {
                String s = "";
                try {
                    long time = Long.parseLong(v);
                    LocalDateTime dateTime = DateHelper.toLocalDateTime(new Date(time));
                    if (areaType != null) {
                        FormatType formatType = Arrays.stream(FormatType.values()).filter(f -> f.ordinal() == areaType).findFirst().orElse(null);
                        if (formatType == FormatType.YEAR) {
                            s = dateTime.getYear() + "年";
                        } else if (formatType == FormatType.MONTH) {
                            s = dateTime.getYear() + "年" + dateTime.getMonthValue() + "月";
                        } else if (formatType == FormatType.DAY) {
                            s = dateTime.getYear() + "年" + dateTime.getMonthValue() + "月" + dateTime.getDayOfMonth() + "日";
                        }
                    }
                } catch (Throwable e) {
                    // ignore parse date error
                }
                map.put(code, s);
            } else if (cell.getType() == NUMBER) {
                if (value instanceof Double) {
                    Double d = (Double) value;
                    if (d.longValue() == d) {
                        v = d.intValue() + "";
                    }
                }
                map.put(code, v);
            } else {
                map.put(code, v);
            }
        }
    }

    private void parseValueToLabel(SurveyResponseCell cell, Object value, Map<Long, Map<String, String>> questionItemMap, Map<String, String> map, String code) {
        List<String> values = new ArrayList<>();
        if (value instanceof List<?>) {
            ((List<?>) value).forEach(i -> {
                if (i != null) {
                    values.add(i.toString());
                }
            });
        } else {
            values.add(value.toString());
        }
        Map<String, String> itemMap = questionItemMap.get(cell.getQuestionId());
        if (itemMap != null) {
            List<String> texts = new ArrayList<>();
            values.forEach(i -> {
                String text = itemMap.get(i);
                if (StringUtils.isNotEmpty(text)) {
                    texts.add(text);
                }
            });
            if (!texts.isEmpty()) {
                map.put(code, String.join(";", texts));
            }
        }
    }

    private void buildQuestionItemMap(Long surveyId, List<String> codes, Map<Long, String> questionIdCodeMap, Map<Long, Map<String, String>> questionItemMap, Map<Long, Integer> questionIdAreaTypeMap) {
        List<Integer> types = Stream.of(
                        TEXT, SINGLE_CHOICE, MULTIPLE_CHOICES, SCORE, NUMBER, MOBILE, EMAIL, DATE, DROP_DOWN, EVALUATION, COMBOBOX, SCORE_EVALUATION, NPS)
                .map(QuestionType::getIndex).collect(Collectors.toList());
        String sqlItems = "select sq.id, sq.code, sq.area_type areaType, sqi.value, sqi.text from survey_question sq" +
                " left join survey_question_item sqi on sqi.q_id=sq.id" +
                " where sq.s_id=:surveyId and sq.code in (:codes) and sq.type in (:types)";
        Map<String, Object> params = new HashMap<>();
        params.put("surveyId", surveyId);
        params.put("codes", codes);
        params.put("types", types);

        jdbcTemplate.query(sqlItems, params, (rs, rowNum) -> {
            long id = rs.getLong("id");
            String code = rs.getString("code");
            String value = rs.getString("value");
            String text = rs.getString("text");
            Integer areaType = rs.getObject("areaType") == null ? null : rs.getInt("areaType");
            if (StringUtils.isNotEmpty(text)) {
                text = RegularExpressionUtils.replaceHtml(text);
            }
            if (areaType != null) {
                questionIdAreaTypeMap.put(id, areaType);
            }
            questionIdCodeMap.put(id, code);
            if (StringUtils.isNotEmpty(value) && StringUtils.isNotEmpty(text)) {
                questionItemMap.computeIfAbsent(id, k -> new HashMap<>()).put(value, text);
            }
            return id;
        });
    }

    private List<String> regParametersKey(String content) {
        return matchKeys(PARAMETER_PATTERN, content);
    }

    private List<String> matchKeys(Pattern pattern, String content) {
        // 使用正则表达式匹配出模板中的参数parameters.xxx
        ArrayList<String> keys = new ArrayList<>();
        if (content == null) return keys;
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            keys.add(matcher.group(1));
        }
        return keys;
    }

}
