package cn.hanyi.cem.task.consumer.notify;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskNotifyInboxDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.extension.constant.InboxMessageType;
import org.befun.extension.entity.InboxMessage;
import org.befun.extension.service.InboxMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NotifyInboxConsumer extends NotifyBaseHelper implements ITaskConsumer<TaskNotifyInboxDto> {

    @Autowired
    private InboxMessageService inboxMessageService;

    @Autowired
    private UserService userService;


    @Override
    public TaskType type() {
        return TaskType.WARNING_NOTIFY_INBOX;

    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskNotifyInboxDto param) {

        InboxMessage inbox = new InboxMessage();
        inbox.setOrgId(param.getOrgId());
        inbox.setType(InboxMessageType.valueOf(param.getType()));
        inbox.setFromUserId(0L);
        inbox.setTitle(param.getTitle());
        inbox.setDescription(param.getTitle());
        inbox.setTargetUrl(param.getTargetUrl());

        var users = getNotifyUsers(param.getOrgId(), param.getDepartmentId(), param.getRoleIds(), param.getUserIds());

        if (CollectionUtils.isNotEmpty(users)) {
            for (SimpleUser user : users) {
                log.info("inboxNotify: {} - {}:{}", param.getTitle(), user.getId(), user.getTruename());
                InboxMessage add = new InboxMessage();
                add.setId(null);
                add.setUserId(user.getId());
                add.setOrgId(inbox.orgId);
                add.setType(inbox.getType());
                add.setFromUserId(inbox.getFromUserId());
                add.setFromUserName(inbox.getFromUserName());
                add.setTitle(inbox.getTitle());
                add.setDescription(inbox.getDescription());
                add.setTargetUrl(inbox.getTargetUrl());
                add.setReadStatus(inbox.getReadStatus());
                inboxMessageService.addInboxMessage(add);
                addResourcePermission(param, user.getId());
            }
        }
        return ConsumerStatus.SUCCESS;
    }
}
