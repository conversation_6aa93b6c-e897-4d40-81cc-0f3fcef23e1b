package cn.hanyi.cem.task.consumer.notify;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskNotifyOutWorkerDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.SystemNotificationService;
import org.befun.auth.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class NotifyOutWorkerConsumer extends NotifyBaseHelper implements ITaskConsumer<TaskNotifyOutWorkerDto> {

    @Autowired
    private WorkerProperties workerProperties;


    @Autowired
    private SystemNotificationService systemNotificationService;

    @Autowired
    private UserService userService;

    @Override
    public TaskType type() {
        return TaskType.WARNING_NOTIFY_OUTWORKER;

    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskNotifyOutWorkerDto param) {
        var params = param.getParams();
        var types = param.getTypes().stream().map(NotificationType::valueOf).toArray(NotificationType[]::new);
        var users = getNotifyUsers(param.getOrgId(), null, param.getRoleIds(), param.getUserIds());

        if (CollectionUtils.isNotEmpty(users)) {
            for (SimpleUser user : users) {
                log.info("type: {} outworker notify: {} - {}:{}", type().name(), param.getTemplate(), user.getId(), user.getTruename());
                params.put("targetTruename", user.getTruename() == null ? "" : user.getTruename());
                if (workerProperties.getNotify().getEnabled() && types.length > 0) {
                    systemNotificationService.notifyToUser(
                            param.getApp(),
                            user.getId(),
                            types,
                            param.getTemplate(),
                            params,
                            true);
                    addResourcePermission(param, user.getId());
                }
            }
        }
        return ConsumerStatus.SUCCESS;
    }
}
