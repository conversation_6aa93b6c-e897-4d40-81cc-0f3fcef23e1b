package cn.hanyi.cem.task.consumer.notify;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskSmsNotificationDelayDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import org.befun.extension.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class NotifySmsDelayConsumer implements ITaskConsumer<TaskSmsNotificationDelayDto> {

    @Autowired
    private SmsService smsService;

    @Override
    public TaskType type() {
        return TaskType.SMS_NOTIFICATION_DELAY;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskSmsNotificationDelayDto param) {
        this.smsService.sendMessageByTemplate(param.getTemplateName(), param.getMobile(), param.getParameters());
        return ConsumerStatus.SUCCESS;
    }
}
