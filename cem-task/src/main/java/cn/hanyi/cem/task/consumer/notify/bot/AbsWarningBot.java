package cn.hanyi.cem.task.consumer.notify.bot;

import cn.hanyi.cem.task.consumer.properties.BotNotifyProperties;
import cn.hanyi.cem.task.consumer.properties.BotNotifyProperties.BotNotifyTemplateProperties;
import cn.hanyi.cem.task.consumer.properties.BotNotifyProperties.BotProperties;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import cn.hanyi.ctm.dto.connector.ConnectorConsumerBotContentDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableRetry
public abstract class AbsWarningBot {

    @Autowired
    public BotNotifyProperties notifyProperties;

    public abstract ConnectorType getType();

    public BotNotifyTemplateProperties getTemplate(String templateName) {

        return notifyProperties.getBots().stream()
                .filter(r -> r.getChannel() == getType()).map(BotProperties::getTemplates)
                .findFirst().orElse(new ArrayList<>()).stream()
                .filter(t -> t.getName().equals(templateName)).findFirst().orElse(null);
    }

    @Retryable
    public void send(String body, String url) {
        try {
            var resp = Request.Post(url).bodyString(body, ContentType.APPLICATION_JSON)
                    .connectTimeout(1000 * 60)
                    .socketTimeout(1000 * 60).execute().returnContent().asString();
            log.info("bot:{} send resp:{}", getType().name(), resp);
        } catch (Exception e) {
            log.error("bot:{} send error:{}",getType().name(), e);
        }
    }

    @Retryable
    public void sendImage(byte[] imageBytes, String url) {
        if (imageBytes != null) {
            var imageBody = buildImageBody(imageBytes);
            if (imageBody != null) {
                send(JsonHelper.toJson(imageBody), url);
            }
        } else {
            log.warn("bot:{} 图片为空，发送失败", getType().name());
        }
    }



    public String buildContent(String templateName, Map<String, Object> params) {

        BotNotifyTemplateProperties template = getTemplate(templateName);

        return TemplateEngine.renderTextTemplate(template.getContent(), params);
    }

    public String buildContent(String templateName, Map<String, Object> params, String templateContent) {

        ConnectorConsumerBotContentDto connectorConsumerBotContentDto= JsonHelper.toObject(templateContent, ConnectorConsumerBotContentDto.class);

        AtomicReference<String> content = new AtomicReference<>(null);

        Optional.ofNullable(connectorConsumerBotContentDto).flatMap(c -> Optional.ofNullable(formatContent(c))).ifPresent(content::set);

        return TemplateEngine.renderTextTemplate(Optional.ofNullable(content.get()).orElse(getTemplate(templateName).getContent()), params);
    }


    public abstract String formatContent(ConnectorConsumerBotContentDto content);

    public abstract Map buildBody(String templateName, Map<String, Object> params);

    public abstract Map buildBody(String templateName, Map<String, Object> params, String templateContent);

    public abstract Map buildImageBody(byte[] imageBase64);

}
