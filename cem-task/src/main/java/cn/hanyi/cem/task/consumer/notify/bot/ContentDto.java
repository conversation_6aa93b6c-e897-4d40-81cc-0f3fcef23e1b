package cn.hanyi.cem.task.consumer.notify.bot;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class ContentDto {

        String title = "体验家系统预警通知";
        String surveyName = "--/--";
        String warningLevel = "--/--";
        String warningName = "--/--";
        String customerName = "--/--";
        String customerPhone = "--/--";
        String customerEmail = "--/--";
        String customerExternalUserId = "--/--";
        String departmentCode = "--/--";
        String departmentName = "--/--";
        Long eventId = 0L;
        Long responseId = 0L;
        Map<String, Object> parameters = new HashMap<>();
        String targetUrl;
        // 答卷数据
        Map<String, String> value = new HashMap<>();
}
