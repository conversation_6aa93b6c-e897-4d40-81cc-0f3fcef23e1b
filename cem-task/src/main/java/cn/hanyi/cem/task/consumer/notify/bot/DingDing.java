package cn.hanyi.cem.task.consumer.notify.bot;

import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.ctm.constant.connector.ConnectorType;

import java.nio.file.OpenOption;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import cn.hanyi.ctm.dto.connector.ConnectorConsumerBotContentDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DingDing extends AbsWarningBot {

    @Autowired
    FileStorageService storageService;

    @Override
    public ConnectorType getType() {
        return ConnectorType.DING_DING;
    }

    @Override
    public Map buildBody(String templateName, Map<String, Object> params) {
        var body = new HashMap<String, Object>(2);
        body.put("msgtype", "markdown");
        body.put("markdown",
                Map.of("title", params.get("title"), "text", buildContent(templateName, params)));
        return body;
    }

    @Override
    public String formatContent(ConnectorConsumerBotContentDto content) {
        StringBuffer body = new StringBuffer();
        body.append("## ").append(content.getTitle()).append("\n\n");
        content.splitContent().forEach(line-> body.append("> ").append(line).append("\n\n"));
        return body.toString();
    }

    @Override
    public Map buildBody(String templateName, Map<String, Object> params, String templateContent) {
        var body = new HashMap<String, Object>(2);
        body.put("msgtype", "markdown");
        body.put("markdown",
                Map.of("title", params.get("title"), "text", buildContent(templateName, params, templateContent)));
        return body;
    }

    @Override
    public Map buildImageBody(byte[] imageBytes) {
        FileInfo fileInfo = storageService.of(imageBytes)
                .setOriginalFilename(String.format("%s.png", UUID.randomUUID())).upload();
        String text = String.format("![screenshot](%s)", fileInfo.getUrl());

        var body = new HashMap<String, Object>(2);
        body.put("msgtype", "markdown");
        body.put("markdown", Map.of("title", "体验家推送", "text", text));
        return body;
    }
}
