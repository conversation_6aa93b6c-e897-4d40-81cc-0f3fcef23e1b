package cn.hanyi.cem.task.consumer.notify.bot;

import cn.hanyi.ctm.constant.connector.ConnectorType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hanyi.ctm.dto.connector.ConnectorConsumerBotContentDto;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Component;

@Component
public class FeiShu extends AbsWarningBot {

    @Override
    public ConnectorType getType() {
        return ConnectorType.FEI_SHU;
    }

    @Override
    public String formatContent(ConnectorConsumerBotContentDto content) {
        FeiShuContentDto body = new FeiShuContentDto(content);
        return JsonHelper.toJson(body);
    }


    @Override
    public Map buildBody(String templateName, Map<String, Object> params) {
        var body = new HashMap<String, Object>(2);
        body.put("msg_type", "post");
        body.put("content", JsonHelper.toObject(buildContent(templateName, params), Map.class));
        return body;
    }

    @Override
    public Map buildBody(String templateName, Map<String, Object> params,String templateContent) {
        var body = new HashMap<String, Object>(2);
        body.put("msg_type", "post");
        body.put("content", JsonHelper.toObject(buildContent(templateName, params, templateContent), Map.class));
        return body;
    }

    @Override
    public Map buildImageBody(byte[] imageBytes) {
        return null;
    }
}
