package cn.hanyi.cem.task.consumer.notify.bot;

import cn.hanyi.ctm.dto.connector.ConnectorConsumerBotContentDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class FeiShuContentDto {

       public Zh post;

       @Setter
       @Getter
       @AllArgsConstructor
       @NoArgsConstructor
       public class Tag{
              private String tag;
              private String text;
              private String href;

       }

       @Setter
       @Getter
       @AllArgsConstructor
       @NoArgsConstructor
       public class Text{
              private String title;
              private List<List<Tag>> content;
       }

       @Setter
       @Getter
       @AllArgsConstructor
       @NoArgsConstructor
       public class Zh{
              private Text zh_cn;
       }

       @Setter
       @Getter
       @AllArgsConstructor
       @NoArgsConstructor
       public class Post{
              private Zh post;
       }

       FeiShuContentDto(ConnectorConsumerBotContentDto content){

              String targetUrl = "(${targetUrl})";
              Zh zh = new Zh();
              Text text = new Text();
              List<List<Tag>> tags = new ArrayList<>();

              content.splitContent().forEach(line->{
                     if(line.contains(targetUrl)){
                            String regex = "(.*?！)(\\[.*?]\\(.*?\\))";

                            Pattern pattern = Pattern.compile(regex);
                            Matcher matcher = pattern.matcher(line);

                            if (matcher.find()) {
                                   String t = matcher.group(1);
                                   String l = matcher.group(2);
                                   tags.add(List.of(new Tag("text", t,null), new Tag("a",l.replace(targetUrl,""), "${targetUrl}")));
                            }

                     }else {
                            tags.add(List.of(new Tag("text",line,null)));
                     }
              });

              text.setContent(tags);
              text.setTitle(content.getTitle());
              zh.setZh_cn(text);
              post = zh;
       }
}
