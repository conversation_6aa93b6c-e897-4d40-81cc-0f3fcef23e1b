package cn.hanyi.cem.task.consumer.notify.bot;

import cn.hanyi.ctm.constant.connector.ConnectorType;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import cn.hanyi.ctm.dto.connector.ConnectorConsumerBotContentDto;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

@Component
public class WechatWork extends AbsWarningBot {

    @Override
    public ConnectorType getType() {
        return ConnectorType.WECHAT_WORK;
    }

    @Override
    public String formatContent(ConnectorConsumerBotContentDto content) {
        StringBuffer body = new StringBuffer();
        body.append("# ").append(content.getTitle()).append("\n");
        content.splitContent().forEach(line-> body.append("> ").append(line).append("\n"));
        return body.toString();
    }

    @Override
    public Map buildBody(String templateName, Map<String, Object> params) {
        var body = new HashMap<String, Object>(2);
        body.put("msgtype", "markdown");
        body.put("markdown", Map.of("content", buildContent(templateName, params)));
        return body;
    }

    @Override
    public Map buildBody(String templateName, Map<String, Object> params, String templateContent) {
        var body = new HashMap<String, Object>(2);
        body.put("msgtype", "markdown");
        body.put("markdown", Map.of("content", buildContent(templateName, params, templateContent)));
        return body;
    }

    @Override
    public Map buildImageBody(byte[] imageBytes) {
        String imageBase64 = Base64.getEncoder().encodeToString(imageBytes);
        String imageMd5 = DigestUtils.md5DigestAsHex(imageBytes);
        var body = new HashMap<String, Object>(2);
        body.put("msgtype", "image");
        body.put("image", Map.of("base64", imageBase64, "md5", imageMd5));
        return body;
    }
}
