package cn.hanyi.cem.task.consumer.orgchange;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.OrgChangeType;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskOrgChangeDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AppType;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.entity.Organization;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.UserService;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.RegHelper;
import org.befun.extension.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.befun.auth.constant.AppVersion.EMPTY;
import static org.befun.auth.constant.AppVersion.FREE;

@Component
public class OrgChangeNotifyConsumer implements ITaskConsumer<TaskOrgChangeDto> {

    @Autowired
    private WorkerProperties workerProperties;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private UserService userService;
    @Autowired
    private SmsService smsService;

    @Override
    public TaskType type() {
        return TaskType.ORG_CHANGE_NOTIFY;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskOrgChangeDto param) {
        if (param.getChangeType() == OrgChangeType.REGISTER) {
            if (param.getRegister() != null) {
                notifyByOrgRegister(param.getRegister());
            }
        } else if (param.getChangeType() == OrgChangeType.CHANGE_VERSION) {
            if (param.getChangeVersion() != null) {
                notifyByOrgChange(param.getChangeVersion());
            }
        }
        return ConsumerStatus.SUCCESS;
    }

    private void notifyByOrgRegister(TaskOrgChangeDto.OrgRegister register) {
        String mobile = getOrgOwnerUserMobile(register.getOrgId());
        smsNotify(mobile, workerProperties.getNotify().getOrgChangeRegister(), new HashMap<>());
    }

    /**
     * 从 FREE 到其他版本需要
     * 不是调研家的
     */
    private void notifyByOrgChange(TaskOrgChangeDto.OrgChangeVersion changeVersion) {
        Organization organization = organizationService.get(changeVersion.getOrgId());
        if (organization == null) {
            return;
        }
        AppVersion from = EnumHelper.parse(AppVersion.values(), changeVersion.getFromVersion(), FREE);
        AppVersion to = EnumHelper.parse(AppVersion.values(), changeVersion.getToVersion(), FREE);
        List<AppType> appTypeList = organizationService.parseOrgAppTypes(organization);
        if (FREE == from && FREE != to && EMPTY != to && appTypeList.contains(AppType.cem)) {
            Map<String, Object> params = new HashMap<>();
            params.put("appVersion", to.getLabel());
            smsNotify(getOrgOwnerUserMobile(organization), workerProperties.getNotify().getOrgChangeVersion(), params);
        }
    }

    private String getOrgOwnerUserMobile(Long orgId) {
        return getOrgOwnerUserMobile(organizationService.get(orgId));
    }

    private String getOrgOwnerUserMobile(Organization org) {
        if (org == null || org.getOwnerId() == null || org.getOwnerId() == 0) {
            return null;
        }
        SimpleUser user = userService.getSimple(org.getOwnerId()).orElse(null);
        if (user == null) {
            return null;
        }
        return user.getMobile();
    }

    private void smsNotify(String mobile, String templateName, Map<String, Object> params) {
        if (RegHelper.isMobile(mobile) && StringUtils.isNotEmpty(templateName)) {
            smsService.sendMessageByTemplate2(templateName, mobile, params);
        }
    }
}
