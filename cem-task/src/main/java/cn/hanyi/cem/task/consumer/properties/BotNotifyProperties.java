package cn.hanyi.cem.task.consumer.properties;

import cn.hanyi.ctm.constant.connector.ConnectorType;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "worker.notify")
public class BotNotifyProperties {
    private String test;

    private List<BotProperties> bots;

    @Getter
    @Setter
    public static class BotProperties {
        private ConnectorType channel;

        private List<BotNotifyTemplateProperties> templates;
    }

    @Getter
    @Setter
    public static class BotNotifyTemplateProperties {
        private String name;
        private String msgType;
        private String content;
    }
}