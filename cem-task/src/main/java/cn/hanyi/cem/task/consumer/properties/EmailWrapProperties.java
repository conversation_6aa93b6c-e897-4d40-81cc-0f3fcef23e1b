package cn.hanyi.cem.task.consumer.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "worker.email")
public class EmailWrapProperties {
    private boolean enable = true;
    private String linkPattern = "https?://(test-|dev-)?t\\.xmplus\\.cn/[0-9a-zA-Z]*";
    private String linkWrap = "<a href=\"%1$s\">%1$s</a>";
    private String linkWrapFlag = "http";               // contain
    private String contentWrap = "<div>%1$s</div>";
    private String contentWrapFlag = "<";               // startsWith
}