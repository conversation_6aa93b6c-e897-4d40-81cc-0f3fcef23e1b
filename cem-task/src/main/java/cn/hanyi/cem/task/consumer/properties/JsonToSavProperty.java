package cn.hanyi.cem.task.consumer.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "worker.converter.json-to-sav")
public class JsonToSavProperty {

    private String url;
    private int timeoutMs = 120000;
}
