package cn.hanyi.cem.task.consumer.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "data-access.gmp")
public class SendGMPProperties {
    private DatahubProperties datahub;

    @Getter
    @Setter
    public static class DatahubProperties {

        private String endpoint;
        private String project;
        private String topic;
        private String key;
        private String secret;
    }



}