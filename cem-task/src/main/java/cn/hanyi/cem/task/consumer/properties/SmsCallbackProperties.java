package cn.hanyi.cem.task.consumer.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "worker.sms.callback")
public class SmsCallbackProperties {

    private int validHours = 168;
    private boolean enablePullChuanglan = true;
    private String cronPullChuanglan = "0 */5 * * * *";
}