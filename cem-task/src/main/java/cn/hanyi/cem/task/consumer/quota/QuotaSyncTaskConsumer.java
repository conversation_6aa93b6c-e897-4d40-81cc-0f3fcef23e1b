package cn.hanyi.cem.task.consumer.quota;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskSyncQuotaDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.survey.service.QuotaService;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.UserTaskService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.constant.TaskStatus;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class QuotaSyncTaskConsumer implements ITaskConsumer<TaskSyncQuotaDto> {

    @Lazy
    @Autowired
    private QuotaService quotaService;
    @Autowired
    private UserTaskService userTaskService;

    @Override
    public TaskType type() {
        return TaskType.QUOTA_SYNC;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskSyncQuotaDto param) {
        TaskProgress taskProgress = userTaskService.get(param.getTaskProgressId());
        if (taskProgress == null) {
            entity.setResponse(String.format("task %d is null", param.getTaskProgressId()));
            return ConsumerStatus.FAILED;
        } else if (taskProgress.getStatus().isCompleted()) {
            entity.setResponse(String.format("task %d is completed", param.getTaskProgressId()));
            return ConsumerStatus.CANCELED;
        } else if (taskProgress.getStatus() != TaskStatus.RUNNING) {
            userTaskService.updateTaskStatus(param.getTaskProgressId(), TaskStatus.RUNNING);
        }
        AtomicInteger count = new AtomicInteger();
        AtomicInteger appendCount = new AtomicInteger();
        try {
            if (param.isCem()) {
                quotaService.syncCemQuotaByPage(
                        param.getSurveyId(),
                        param.getSyncQuotas(),
                        param.getPage(),
                        param.getSize(),
                        response -> appendSuccess(taskProgress, param.getRealSize(), count, appendCount)
                );
            } else {
                quotaService.syncAdminxQuotaByPage(
                        param.getSurveyId(),
                        param.getSyncQuotas(),
                        param.getPage(),
                        param.getSize(),
                        response -> appendSuccess(taskProgress, param.getRealSize(), count, appendCount)
                );
            }
        } catch (Throwable e) {
            log.error("sync {} quota error, taskId={}, params={}", param.isCem() ? "cem" : "adminx", entity.getId(), JsonHelper.toJson(param), e);
        }
        completePage(entity, param, taskProgress, count, appendCount);
        return ConsumerStatus.SUCCESS;
    }

    private void appendSuccess(TaskProgress taskProgress, int realSize, AtomicInteger count, AtomicInteger appendCount) {
        if (count.getAndIncrement() < realSize) {
            appendCount.incrementAndGet();
            userTaskService.appendTaskSuccessSize(taskProgress.getId(), 1, false);
        }
    }

    private void completePage(CemTask entity, TaskSyncQuotaDto param, TaskProgress taskProgress, AtomicInteger count, AtomicInteger appendCount) {
        int realSize = param.getRealSize();
        int appendSuccess = count.get() < realSize ? (realSize - count.get()) : 0;
        appendCount.addAndGet(appendSuccess);
        boolean complete = userTaskService.updateTaskProgress(taskProgress.getId(), appendSuccess, 0);
        if (complete) {
            quotaService.syncQuotaComplete(param.getSurveyId(), true, param.isCem());
        }
        log.info("sync quota page complete, taskId={}, page={}, realSize={}, count={}, appendCount={} taskComplete={}",
                entity.getId(), param.getPage(), realSize, count.get(), appendCount.get(), complete);
    }
}
