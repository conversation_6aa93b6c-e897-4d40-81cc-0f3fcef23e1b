package cn.hanyi.cem.task.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskResponseDownloadDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.cem.task.consumer.properties.JsonToSavProperty;
import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.common.file.storage.MockMultipartFile;
import cn.hanyi.survey.core.constant.DownloadType;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.dto.DownloadDto;
import cn.hanyi.survey.service.DownloadService;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.service.download.ResponseDownloadHelper;
import cn.hanyi.survey.service.download.dto.ResponseDownloadFile;
import cn.hanyi.survey.service.response.ResponseDownloadService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.auth.dto.usertask.UserTaskResponseDownloadDto;
import org.befun.auth.dto.usertask.UserTaskResultDto;
import org.befun.auth.service.UserTaskService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.constant.TaskStatus;
import org.befun.task.repository.TaskProgressRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class ResponseDownloadAttachmentConsumer implements ITaskConsumer<TaskResponseDownloadDto> {
    @Autowired
    private UserTaskService taskProgressService;

    @Autowired
    private TaskProgressRepository taskProgressRepository;

    @Autowired
    private ResponseDownloadHelper responseDownloadHelper;

    @Autowired
    private UserTaskService userTaskService;

    @Override
    public TaskType type() {
        return TaskType.RESPONSE_DOWNLOAD_ATTACHMENT;
    }

    @SneakyThrows
    @Override
    public ConsumerStatus consumer(CemTask entity, TaskResponseDownloadDto param) {
        log.info("开始下载问卷:{}答题数据", param.getSurveyId());
        var surveyId = param.getSurveyId();
        var downloadDto = JsonHelper.toObject(param.getData(), DownloadDto.class);
        var taskId = param.getTaskProgressId();
        var taskProgress = taskProgressService.get(taskId);

        if (taskProgress == null) {
            entity.setResponse("任务不存在");
            return ConsumerStatus.FAILED;
        }
        taskProgress.setStatus(TaskStatus.RUNNING);
        taskProgressRepository.save(taskProgress);

        try {
            // 错误后再重试一次
            FileInfo file = null;
            log.info("task:{}问卷:{}", taskId, param.getSurveyId());
            try {
                if (downloadDto.getUseNewDownload()) {
                    if (downloadDto.getDownloadType() == DownloadType.ATTACHMENT){
                        file = responseDownloadHelper.downloadToUploadFileZip(taskId,surveyId,downloadDto);
                    }
                }
                var userTaskResponseDownloadDto = new UserTaskResponseDownloadDto();
                userTaskResponseDownloadDto.setSurveyId(surveyId);
                if (file != null) {
                    userTaskResponseDownloadDto.setFileName(file.getFilename());
                    userTaskResponseDownloadDto.setFileUrl(file.getUrl());
                    userTaskResponseDownloadDto.setFileSize(readFileSize(file.getUrl()));
                    userTaskService.successTask(taskId, r -> r.setResponseDownload(userTaskResponseDownloadDto));
                    log.info("问卷:{}下载文件:{}", param.getSurveyId(), file.getUrl());
                } else {
                    log.error("问卷:{}下载文件失败", param.getSurveyId());
                    userTaskService.failedTask(taskId, "下载文件失败");
                }

            } catch (Exception e) {
                log.error("问卷:{}下载文件失败", param.getSurveyId(), e);
                userTaskService.failedTask(taskId, e.getMessage());
            }

            userTaskService.syncTaskToDb(taskId);

        } catch (Exception e) {
            userTaskService.failedTask(taskId, e.getMessage());
        }

        return ConsumerStatus.SUCCESS;
    }

    public long readFileSize(String fileUrl) {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(fileUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.connect();
            long contentLength = connection.getContentLengthLong();
            return contentLength;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
}
