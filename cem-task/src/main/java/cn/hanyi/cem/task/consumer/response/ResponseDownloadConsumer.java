package cn.hanyi.cem.task.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskResponseDownloadDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.cem.task.consumer.properties.JsonToSavProperty;
import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.common.file.storage.MockMultipartFile;
import cn.hanyi.survey.core.constant.DownloadType;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.dto.DownloadDto;
import cn.hanyi.survey.service.DownloadService;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.service.download.ResponseDownloadHelper;
import cn.hanyi.survey.service.download.dto.ResponseDownloadFile;
import cn.hanyi.survey.service.response.ResponseDownloadService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.auth.dto.usertask.UserTaskResponseDownloadDto;
import org.befun.auth.dto.usertask.UserTaskResultDto;
import org.befun.auth.service.UserTaskService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.FileService;
import org.befun.task.constant.TaskStatus;
import org.befun.task.repository.TaskProgressRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class ResponseDownloadConsumer implements ITaskConsumer<TaskResponseDownloadDto> {

    @Autowired
    private DownloadService downloadService;

    @Autowired
    private ResponseDownloadService responseDownloadService;

    @Autowired
    private FileStorageService storageService;

    @Autowired
    private UserTaskService taskProgressService;

    @Autowired
    private TaskProgressRepository taskProgressRepository;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private ResponseDownloadHelper responseDownloadHelper;

    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private JsonToSavProperty jsonToSavProperty;

    @Override
    public TaskType type() {
        return TaskType.RESPONSE_DOWNLOAD;
    }

    @SneakyThrows
    @Override
    public ConsumerStatus consumer(CemTask entity, TaskResponseDownloadDto param) {
        log.info("开始下载问卷:{}答题数据", param.getSurveyId());
        var surveyId = param.getSurveyId();
        var survey = surveyService.requireSurvey(surveyId);
        var downloadDto = JsonHelper.toObject(param.getData(), DownloadDto.class);
        var taskId = param.getTaskProgressId();
        var taskProgress = taskProgressService.get(taskId);
        var resultDto = new UserTaskResultDto();

        if (taskProgress == null) {
            entity.setResponse("任务不存在");
            return ConsumerStatus.FAILED;
        }
        taskProgress.setStatus(TaskStatus.RUNNING);
        taskProgressRepository.save(taskProgress);

//        List<String> exceptions = new ArrayList<>();

        try {
            // 错误后再重试一次
            FileInfo file = null;
            log.info("task:{}问卷:{}", taskId, param.getSurveyId());
            try {
                if (downloadDto.getUseNewDownload()) {
                    if (downloadDto.getDownloadType() == DownloadType.SAV) {
                        ResponseDownloadFile downloadFile = responseDownloadHelper.download(taskId, surveyId, downloadDto);
                        file = convertToSav(downloadFile);
                    } else if (downloadDto.getDownloadType() == DownloadType.ATTACHMENT){
                        file = responseDownloadHelper.downloadToUploadFileZip(taskId,surveyId,downloadDto);
                    }else {
                        file = responseDownloadHelper.downloadToUpload(taskId, surveyId, downloadDto);
                    }
                } else {
                    var zipFileName = String.format("%s-%s.zip", RegularExpressionUtils.safeTitle(survey.getTitle()), System.currentTimeMillis());
                    file = createFile(surveyId, downloadDto, zipFileName, taskId);
                }

                var userTaskResponseDownloadDto = new UserTaskResponseDownloadDto();
                userTaskResponseDownloadDto.setSurveyId(surveyId);
                if (file != null) {
                    userTaskResponseDownloadDto.setFileName(file.getFilename());
                    userTaskResponseDownloadDto.setFileUrl(file.getUrl());
                    userTaskResponseDownloadDto.setFileSize(file.getSize());
                    userTaskService.successTask(taskId, r -> r.setResponseDownload(userTaskResponseDownloadDto));
                    log.info("问卷:{}下载文件:{}", param.getSurveyId(), file.getUrl());
                } else {
                    log.error("问卷:{}下载文件失败", param.getSurveyId());
                    userTaskService.failedTask(taskId, "下载文件失败");
                }

            } catch (Exception e) {
                log.error("问卷:{}下载文件失败", param.getSurveyId(), e);
                userTaskService.failedTask(taskId, e.getMessage());
            }

            userTaskService.syncTaskToDb(taskId);

        } catch (Exception e) {
            userTaskService.failedTask(taskId, e.getMessage());
        }

        return ConsumerStatus.SUCCESS;
    }

    public FileInfo convertToSav(ResponseDownloadFile downloadFile) {
        try {
            byte[] newFile = Request.Post(jsonToSavProperty.getUrl())
                    .bodyByteArray(downloadFile.getBytes(), ContentType.APPLICATION_JSON)
                    .connectTimeout(jsonToSavProperty.getTimeoutMs())
                    .socketTimeout(jsonToSavProperty.getTimeoutMs())
                    .execute().returnContent().asBytes();
            var file = new MockMultipartFile(downloadFile.getFileName(), downloadFile.getFileName(), MediaType.MULTIPART_FORM_DATA_VALUE, newFile);
            var pre = storageService.of(file);
            pre.setSaveFilename(URLEncoder.encode(downloadFile.getFileName(), StandardCharsets.UTF_8));
            return pre.upload();
        } catch (IOException e) {
            log.error("json to sav fail ", e);
        }
        return null;
    }


    @SneakyThrows
    public FileInfo createFile(Long surveyId, DownloadDto dto, String fileName, Long taskId) {
        var fileOutputStream = downloadService.createFile(surveyId, dto, taskId);
        var file = new MockMultipartFile(fileName, fileName, MediaType.MULTIPART_FORM_DATA_VALUE, fileOutputStream.toByteArray());
        var pre = storageService.of(file);
        pre.setPath(FileService.PRIVATE_PATH);
        pre.setSaveFilename(URLEncoder.encode(file.getOriginalFilename(), "UTF-8"));
        return pre.upload();
    }
}
