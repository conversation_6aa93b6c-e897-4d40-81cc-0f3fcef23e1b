package cn.hanyi.cem.task.consumer.response;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskResponseImportDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.service.download.ResponseImportParser;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.UserTaskService;
import org.befun.task.constant.TaskStatus;
import org.befun.task.repository.TaskProgressRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ResponseImportConsumer implements ITaskConsumer<TaskResponseImportDto> {

    @Autowired
    private UserTaskService taskProgressService;

    @Autowired
    private TaskProgressRepository taskProgressRepository;

    @Autowired
    private ResponseImportParser responseImportParser;

    @Autowired
    private SurveyService surveyService;

    @Override
    public TaskType type() {
        return TaskType.RESPONSE_IMPORT;
    }
    @Override
    public ConsumerStatus consumer(CemTask entity, TaskResponseImportDto param) {

        log.info("开始导入问卷:{}答题数据", param.getSurveyId());
        var surveyId = param.getSurveyId();
        var survey = surveyService.requireSurvey(surveyId);
        var taskId = param.getTaskProgressId();
        var taskProgress = taskProgressService.get(taskId);

        if (taskProgress == null) {
            entity.setResponse("任务不存在");
            return ConsumerStatus.FAILED;
        }
        taskProgress.setStatus(TaskStatus.RUNNING);
        taskProgressRepository.save(taskProgress);

        try {
            responseImportParser.importResponseConsumer(survey.getOrgId(), survey.getUserId(), surveyId, param.getUrl());
            log.info("导入成功, 问卷:{}", param.getSurveyId());
        } catch (Exception e) {
            log.info("导入答卷失败, 问卷:{}", param.getSurveyId());
            e.printStackTrace();
        }

        return ConsumerStatus.SUCCESS;
    }
}
