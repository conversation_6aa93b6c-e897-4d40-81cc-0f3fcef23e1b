package cn.hanyi.cem.task.consumer.sendchannelsurvey;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskResendChannelSurveyDelayDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.survey.core.constant.channel.ChannelResendStatus;
import cn.hanyi.survey.core.dto.channel.ChannelDelayResendDto;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import cn.hanyi.survey.service.channel.ChannelSendHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 发送问卷渠道短信任务的处理器
 */
@Slf4j
@Component
public class ResendChannelSurveyDelayTaskConsumer implements ITaskConsumer<TaskResendChannelSurveyDelayDto> {

    @Autowired
    private ChannelSendHelper channelSendHelper;
    @Autowired
    private SurveyChannelRepository surveyChannelRepository;

    @Override
    public TaskType type() {
        return TaskType.RESEND_CHANNEL_SURVEY_DELAY;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskResendChannelSurveyDelayDto param) {
        if (param.getSurveyId() == null || param.getChannelId() == null || StringUtils.isEmpty(param.getJobId())) {
            entity.setResponse(String.format("任务参数错误：param = %s", JsonHelper.toJson(param)));
            return ConsumerStatus.FAILED;
        }
        SurveyChannel channel = channelSendHelper.requireChannel(param.getSurveyId(), param.getChannelId());
        ChannelDelayResendDto job = null;
        if (CollectionUtils.isNotEmpty(channel.getResendTimes())) {
            job = channel.getResendTimes().stream().filter(i -> param.getJobId().equals(i.getJobId())).findFirst().orElse(null);
        }
        if (job == null) {
            entity.setResponse(String.format("任务不存在：param = %s", JsonHelper.toJson(param)));
            return ConsumerStatus.FAILED;
        }
        ConsumerStatus status = ConsumerStatus.FAILED;
        try {
            if (job.getStatus() != ChannelResendStatus.QUEUE) {
                entity.setResponse(String.format("任务状态不匹配：param = %s, job = %s", JsonHelper.toJson(param), JsonHelper.toJson(job)));
                return status;
            }
            if (CollectionUtils.isEmpty(job.getResendConditions())) {
                entity.setResponse(String.format("任务缺少发送条件：param = %s, job = %s", JsonHelper.toJson(param), JsonHelper.toJson(job)));
                return status;
            }
            channelSendHelper.resendByDelay(entity.getOrgId(), entity.getUserId(), param.getSurveyId(), channel, job.getResendConditions());
            status = ConsumerStatus.SUCCESS;
        } catch (Throwable e) {
            entity.setResponse(e.getMessage());
        } finally {
            if (status == ConsumerStatus.SUCCESS) {
                job.setStatus(ChannelResendStatus.SUCCESS);
            } else {
                job.setStatus(ChannelResendStatus.FAILURE);
            }
            channel.setModifyTime(new Date());
            surveyChannelRepository.save(channel);
        }
        return status;
    }


}
