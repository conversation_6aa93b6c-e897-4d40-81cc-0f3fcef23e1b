package cn.hanyi.cem.task.consumer.sendchannelsurvey;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskSendChannelSurveyDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.survey.core.dto.channel.ChannelSendCustomerDto;
import cn.hanyi.survey.service.channel.ChannelSendHelper;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.UserTaskService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.constant.TaskStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 发送问卷渠道短信任务的处理器
 */
@Slf4j
@Component
public class SendChannelSurveyTaskConsumer implements ITaskConsumer<TaskSendChannelSurveyDto> {

    @Autowired
    private ChannelSendHelper channelSendHelper;
    @Autowired
    private UserTaskService userTaskService;

    @Override
    public TaskType type() {
        return TaskType.SEND_CHANNEL_SURVEY;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskSendChannelSurveyDto param) {
        ChannelSendCustomerDto data = JsonHelper.toObject(param.getData(), ChannelSendCustomerDto.class);
        if (data == null) {
            entity.setResponse("参数解析失败");
            userTaskService.failedTask(param.getTaskProgressId(), entity.getResponse());
            return ConsumerStatus.FAILED;
        }
        try {
            channelSendHelper.send(entity.getOrgId(), entity.getUserId(), param.getTaskProgressId(), param.getSurveyId(), param.getChannelId(), 100, data);
            userTaskService.updateTaskStatus(param.getTaskProgressId(), TaskStatus.RUNNING);
        } catch (Throwable e) {
            userTaskService.failedTask(param.getTaskProgressId(), e.getMessage());
            throw e;
        }
        return ConsumerStatus.SUCCESS;
    }


}
