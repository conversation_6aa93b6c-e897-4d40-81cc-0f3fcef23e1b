package cn.hanyi.cem.task.consumer.survey;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskSurveyTranslateDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.survey.core.constant.SurveyTranslatePage;
import cn.hanyi.survey.core.constant.survey.TranslateStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyLanguage;
import cn.hanyi.survey.core.entity.SurveyPersonalizedRemark;
import cn.hanyi.survey.core.repository.SurveyLanguageRepository;
import cn.hanyi.survey.core.repository.SurveyPersonalizedRemarkRepository;
import cn.hanyi.survey.service.SurveyService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.shade.com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.befun.auth.constant.AiPointRecordType;
import org.befun.auth.entity.OrganizationAiPointRecord;
import org.befun.auth.service.OrganizationAiPointRecordResponseService;
import org.befun.auth.service.OrganizationAiPointRecordService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.nlp.core.service.IModelService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SurveyTranslateConsumer implements ITaskConsumer<TaskSurveyTranslateDto> {

    private static final int THREAD_SIZE = Math.max(Runtime.getRuntime().availableProcessors(), 8);
    private static final float TRANSLATE_TEMPERATURE = 0.1F;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private SurveyLanguageRepository surveyLanguageRepository;

    @Autowired
    private OrganizationAiPointRecordResponseService aiPointRecordResponseService;

    @Autowired
    private SurveyPersonalizedRemarkRepository surveyPersonalizedRemarkRepository;

    @Autowired
    private IModelService iModelService;

    @Autowired
    private OrganizationAiPointRecordService aiPointRecordService;

    private final ExecutorService executorService = new ThreadPoolExecutor(
            THREAD_SIZE, THREAD_SIZE, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("translate-pool-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    private static final Map<String, String> languageMap = Map.ofEntries(
            Map.entry("zh-cn", "中文-简体"),
            Map.entry("zh-hk", "中文 - 繁體（香港）"),
            Map.entry("zh-tw", "中文 - 繁體（台湾）"),
            Map.entry("en", "英语"),
            Map.entry("es", "西班牙语"),
            Map.entry("ko", "韩语"),
            Map.entry("it", "意大利语"),
            Map.entry("ja", "日语"),
            Map.entry("pt", "葡萄牙语"),
            Map.entry("ru", "俄语"),
            Map.entry("th", "泰语"),
            Map.entry("de", "德语"),
            Map.entry("fr", "法语")
    );

    @Override
    public TaskType type() {
        return TaskType.SURVEY_TRANSLATE;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskSurveyTranslateDto param) {
        log.info("开始翻译问卷 sid: {}, languageId: {}", param.getSid(), param.getLanguageId());
        Survey survey = surveyService.requireSurvey(param.getSid());
        try {
            run(survey, param);
            log.info("翻译问卷结束 sid: {}, languageId: {}", param.getSid(), param.getLanguageId());
        } catch (Exception ex) {
            log.error("翻译问卷失败 sid: {}, languageId: {}, error: {}", param.getSid(),
                    param.getLanguageId(), ex.getMessage(), ex);
            return ConsumerStatus.FAILED;
        }
        return ConsumerStatus.SUCCESS;
    }

    public void run(Survey survey, TaskSurveyTranslateDto requestDto) {
        SurveyLanguage surveyLanguage = surveyLanguageRepository
                .findById(requestDto.getLanguageId())
                .orElseThrow(() -> new BadRequestException(
                        "language not exist, languageId:" + requestDto.getLanguageId()));

        Semaphore semaphore = new Semaphore(THREAD_SIZE);
        List<Future<?>> futures = new ArrayList<>();
        List<TaskSurveyTranslatedPageData> results = new CopyOnWriteArrayList<>();
        // 提前添加记录 避免多线程翻译时添加失败
        aiPointRecordService.getOrAddRecord(requestDto.getOrgId(), requestDto.getUserId(), AiPointRecordType.text_analysis, requestDto.getLanguageId());

        for (String page : requestDto.getPages()) {
            try {
                List<TaskSurveyLanguageGPTTranslateDto> dtos = extractTranslateUnits(survey,
                        requestDto, SurveyTranslatePage.valueOf(page));
                for (TaskSurveyLanguageGPTTranslateDto dto : dtos) {
                    if (dto.getText().isEmpty() || dto.getSourceLanguage().isEmpty() || dto
                            .getTargetLanguage().isEmpty()) {
                        continue;
                    }
                    futures.add(submitTranslateTask(semaphore, dto, results));
                }
            } catch (Exception ex) {
                log.error("处理页面失败 sid: {}, page: {}, error: {}", requestDto.getSid(), page, ex.getMessage(), ex);
            }
        }

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("翻译任务被中断", e);
            } catch (ExecutionException e) {
                throw new RuntimeException("翻译任务执行失败", e.getCause());
            }
        }

        if (!requestDto.getPages().isEmpty() && results.isEmpty()) {
            surveyLanguage.setStatus(TranslateStatus.FAILED);
        }

        applyTranslatedResults(surveyLanguage, results);
    }

    private Future<?> submitTranslateTask(Semaphore semaphore, TaskSurveyLanguageGPTTranslateDto dto,
            List<TaskSurveyTranslatedPageData> results) {
        return executorService.submit(() -> {
            try {
                semaphore.acquire();
                log.info("翻译文本：{}", dto.getText());
                aiPointRecordResponseService.addBySurveyTranslate(
                        dto.getOrgId(), dto.getUserId(), dto.getLanguageId(), dto.getPageCode(),
                        dto.getCost(), () -> translateByAI(dto, results));
            } catch (Exception ex) {
                log.error("翻译异常，文本: {}, error: {}", dto.getText(), ex.getMessage(), ex);
            } finally {
                semaphore.release();
            }
        });
    }

    private void translateByAI(TaskSurveyLanguageGPTTranslateDto dto, List<TaskSurveyTranslatedPageData> results) {
        String translatedText = iModelService.translate(
                dto.getText(), dto.getSourceLanguage(), dto.getTargetLanguage(),
                TRANSLATE_TEMPERATURE);
        TaskSurveyTranslatedPageData translatedPageData = new TaskSurveyTranslatedPageData(dto.getPageType(),
                dto.getQuestionCode(),
                translatedText, dto.getOriginText());
        results.add(translatedPageData);
    }

    private List<TaskSurveyLanguageGPTTranslateDto> extractTranslateUnits(Survey survey,
            TaskSurveyTranslateDto requestDto, SurveyTranslatePage page) {
        List<TaskSurveyLanguageGPTTranslateDto> result = new ArrayList<>();
        TaskSurveyLanguageGPTTranslateDto baseDto = buildBaseDto(survey, requestDto, page);

        try {
            switch (page) {
                case QUESTIONS -> {
                    Map<String, Object> questionMap = JsonHelper.toMap(requestDto.getQuestions());
                    questionMap.forEach(
                            (key, val) -> result.add(buildTranslateDto(baseDto, key, val)));
                }
                case PERSONALIZED_REMARK -> {
                    for (Long remarkId : requestDto.getRemarkIds()) {
                        surveyPersonalizedRemarkRepository.findById(remarkId).ifPresent(r -> {
                            if (r.getSurvey().getId().equals(survey.getId())) {
                                TaskSurveyLanguageGPTTranslateDto dto = new TaskSurveyLanguageGPTTranslateDto();
                                BeanUtils.copyProperties(baseDto, dto);
                                dto.setText(preProcessText(r.getText()));
                                dto.setOriginText(JsonHelper.toJson(r));
                                result.add(dto);
                            }
                        });

                    }
                }
                case TITLE, WELCOMING_REMARK, REAL_TITLE -> {
                    TaskSurveyLanguageGPTTranslateDto dto = new TaskSurveyLanguageGPTTranslateDto();
                    BeanUtils.copyProperties(baseDto, dto);
                    switch (page) {
                        case TITLE -> dto.setText(survey.getTitle());
                        case WELCOMING_REMARK -> dto
                                .setText(preProcessText(survey.getWelcomingRemark()));
                        case REAL_TITLE -> dto.setText(preProcessText(survey.getRealTitle()));
                    }
                    result.add(dto);
                }
                case CONCLUDING_REMARK, ABNORMAL_CONCLUDING_REMARK -> {
                    String remark = page == SurveyTranslatePage.CONCLUDING_REMARK
                            ? survey.getConcludingRemark()
                            : survey.getAbnormalConcludingRemark();
                    TaskSurveyLanguageGPTTranslateDto dto = new TaskSurveyLanguageGPTTranslateDto();
                    BeanUtils.copyProperties(baseDto, dto);
                    dto.setText(getTextFromRemark(remark));
                    dto.setOriginText(remark);
                    result.add(dto);
                }
                default -> throw new BadRequestException("Unsupported page type: " + page);
            }
        } catch (Exception e) {
            log.error("提取翻译单元失败，page: {}, error: {}", page, e.getMessage(), e);
        }

        return result;
    }

    private TaskSurveyLanguageGPTTranslateDto buildBaseDto(Survey survey,
            TaskSurveyTranslateDto requestDto, SurveyTranslatePage page) {
        TaskSurveyLanguageGPTTranslateDto dto = new TaskSurveyLanguageGPTTranslateDto();
        dto.setSid(requestDto.getSid());
        dto.setOrgId(requestDto.getOrgId());
        dto.setUserId(requestDto.getUserId());
        dto.setLanguageId(requestDto.getLanguageId());
        dto.setSourceLanguage(languageMap.get(survey.getLanguage()));
        dto.setTargetLanguage(languageMap.get(requestDto.getLanguage()));
        dto.setPageType(page);
        dto.setCost(10);
        return dto;
    }

    private TaskSurveyLanguageGPTTranslateDto buildTranslateDto(
            TaskSurveyLanguageGPTTranslateDto baseDto, String code, Object value) {
        TaskSurveyLanguageGPTTranslateDto dto = new TaskSurveyLanguageGPTTranslateDto();
        BeanUtils.copyProperties(baseDto, dto);
        dto.setQuestionCode(code);
        dto.setText(preProcessText(value));
        return dto;
    }

    private void applyTranslatedResults(SurveyLanguage language,
            List<TaskSurveyTranslatedPageData> results) {

        Map<String, Object> questionMap = Optional.ofNullable(language.getQuestions())
                .map(JsonHelper::toMap).orElse(new HashMap<>());
        List<Map<String, Object>> personalizedRemarks = Optional
                .ofNullable(language.getPersonalizedRemarks())
                .map(JsonHelper::toListMap).orElse(new ArrayList<>());

        for (TaskSurveyTranslatedPageData data : results) {
            if (data.getTranslatedText() == null) {
                log.error("文本翻译结果为空:{}", data.getOriginText());
            }
            Object translated = afterProcessText(data.getTranslatedText());
            try {
                switch (data.getPageType()) {
                    case QUESTIONS -> questionMap.put(data.getQuestionCode(), translated);
                    case PERSONALIZED_REMARK -> applyPersonalizedRemark(personalizedRemarks, data);
                    case TITLE -> language.setTitle(String.valueOf(translated));
                    case REAL_TITLE -> language.setRealTitle(String.valueOf(translated));
                    case WELCOMING_REMARK -> language
                            .setWelcomingRemark(String.valueOf(translated));
                    case CONCLUDING_REMARK -> language.setConcludingRemark(
                            updateJsonText(data.getOriginText(), String.valueOf(translated)));
                    case ABNORMAL_CONCLUDING_REMARK -> language.setAbnormalConcludingRemark(
                            updateJsonText(data.getOriginText(), String.valueOf(translated)));
                }
            } catch (Exception ex) {
                log.error("apply translated results error :{}, {}", data.getTranslatedText(),
                        ex.getMessage());
            }
        }

        language.setQuestions(JsonHelper.toJson(questionMap));
        language.setPersonalizedRemarks(JsonHelper.toJson(personalizedRemarks));
        language.setStatus(TranslateStatus.TRANSLATED);
        surveyLanguageRepository.save(language);
    }

    private void applyPersonalizedRemark(List<Map<String, Object>> remarks,
            TaskSurveyTranslatedPageData data) {
        SurveyPersonalizedRemark origin = JsonHelper
                .toObject(data.getOriginText(), SurveyPersonalizedRemark.class);
        boolean updated = false;
        for (Map<String, Object> remark : remarks) {
            if (Objects.equals(remark.get("id"), origin.getId())) {
                remark.put("text", afterProcessText(data.getTranslatedText()));
                updated = true;
                break;
            }
        }
        if (!updated) {
            origin.setText(String.valueOf(afterProcessText(data.getTranslatedText())));
            remarks.add(JsonHelper.toMap(origin));
        }
    }

    private String updateJsonText(String originJson, String newText) {
        try {
            Map<String, Object> map = JsonHelper.toMap(originJson);
            map.put("text", newText);
            return JsonHelper.toJson(map);
        } catch (Exception ex) {
            log.warn("更新翻译文本失败: {}", ex.getMessage(), ex);
            return originJson;
        }
    }

    private String getTextFromRemark(String remark) {
        if (remark == null || remark.isEmpty()) {
            return "";
        }
        try {
            Map<String, Object> remarkMap = JsonHelper.toMap(remark);
            return preProcessText(String.valueOf(remarkMap.getOrDefault("text", "")));
        } catch (Exception ex) {
            log.warn("解析 remark.text 失败: {}", ex.getMessage());
            return "";
        }
    }

    private String preProcessText(Object text) {
        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("text", text);
        return JsonHelper.toJson(jsonMap);
    }

    private Object afterProcessText(String text) {
        try {
            if (text == null) {
                return null;
            }
            Map<String, Object> jsonMap = JsonHelper.toMap(text);
            return jsonMap.get("text");
        } catch (Exception ex) {
            log.info("process text error: {}",text);
            return null;
        }
    }


}
