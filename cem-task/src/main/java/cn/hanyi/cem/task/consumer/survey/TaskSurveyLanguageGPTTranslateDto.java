package cn.hanyi.cem.task.consumer.survey;

import cn.hanyi.cem.core.dto.task.TaskSurveyTranslateDto;
import cn.hanyi.survey.core.constant.SurveyTranslatePage;
import cn.hanyi.survey.dto.SurveyLanguageTranslateRequestDto;
import cn.hanyi.survey.dto.SurveyLanguageTranslateTaskDto;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class TaskSurveyLanguageGPTTranslateDto extends TaskSurveyTranslateDto {
    private Integer cost;
    private String text;
    private String questionCode;
    private Long personalizedRemarkId;
    private SurveyTranslatePage pageType;
    private String sourceLanguage;
    private String targetLanguage;
    private String originText;
    private String pageCode;
    private Boolean isEnd;
    private TaskSurveyTranslatedPageData context;

}
