package cn.hanyi.cem.task.consumer.warning;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskNotifyBaseDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyInboxDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyOutWorkerDto;
import cn.hanyi.cem.core.dto.task.TaskNotifyWarningJourneyDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.ctm.entity.journey.ElementEventStatBase;
import cn.hanyi.ctm.entity.journey.ExperienceIndicatorBase;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.repository.JourneyWarningPublishRepository;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.JourneyWarningService;
import cn.hanyi.ctm.service.data.EventStatDataService;
import cn.hanyi.ctm.service.data.IndicatorDataService;
import cn.hanyi.ctm.service.journey.elements.scene.JourneyScenePublishService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.extension.constant.InboxMessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


@Slf4j
@Component
public class JourneyNotifyTaskConsumer implements ITaskConsumer<TaskNotifyWarningJourneyDto> {

    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private WorkerProperties workerProperties;

    @Autowired
    private JourneyScenePublishService journeyScenePublishService;

    @Autowired
    private JourneyMapService journeyMapService;

    @Autowired
    private JourneyWarningPublishRepository journeyWarningPublishRepository;


    @Autowired
    private IndicatorDataService indicatorService;

    @Autowired
    private EventStatDataService eventStatService;

    @Autowired
    private JourneyWarningService journeyWarningService;


    @Override
    public TaskType type() {
        return TaskType.WARNING_NOTIFY_JOURNEY;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskNotifyWarningJourneyDto dto) {
        /**
         * 查询客户旅程设置的预警记录
         * 生成通知的task
         */
        journeyWarningPublishRepository.findById(dto.getJourneyWarningId()).ifPresent(journeyWarningPublish -> {
            var journeyPublish = journeyScenePublishService.get(journeyWarningPublish.getJourneyId());
            var receiver = journeyWarningPublish.getReceiver();
            var journeyMap = journeyMapService.get(journeyWarningPublish.getJourneyMapId());

            if (journeyPublish != null && journeyMap != null) {
                Optional.ofNullable(receiver).ifPresent(rcr -> {
                    var roleIds = new HashSet<>(CollectionUtils.emptyIfNull(rcr.getRoleIds()));
                    var userIds = new HashSet<>(CollectionUtils.emptyIfNull(rcr.getUserIds()));
                    var notifyType = CollectionUtils.emptyIfNull(rcr.getNotifyChannel()).toArray(new NotificationType[0]);
                    var value = dto.getValue();

                    String warningTypeText = "";
                    String methodText = "";
                    String warningTitle = "";
                    String valueText = "";
                    String warningValue = "";
                    switch (journeyWarningPublish.getRelationType()) {
                        case experience_indicator:

                            warningTypeText = "平均值";
                            ExperienceIndicatorBase indicator = indicatorService.getEntity(journeyWarningPublish.getRelationId(), true);
                            if (indicator == null) {
                                return;
                            }
                            warningTitle = indicator.getIndicatorName();
                            String calculatingMethod = indicator.getCalculatingMethod() == null ? "" : indicator.getCalculatingMethod().toLowerCase();
                            switch (calculatingMethod) {
                                case "average":
                                    methodText = "平均值";
                                    valueText = formatDouble(value, false);//String.format("%.2f", value);
                                    warningValue = formatDouble(journeyWarningPublish.getWarningValue(), false);//String.valueOf(journeyWarningPublish.getWarningValue());
                                    break;
                                case "weightavg":
                                    methodText = "加权平均值";
                                    valueText = formatDouble(value, false);//String.format("%.2f", value);
                                    warningValue = formatDouble(journeyWarningPublish.getWarningValue(), false);//String.valueOf(journeyWarningPublish.getWarningValue());
                                    break;
                                case "nps":
                                    methodText = "净满意度";
                                    valueText = formatDouble(value, true);//String.format("%.2f", value * 100) + "%";
                                    warningValue = formatDouble(journeyWarningPublish.getWarningValue(), true);//String.valueOf(journeyWarningPublish.getWarningValue() * 100) + "%";
                                    break;
                                case "nss":
                                    methodText = "净推荐度";
                                    valueText = formatDouble(value, true);//String.format("%.2f", value * 100) + "%";
                                    warningValue = formatDouble(journeyWarningPublish.getWarningValue(), true);//String.valueOf(journeyWarningPublish.getWarningValue() * 100) + "%";
                                    break;
                                case "percent":
                                    methodText = "选项占比";
                                    valueText = formatDouble(value, true);//String.format("%.2f", value * 100) + "%";
                                    warningValue = formatDouble(journeyWarningPublish.getWarningValue(), true);//String.valueOf(journeyWarningPublish.getWarningValue() * 100) + "%";
                                    break;
                            }

                            break;
                        case event_stat:
                            warningTypeText = "触发总数";
                            ElementEventStatBase eventStat = eventStatService.getEntity(journeyWarningPublish.getRelationId(), true);
                            if (eventStat == null) {
                                return;
                            }

                            switch (eventStat.getStatType()) {
                                case 1:
                                    warningTypeText = "触发总数";
                                    break;
                                case 2:
                                    warningTypeText = "待处理数";
                                    break;
                                default:
                                    warningTypeText = "";
                                    break;
                            }

                            Map<Long, String> eventRuleMap = journeyWarningService.getEventRuleTitleByIds(Collections.singleton(eventStat.getEventRuleId()));
                            warningTitle = eventRuleMap.getOrDefault(eventStat.getEventRuleId(), "");
                            methodText = warningTypeText;
                            valueText = String.format("%d", value.intValue());
                            warningValue = String.valueOf(journeyWarningPublish.getWarningValue().intValue());
                            break;
                        default:
                            log.error("暂不支持relation type: {}", journeyWarningPublish.getRelationType());
                            return;
                    }

                    // 需要单独处理 0 表示全部
                    Integer warningRange = journeyWarningPublish.getWarningRange();
                    String range = warningRange == 0 ? "全部" : String.format("%d", warningRange);
                    // 指标名称+监测范围+计算方法+当前值+（过低/过高）
                    String inboxJourneyTitle = String.format("指标预警: %s (最近%s%s) 的%s%s（%s）",
                            warningTitle,
                            range,
                            journeyWarningPublish.getWarningFrequency().text,
                            methodText,
                            valueText,
                            journeyWarningPublish.getWarningCompare().label
                    );

                    String JourneyWarningTitle = String.format("%s (最近%s%s) 的%s为%s，(%s)设定值%s",
                            warningTitle,
                            range,
                            journeyWarningPublish.getWarningFrequency().text,
                            methodText,
                            valueText,
                            journeyWarningPublish.getWarningCompare().text,
                            warningValue
                    );

                    var targetUrl = StringUtils.isNotEmpty(workerProperties.getEvent().getWarning().getJourneyUrl()) ? String.format("%s%s", workerProperties.getEvent().getWarning().getJourneyUrl(), journeyWarningPublish.getJourneyMapId()) : "";

                    String warningDateRange = journeyWarningService.getWarningDateRange(journeyWarningPublish.getWarningFrequency(), journeyWarningPublish.getWarningRange());
                    String finalWarningTypeText = warningTypeText;
                    String finalValueText = valueText;
                    String finalWarningTitle = warningTitle;
                    String finalWarningValue = warningValue;
                    // sms      ${targetTruename}，您好！您收到一条指标预警：${indicatorName}的值为${currentValue}(${indicatorCompareLabel})，请尽快登录体验家XM处理！
                    // wechat   ${warningTitle} ${warningTime}
                    // wx_work  ${JourneyWarningTitle}
                    // email    ${targetTruename} ${warningLevelSimple} ${warningTitle} ${currentValue} ${indicatorCompareLabel} ${url} ${indicatorName} ${warningRange} ${warningFrequency} ${warningTypeText} ${currentValue} ${indicatorCompareLabel} ${indicatorCompareText} ${warningValue} ${warningTime} ${journeyName}
                    Map<String, Object> notifyParams = new HashMap<>() {
                        {
                            put("warningLevelSimple", journeyWarningPublish.getRelationType().text);
                            put("warningTitle", finalWarningTitle);
                            put("JourneyWarningTitle", JourneyWarningTitle);
                            put("indicatorCompareLabel", journeyWarningPublish.getWarningCompare().label);
                            put("indicatorCompareText", journeyWarningPublish.getWarningCompare().text);
                            put("indicatorName", finalWarningTitle);
                            put("warningRange", range);
                            put("warningFrequency", journeyWarningPublish.getWarningFrequency().text);
                            put("warningTypeText", finalWarningTypeText);
                            put("currentValue", finalValueText);
                            put("warningValue", finalWarningValue);
                            put("url", targetUrl);
                            put("journeyName", journeyMap.getTitle());
                            put("warningTime", warningDateRange);
                        }
                    };
                    // 客户旅程通知只有角色没有部门
                    // 部门为null查询所有部门
                    // 客户旅程没有延迟通知  都是在指定事件计算完成后就推送
                    var inboxDto = buildBaseParam(TaskNotifyInboxDto.class, journeyWarningPublish.getOrgId(), null, roleIds, userIds, journeyWarningPublish.getId());
                    inboxDto.setResourceType(ResourcePermissionType.JOURNEY.name());
                    inboxDto.setType(InboxMessageType.JOURNEY.name());
                    inboxDto.setTargetUrl(targetUrl);
                    inboxDto.setTitle(inboxJourneyTitle);

                    CompletableFuture.runAsync(() -> taskProducerHelper.notifyInbox(inboxDto, entity.getOrgId(), entity.getUserId(), dto.getJourneyWarningId(), null));


                    var app = workerProperties.getNotify().getApp();
                    var types = receiver.getNotifyChannel();
                    var template = workerProperties.getNotify().getJourney();
                    var outWorker = buildBaseParam(TaskNotifyOutWorkerDto.class, journeyWarningPublish.getOrgId(), null, roleIds, userIds, journeyWarningPublish.getId());

                    outWorker.setResourceType(ResourcePermissionType.JOURNEY.name());
                    outWorker.setTypes(types.stream().map(Enum::name).collect(Collectors.toList()));
                    outWorker.setApp(app);
                    outWorker.setTemplate(template);
                    outWorker.setParams(notifyParams);

                    CompletableFuture.runAsync(() -> taskProducerHelper.notifyOutWork(outWorker, entity.getOrgId(), entity.getUserId(), dto.getJourneyWarningId(), null));

                });
            }
        });

        return ConsumerStatus.SUCCESS;
    }

    public static String formatDouble(Double value, boolean percent) {
        if (value == null || value == 0) {
            return percent ? "0%" : "0";
        }
        if (percent) {
            return (Math.round(value * 10000) / 100.0) + "%";
        } else {
            return (Math.round(value * 100) / 100.0) + "";
        }
    }

    @SneakyThrows
    public <W extends TaskNotifyBaseDto> W buildBaseParam(Class<W> clazz, Long orgId, Long departmentId, Set<Long> roleIds, Set<Long> userIds, Long sourceId) {
        W param = clazz.getDeclaredConstructor().newInstance();
        param.setOrgId(orgId);
        param.setDepartmentId(departmentId);
        param.setRoleIds(roleIds);
        param.setUserIds(userIds);
        param.setSourceId(sourceId);
        return param;
    }

}

