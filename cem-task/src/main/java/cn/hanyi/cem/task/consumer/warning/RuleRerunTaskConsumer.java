package cn.hanyi.cem.task.consumer.warning;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskNotifyInboxDto;
import cn.hanyi.cem.core.dto.task.TaskProcessWarningDto;
import cn.hanyi.cem.core.dto.task.TaskWebHookDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.core.properties.WorkerProperties;
import cn.hanyi.cem.event.consumer.response.WarningBaseConsumer;
import cn.hanyi.cem.event.consumer.utils.EventConsumerUtils;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.ctm.constant.*;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import cn.hanyi.ctm.constant.connector.ConnectorPushType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.dto.connector.ConnectorParamsDto;
import cn.hanyi.ctm.dto.event.EventReceiverDto;
import cn.hanyi.ctm.dto.event.EventWarningDto;
import cn.hanyi.ctm.dto.event.TaskEventWarningDto;
import cn.hanyi.ctm.dto.event.WarningRerunDto;
import cn.hanyi.ctm.dto.survey.SurveyResponseCellMessageRuleDto;
import cn.hanyi.ctm.dto.task.DataWarningDto;
import cn.hanyi.ctm.dto.task.DataWarningRuleDto;
import cn.hanyi.ctm.dto.task.ResponseTaskDetailDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.repository.EventRuleRelationRepository;
import cn.hanyi.ctm.repository.PushRepository;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import cn.hanyi.ctm.service.EventActionService;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.entity.UserDto;
import org.befun.auth.exception.AmountNotEnoughException;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationAiPointRecordResponseService;
import org.befun.auth.service.RoleService;
import org.befun.auth.service.UserService;
import org.befun.auth.service.UserTaskService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.InboxMessageType;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.stream.Collectors;


@Slf4j
@Component
public class RuleRerunTaskConsumer extends WarningBaseConsumer implements ITaskConsumer<TaskProcessWarningDto> {

    private final static String TASK_KEY = "task:warning:rerun:";
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private EventRuleRelationRepository eventRuleRelationRepository;

    @Autowired
    private SurveyResponseRepository responseRepository;

    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;
    @Autowired
    private EventRepository eventRepository;
    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private ExecutorService executorService;
    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private EventConsumerUtils eventConsumerUtils;

    @Autowired
    private SurveyBaseEntityService surveyBaseEntityService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private TaskProducerHelper taskProducerHelper;

    @Autowired
    private WorkerProperties workerProperties;

    @Autowired
    private RoleService roleService;

    @Autowired
    private EventActionService actionService;

    @Autowired
    private PushRepository pushRepository;
    @Autowired
    private ConnectorConsumerService connectorConsumerService;

    @Autowired
    private OrganizationAiPointRecordResponseService organizationAiPointRecordResponseService;

    @Autowired
    private UserService userService;

    @Override
    public TaskType type() {
        return TaskType.WARNING_RULE_RERUN;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskProcessWarningDto dto) {
        log.info("warning rerun task: {}", dto.getRuleId());
        EventMonitorRules rule = eventMonitorRulesRepository.findById(dto.getRuleId()).orElse(null);
        if (rule == null) {
            log.info("warning rerun task: {} rule not found", dto.getRuleId());
            return ConsumerStatus.CANCELED;
        }
        if (rule.getStatus() == EventMonitorStatus.CLOSE) {
            log.info("warning rerun task: {} rule not open", dto.getRuleId());
            return ConsumerStatus.CANCELED;
        }
        if (lock(dto.getRuleId())) {
            log.info("warning rerunning skip: {}", dto.getRuleId());
            return ConsumerStatus.CANCELED;
        }
        try {
            rerun(dto, rule);
        } catch (Exception e) {
            log.error("warning rerun task error: {}", dto.getRuleId(), e);
        } finally {
            unlock(dto.getRuleId());
        }
        log.info("warning rerun task: {} finished", dto.getRuleId());
        return ConsumerStatus.SUCCESS;
    }

    private void rerun(TaskProcessWarningDto dto, EventMonitorRules rule) {
        log.info("warning:{} rerun task: {}", dto.getRuleId(), dto.getTaskProgressId());
        TaskProgress taskProgress = userTaskService.get(dto.getTaskProgressId());
        if (taskProgress == null) {
            log.warn("warning:{} rerun task not found: {}", dto.getRuleId(), dto.getTaskProgressId());
            return;
        }
        List<TaskEventWarningDto> list = JsonHelper.toList(taskProgress.getParams(), TaskEventWarningDto.class);
        if (CollectionUtils.isEmpty(list)) {
            log.warn("warning:{} rerun task info is missing: {}", dto.getRuleId(), dto.getTaskProgressId());
            return;
        }
        TaskEventWarningDto taskEventWarningDto = list.get(0);
        WarningRerunDto rerunDto = taskEventWarningDto.getWarningRerunDto();
        if (rerunDto == null) {
            log.warn("warning:{} rerun info is missing: {}", dto.getRuleId(), dto.getTaskProgressId());
            return;
        }
        Long minId = rerunDto.getMinId();
        Long maxId = rerunDto.getMaxId();
        String dataScope = rerunDto.getDataScope();
        if (minId == null || maxId == null || minId > maxId) {
            log.warn("warning:{} rerun params is error: {}", dto.getRuleId(), JsonHelper.toJson(rerunDto));
            return;
        }
        int cost = eventRuleService.calculateRuleCost(rule);
        boolean forceRerun = "all".equals(dataScope) || "dateRange".equals(dataScope);
        Long surveyId = rule.getSurveyId();
        ResponseStatus status = ResponseStatus.FINAL_SUBMIT;
        Long nextId = minId;
        try {
            while (nextId <= maxId) {
                List<SurveyResponse> data = responseRepository.findBySurveyIdAndStatusAndIdBetween(surveyId, status, nextId, maxId,
                        PageRequest.of(0, 500, Sort.by(Sort.Direction.ASC, "id")));
                if (CollectionUtils.isNotEmpty(data)) {
                    userTaskService.appendTaskTotalSize(dto.getTaskProgressId(), data.size());
                    for (SurveyResponse response : data) {
                        // 如果抛出异常的是余额不足，直接停止任务，
                        // 如果是其他异常，则会忽略异常，运行下一个
                        runEvent(response, rule, rerunDto, taskProgress.getId(), cost, forceRerun);
                    }
                    if (data.size() == 500) {
                        nextId = data.get(data.size() - 1).getId() + 1;
                        continue;
                    }
                }
                break;
            }
            userTaskService.successTask(dto.getTaskProgressId(), null);
        } catch (Throwable e) {
            log.error("warning:{} rerun error", dto.getRuleId(), e);
            userTaskService.failedTask(dto.getTaskProgressId(), e.getMessage());
        }
    }

    public void runEvent(SurveyResponse response, EventMonitorRules rule, WarningRerunDto dto, Long taskProgressId, int cost, boolean forceRerun) {
        HashMap<EventMonitorRules, Set<String>> ruleFalseCauses = new HashMap<>();
        try {
            organizationAiPointRecordResponseService.addByWarningRerun(rule.getOrgId(), rule.getUserId(), response.getId(), rule.getId(), cost, forceRerun,
                    () -> {
                        boolean hit = !triggerWarning(response.getSurveyId(), response.getId(), List.of(rule), ruleFalseCauses::putAll).isEmpty();
                        updateEvent(response, rule, ruleFalseCauses, hit, e -> updateEventCompleted(hit, response, rule, e, dto));
                    });
            userTaskService.appendTaskSuccessSize(taskProgressId, 1);
        } catch (AmountNotEnoughException e) {
            // 余额不足 close rule
            eventRuleService.closeRules(Map.of(rule.getId(), cost));
            userTaskService.appendTaskFailedSize(taskProgressId, 1);
            log.warn("warning rerun response:{} task error ai point not enough {}", response.getId(), rule.getId());
            throw e; // 异常重新抛出
        } catch (Throwable e) {
            userTaskService.appendTaskFailedSize(taskProgressId, 1);
            log.error("warning rerun response:{} task error", response.getId(), e);
        }
    }

    private void updateEventCompleted(Boolean hit, SurveyResponse response, EventMonitorRules rule, Event event, WarningRerunDto rerunDto) {
        updateEventRelation(hit, response.getId(), rule.getId(), event.getId());
        if (hit) {
            event.setStatus(EventStatusType.WAIT);
            Optional.ofNullable(rerunDto).ifPresent(w -> {
                Optional.ofNullable(w.getStatus()).ifPresent(i -> {
                    if (i != EventStatusType.NONE) {
                        event.setStatus(i);
                    }
                });
                Optional.ofNullable(w.getNotify()).ifPresent(n -> {
                    if (n) {
                        reNotify(event, rule);
                    }
                });
            });
            actionService.addActionSingle(
                    new SimpleUser(-1L, "系统", null, null, null, null),
                    EventActionType.ACTION_TYPE_WARNING,
                    "触发预警规则：" + event.getWarningTitle().replace(";", "、"),
                    EventActionStatusType.SUCCESS,
                    event);
        }
        eventRepository.save(event);
    }

    private void updateEventRelation(Boolean hit, Long responseId, Long ruleId, Long eventId) {
        List<EventRuleRelation> eventRuleRelations = eventRuleRelationRepository.findAll((r, q, b) -> b.and(
                b.equal(r.get("responseId"), responseId),
                b.equal(r.get("ruleId"), ruleId)
        ));
        if (hit) {
            if (eventRuleRelations.isEmpty()) {
                EventRuleRelation eventRuleRelation = new EventRuleRelation();
                eventRuleRelation.setEventId(eventId);
                eventRuleRelation.setRuleId(ruleId);
                eventRuleRelation.setResponseId(responseId);
                eventRuleRelationRepository.save(eventRuleRelation);
            }
        } else {
            eventRuleRelationRepository.deleteAll(eventRuleRelations);
        }
    }

    private void reNotify(Event warning, EventMonitorRules rule) {
        log.info("warning:{} reNotify: {}", warning.getId(), rule.getId());
        Set<Long> roleIds = rule.getReceiver().stream().map(EventReceiverDto::getRoleIds).flatMap(List::stream).collect(Collectors.toSet());
        Set<Long> userIds = rule.getReceiver().stream().map(EventReceiverDto::getUserIds).flatMap(List::stream).collect(Collectors.toSet());
        List<SimpleUser> users = eventConsumerUtils.getNotifyUsers(warning.getOrgId(), warning.getDepartmentId(), roleIds, userIds);

        if (CollectionUtils.isNotEmpty(users)) {
            Set<Long> notifyIds = users.stream().map(SimpleUser::getId).collect(Collectors.toSet());
            String targetUrl = StringUtils.isNotEmpty(workerProperties.getEvent().getWarning().getEventUrl()) ? String.format("%s%s", workerProperties.getEvent().getWarning().getEventUrl(), warning.getId()) : "";
            TaskNotifyInboxDto inboxDto = eventConsumerUtils.buildBaseParam(TaskNotifyInboxDto.class, warning.getOrgId(), null, null, notifyIds, warning.getId());
            inboxDto.setResourceType(ResourcePermissionType.EVENT.name());
            inboxDto.setSourceId(warning.getId());
            inboxDto.setType(InboxMessageType.WARNING.name());
            inboxDto.setTargetUrl(targetUrl);
            inboxDto.setTitle(String.format("%s:%s", warning.getWarningLevel().getText(), warning.getWarningTitle()));
            taskProducerHelper.notifyInbox(inboxDto, rule.getOrgId(), rule.getUserId(), warning.getResponseId(), null);

            actionLog(warning, roleIds, userIds);
        }

        if (rule.getNotifyConsumer()) {
            AtomicReference<DataWarningDto> dataWarningDto = new AtomicReference<>();
            // 找到预警规则
            List<String> ruleUsersEmail = new ArrayList<>();

            // 从规则中获取预警名称和通知人员
            rule.getReceiver().forEach(receiver -> {
                receiver.getRoleIds().forEach(roleId -> {
                    ruleUsersEmail.addAll(userService.getAllUsersInRole(rule.getOrgId(), roleId)
                            .stream()
                            .map(UserDto::getEmail)
                            .distinct()
                            .collect(Collectors.toList()));
                });
            });

            dataWarningDto.set(new DataWarningDto(
                    rule.getId(),
                    ruleUsersEmail.stream().distinct().collect(Collectors.toList()),
                    List.of(new DataWarningRuleDto(rule.getTitle(), rule.getLevel().getText())),
                    null,
                    null,
                    warning.getStatus().desc,
                    actionService.getRemarksByEventId(warning.getId())
            ));

            Set<Long> ruleSet = new HashSet<>();
            ruleSet.add(rule.getId());
            List<Connector> connectors = connectorConsumerService.webhookConnector(
                    ruleSet,
                    ConnectorPushType.WARNING,
                    ConnectorPushCondition.WARNING
            ).stream().filter(c -> c.getType() == ConnectorType.WEBHOOK).collect(Collectors.toList());
            if (!connectors.isEmpty()) {

                connectors.stream().filter(c -> ConnectorProviderType.WEBHOOK.equals(c.getProviderType())).forEach(connector -> {
                    ConnectorConsumer connectorConsumer = connectorConsumerService.getConsumerByConnectorAndRelationId(connector, rule.getId());
                    Survey survey = surveyBaseEntityService.get(Survey.class, rule.getSurveyId());
                    SurveyResponse response = surveyBaseEntityService.get(SurveyResponse.class, warning.getResponseId());
                    ResponseTaskDetailDto sendData = eventConsumerUtils.buildResponseDetailDto(survey, response, ResponseTaskDetailDto.class);
                    sendData.setDataWarning(dataWarningDto.get());
                    List<ConnectorParamsDto> params = connectorConsumer.getParams();
                    Map<String, Object> pushContent = JsonHelper.toMap(sendData);
                    Map parameters = (Map) pushContent.getOrDefault("parameters", new HashMap<>());

                    if (params != null && !params.isEmpty()) {
                        params.forEach(param -> {
                            if (ConnectorParamsType.Number.equals(param.getType())) {
                                parameters.put(param.getName(), param.getValue());
                            } else {
                                parameters.put(param.getName(), String.valueOf(param.getValue()));
                            }
                        });
                    }

                    if (!parameters.isEmpty()) {
                        pushContent.put("parameters", parameters);
                    }

                    var url = connector.getGateway();
                    Push push = new Push();
                    push.setOrgId(rule.getOrgId());
                    push.setName(connector.getName());
                    push.setConnector(connector);
                    push.setType(ConnectorType.WEBHOOK);
                    push.setAddress(url);
                    push.setContent(JsonHelper.toJson(pushContent));
                    push.setStatus(cn.hanyi.ctm.constant.PushStatus.FAILED);
                    pushRepository.save(push);
                    taskProducerHelper.addTask(true, true, false, TaskType.WEBHOOK, TaskType.WEBHOOK.group, rule.getId(), new TaskWebHookDto(push.getId()), null);
                    String webhookName = connectors.stream().filter(c -> ConnectorProviderType.WEBHOOK.equals(c.getProviderType())).map(x -> "webhook-" + x.getName()).collect(Collectors.joining("、"));
                    actionService.addActionSingle(
                            new SimpleUser(null, "系统", null, null, null, null),
                            EventActionType.ACTION_TYPE_NOTICE,
                            "已推送：" + webhookName,
                            EventActionStatusType.SUCCESS,
                            warning
                    );
                });
            }
        }
    }

    private String key(Long warningId) {
        return TASK_KEY + warningId;
    }

    private Boolean lock(Long warningId) {
        return !redisTemplate.opsForValue().setIfAbsent(key(warningId), "1", Duration.ofDays(3));
    }

    public void unlock(Long warningId) {
        redisTemplate.delete(key(warningId));
    }

    public void updateEvent(SurveyResponse response, EventMonitorRules rule, HashMap<EventMonitorRules, Set<String>> ruleFalseCauses, Boolean hit, Consumer<Event> eventConsumer) {
        eventRepository.findOneBySurveyIdAndResponseId(response.getSurveyId(), response.getId()).ifPresentOrElse(
                event -> {
                    List<EventWarningDto> eventWarningDtos = event.getWarnings();
                    List<EventWarningDto> newEventWarningDtos = new ArrayList<>();
                    EventWarningDto findWarning = null;
                    for (EventWarningDto w : eventWarningDtos) {
                        if (w.getRuleId() != null && w.getRuleId() > 0) {
                            // 不是这个规则，或者 是这个规则，但是命中了
                            boolean isThisRule = w.getRuleId().equals(rule.getId());
                            if (isThisRule && hit) {
                                findWarning = w;
                                newEventWarningDtos.add(w);
                            } else if (!isThisRule) {
                                newEventWarningDtos.add(w);
                            }
                        }
                    }
                    if (findWarning == null && hit) {
                        newEventWarningDtos.add(new EventWarningDto(response.getId(), rule.getId()));
                    }
                    if (newEventWarningDtos.isEmpty()) {
                        newEventWarningDtos.add(new EventWarningDto(response.getId(), 0L));
                    }
                    event.setWarnings(newEventWarningDtos);

                    AtomicReference<EventWarningType> level = new AtomicReference<>(EventWarningType.NONE);
                    String title = "";
                    List<String> tags = new ArrayList<>();
                    List<Long> ruleIds = newEventWarningDtos.stream().map(EventWarningDto::getRuleId).filter(ruleId -> !ruleId.equals(0L)).collect(Collectors.toList());
                    List<EventMonitorRules> eventMonitorRules = eventMonitorRulesRepository.findAllById(ruleIds);
                    if (!eventMonitorRules.isEmpty()) {
                        eventMonitorRules.forEach(i -> {
                            if (CollectionUtils.isNotEmpty(i.getTags())) {
                                tags.addAll(i.getTags());
                            }
                        });
                        eventMonitorRules.sort(Comparator.comparing(EventMonitorRules::getLevel));
                        title = eventMonitorRules.stream().map(EventMonitorRules::getTitle).collect(Collectors.joining(";"));
                        level.set(eventMonitorRules.get(0).getLevel());
                    } else {
                        event.setStatus(EventStatusType.NONE);
                    }

                    List<SurveyResponseCellMessageRuleDto> newQuestions = new ArrayList<>();
                    event.getQuestions().forEach(q -> {
                        newQuestions.add(q);
                        if (rule.getQuestionIds().contains(q.getQuestionId())) {
                            if (hit) {
                                q.setEventMonitorRules(rule);
                            } else {
                                q.setEventMonitorRules(null);
                            }
                        }
                    });
                    event.setQuestions(newQuestions);
                    event.setWarningLevel(level.get());
                    event.setWarningTitle(title);
                    if (tags.isEmpty()) {
                        event.setTags(null);
                    } else {
                        event.setTags(String.join("/", tags));
                    }
                    eventConsumer.accept(event);
                },
                () -> eventConsumer.accept(
                        saveEvent(
                                surveyBaseEntityService.get(Survey.class, response.getSurveyId()),
                                hit ? List.of(rule) : List.of(),
                                ruleFalseCauses,
                                List.of(rule),
                                surveyBaseEntityService.get(SurveyResponse.class, response.getId()),
                                eventConsumerUtils.getCustomerFromResponse(response)
                        )
                )
        );
    }

}

