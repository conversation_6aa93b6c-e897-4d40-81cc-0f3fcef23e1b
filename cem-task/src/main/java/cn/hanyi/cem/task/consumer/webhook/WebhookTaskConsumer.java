package cn.hanyi.cem.task.consumer.webhook;

import cn.hanyi.cem.compat.webhook.IWebhookCompat;
import cn.hanyi.cem.compat.webhook.IWebhookCompatInfo;
import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskWebHookDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.utils.SignGenerator;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import cn.hanyi.ctm.constant.PushStatus;
import cn.hanyi.ctm.entity.Push;
import cn.hanyi.ctm.repository.PushRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.fluent.Content;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.befun.auth.constant.FeatureAction;
import org.befun.auth.service.FeatureActionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
@EnableRetry
public class WebhookTaskConsumer implements ITaskConsumer<TaskWebHookDto> {

    @Autowired
    private PushRepository pushRepository;

    @Autowired
    private WebhookTaskConsumer self;

    @Autowired(required = false)
    private IWebhookCompat webhookCompat;

    @Override
    public TaskType type() {
        return TaskType.WEBHOOK;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskWebHookDto param) {
        Push push = pushRepository.findById(param.getPushId()).orElse(null);

        if (push == null) {
            entity.setResponse(String.format("pushId=%d, 不存在，可能是添加任务时，事务已经回滚了", param.getPushId()));
            return ConsumerStatus.CANCELED;
        }

        if (!FeatureActionService.canRun(push.getOrgId(), FeatureAction.WEBHOOK_NOTIFY, true)) {
            entity.setResponse(String.format("%s版本已降级 取消webhook推送", push.getOrgId()));
            return ConsumerStatus.CANCELED;
        }

        if (push.getStatus() == PushStatus.FAILED) {
            return httpPost(push) ? ConsumerStatus.SUCCESS : ConsumerStatus.FAILED;
        }
        return ConsumerStatus.FAILED;
    }

    @Retryable
    public Content post(String url, String body, HashMap<String, Object> headers, AtomicInteger retry) throws Exception {
        int currentRetry = 1 + RetrySynchronizationManager.getContext().getRetryCount();
        retry.set(currentRetry);
        log.info("try: {} push webhook: url={}, body={}", retry, url, body);
        return post(url, body, headers);
    }

    public Content post(String url, String body, HashMap<String, Object> headers) throws Exception {

        Request request = Request.Post(url)
                .connectTimeout(1000 * 10)
                .socketTimeout(1000 * 60)
                .bodyByteArray(body.getBytes(StandardCharsets.UTF_8), ContentType.APPLICATION_JSON);

        Optional.ofNullable(headers).ifPresent(h -> h.forEach((k, v) -> request.addHeader(k, String.valueOf(v))));

        return request.execute().returnContent();
    }

    public boolean httpPost(Push push) {
        IWebhookCompatInfo compat;
        HashMap<String, Object> headers = new HashMap<>();
        AtomicReference<String> pushUrl = new AtomicReference<>(push.getAddress());

        Optional.ofNullable(push.getConnector()).flatMap(connector -> Optional.ofNullable(connector.getAuthorizeConfig()))
                .ifPresent(config -> {
                    Optional.ofNullable(config.getToken()).ifPresent(token -> headers.put("cem-token", token));
                    Optional.ofNullable(config.getSign()).ifPresent(sign -> {
                        try {
                            // 需求不改  为了对接外部的接口  几乎是写死了 需要在url上添加signt 和sign
                            long timeStamp = System.currentTimeMillis();
                            String url = pushUrl.get();
                            String signtParam = (url.contains("?") || url.contains("%3F")) ? "&signt=" : "?signt=";
                            url = url + signtParam + timeStamp;
                            url += "&appkey=" + sign.getAppKey();
                            url += "&sign=" + SignGenerator.generate(url, sign.getAppSecret());
                            pushUrl.set(url);

                        } catch (Exception e) {
                            log.error("webhook sign error", e);
                        }
                    });
                });

        if (webhookCompat != null && (compat = webhookCompat.hasCompat(push)) != null) {
            return httpPostCompat(push, headers, compat);
        }
        AtomicInteger retry = new AtomicInteger(1);
        try {
            String body = push.getContent();
            String response = self.post(pushUrl.get(), body, headers, retry).asString();
            push.setStatus(PushStatus.SUCCESS);
            push.setResponse(response);
            return true;
        } catch (Throwable e) {
            push.setStatus(PushStatus.FAILED);
            push.setResponse(e.getMessage());
        } finally {
            push.setRetry(retry.get());
            pushRepository.save(push);
        }
        return false;
    }

    public boolean httpPostCompat(Push push, HashMap<String, Object> headers, IWebhookCompatInfo compat) {
        try {
            PushStatus status = webhookCompat.push(push, compat, () -> {
                try {
                    String response = post(push.getAddress(), push.getContent(), headers).asString();
                    push.setResponse(response);
                    return response;
                } catch (Throwable e) {
                    push.setResponse(e.getMessage());
                    throw e;
                }
            });
            push.setStatus(status);
            return status == PushStatus.SUCCESS;
        } catch (Throwable e) {
            push.setStatus(PushStatus.FAILED);
            push.setResponse(e.getMessage());
        } finally {
            pushRepository.save(push);
        }
        return false;
    }
}
