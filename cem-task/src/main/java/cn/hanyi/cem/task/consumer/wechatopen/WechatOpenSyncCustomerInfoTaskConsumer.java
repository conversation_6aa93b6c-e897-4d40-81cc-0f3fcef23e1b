package cn.hanyi.cem.task.consumer.wechatopen;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskWechatOpenSyncCustomerInfoDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskAutoUpdateProgressConsumer;
import cn.hanyi.ctm.constant.connector.ConnectorAuthorizeStatus;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.repository.CustomerRepository;
import cn.hanyi.ctm.repository.ThirdPartyCustomerRepository;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.collections.CollectionUtils;
import org.befun.extension.service.WeChatOpenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class WechatOpenSyncCustomerInfoTaskConsumer implements ITaskAutoUpdateProgressConsumer<TaskWechatOpenSyncCustomerInfoDto> {

    @Autowired
    private WeChatOpenService weChatOpenService;
    @Autowired
    private CustomerRepository customerRepository;
    @Autowired
    private ThirdPartyCustomerRepository thirdPartyCustomerRepository;

    @Override
    public TaskType type() {
        return TaskType.WECHAT_OPEN_SYNC_CUSTOMER_INFO;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskWechatOpenSyncCustomerInfoDto param) {
        try {
            List<String> openIds = param.parseOpenIds();
            if (CollectionUtils.isNotEmpty(openIds)) {
                if ("mockSync".equals(param.getAppId())) {
                    mockSync(entity, param);
                } else {
                    syncOpenIds(entity.getOrgId(), param.getTaskProgressId(), param.getThirdpartyAuthId(), param.getAppId(), openIds);
                }
            }
        } catch (WxErrorException e) {
            log.error("同步微信用户信息：fail， orgId={}, thirdpartyAuthId={}, taskProgressId={}", entity.getOrgId(), param.getThirdpartyAuthId(), param.getTaskProgressId());
            entity.setResponse(e.getMessage());
            return ConsumerStatus.FAILED;
        }
        return ConsumerStatus.SUCCESS;
    }

    private List<Customer> syncOpenIds(Long orgId, Long taskProgressId, Long thirdpartyAuthId, String appId, List<String> openIds) throws WxErrorException {
        List<WxMpUser> users = weChatOpenService.getUserInfoList(appId, openIds);
        if (CollectionUtils.isNotEmpty(users)) {
            List<Customer> customers = new ArrayList<>();
            users.forEach(i -> {
                try {
                    Customer customer = syncThirdPartyCustomer(orgId, thirdpartyAuthId, i);
                    if (customer != null) {
                        customers.add(customer);
                    }
                } catch (Throwable e) {
                    log.error("同步微信用户信息：fail, orgId={}, thirdpartyAuthId={}, openId={}", orgId, thirdpartyAuthId, i.getOpenId());
                }
            });
            log.info("同步微信用户信息：next，orgId={}, thirdpartyAuthId={}, taskProgressId={}", orgId, thirdpartyAuthId, taskProgressId);
            return customers;
        }
        return null;
    }

    /**
     * 从微信同步的客户信息
     */
    private Customer syncThirdPartyCustomer(Long orgId, Long thirdpartyAuthId, WxMpUser mpUser) {

        ThirdPartyCustomer thirdPartyCustomer = thirdPartyCustomerRepository.findFirstByOrgIdAndOpenId(orgId, mpUser.getOpenId());

        // 新增或更新 微信用户
        if (thirdPartyCustomer == null) {
            thirdPartyCustomer = new ThirdPartyCustomer();
            thirdPartyCustomer.setOrgId(orgId);
            thirdPartyCustomer.setThirdpartyAuthId(thirdpartyAuthId);
        } else {
            // 如果之前授权的公众号删了，重新授权之后，thirdpartyAuthId 会变化，这里重新赋值一次
            thirdPartyCustomer.setThirdpartyAuthId(thirdpartyAuthId);
        }
        buildCustomer(mpUser, thirdPartyCustomer);
        thirdPartyCustomerRepository.save(thirdPartyCustomer);

        List<Customer> customers = customerRepository.findByOrgIdAndThirdPartyCustomerId(orgId, thirdPartyCustomer.getId());
        if (CollectionUtils.isNotEmpty(customers)) {
            return customers.get(0);
        } else {
            // 新增客户
            Customer customer = new Customer(thirdPartyCustomer, orgId);
            if (customer.getUsername() == null) {
                customer.setUsername("");
            }
            if (customer.getAddress() == null) {
                customer.setAddress("");
            }
            if (customer.getMembershipLevel() == null) {
                customer.setMembershipLevel("");
            }
            if (customer.getLevelIds() == null) {
                customer.setLevelIds("");
            }
            if (customer.getDepartmentNames() == null) {
                customer.setDepartmentNames("");
            }
            customerRepository.save(customer);
            return customer;
        }
    }

    private void buildCustomer(WxMpUser mpUser, ThirdPartyCustomer customer) {
        customer.setOpenId(mpUser.getOpenId());
        customer.setUnionId(mpUser.getUnionId());
        customer.setSubscribedAt(mpUser.getSubscribeTime());
        customer.setRemark(mpUser.getRemark());
        customer.setSubscribed(true);
        customer.setAuthorizeStatus(ConnectorAuthorizeStatus.AUTHORIZED);
    }

    private void mockSync(CemTask entity, TaskWechatOpenSyncCustomerInfoDto param) {
        log.info("同步微信用户信息：next，orgId={}, thirdpartyAuthId={}, taskProgressId={}", entity.getOrgId(), param.getThirdpartyAuthId(), param.getTaskProgressId());
    }

    public Customer syncOpenId(Long orgId, Long thirdpartyAuthId, String appId, String openId) {
        try {
            List<Customer> customers = syncOpenIds(orgId, 0L, thirdpartyAuthId, appId, List.of(openId));
            if (CollectionUtils.isNotEmpty(customers)) {
                return customers.get(0);
            }
        } catch (WxErrorException e) {
            log.error("同步微信用户失败, orgId={}, appId={}, openId={}", orgId, appId, openId, e);
        }
        return null;
    }
}
