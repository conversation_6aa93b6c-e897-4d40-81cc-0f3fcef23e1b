package cn.hanyi.cem.task.consumer.wechatopen;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskWechatOpenSyncCustomerListDto;
import cn.hanyi.cem.core.dto.task.progress.TaskPageableDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.cem.task.consumer.ITaskConsumer;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.UserTaskService;
import org.befun.extension.service.WeChatOpenService;
import org.befun.task.constant.TaskStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Component
public class WechatOpenSyncCustomerListTaskConsumer implements ITaskConsumer<TaskWechatOpenSyncCustomerListDto> {

    @Autowired
    private WeChatOpenService weChatOpenService;
    @Autowired
    private TaskProducerHelper taskProducerHelper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private UserTaskService userTaskService;

    @Override
    public TaskType type() {
        return TaskType.WECHAT_OPEN_SYNC_CUSTOMER_LIST;
    }

    /**
     * 1 设置任务状态为进行中
     * 2 添加任务总数
     */
    @Override
    public ConsumerStatus consumer(CemTask entity, TaskWechatOpenSyncCustomerListDto param) {
        // 设置任务状态为进行中
        userTaskService.updateTaskStatus(param.getTaskProgressId(), TaskStatus.RUNNING);
        if ("mockSync".equals(param.getAppId())) {
            mockSync(entity, param);
        } else {
            try {
                WxMpUserList userList = weChatOpenService.getUserIdList(param.getAppId(), param.getNextId());
                if (userList == null) {
                    log.info("同步微信openId列表：未拉取到用户，taskId={}", entity.getId());
                    userTaskService.failedTask(param.getTaskProgressId(), "未拉取到用户");
                    return ConsumerStatus.FAILED;
                }
                if (StringUtils.isEmpty(param.getNextId()) && userList.getTotal() > 0) {
                    userTaskService.updateTaskTotalSize(param.getTaskProgressId(), (int) userList.getTotal()); // 设置任务总数
                }
                if (userList.getCount() > 0 && CollectionUtils.isNotEmpty(userList.getOpenids())) {
                    taskProducerHelper.wechatOpenSyncCustomerInfo(entity, param.getTaskProgressId(), param.getThirdpartyAuthId(), param.getAppId(), userList.getCount(), (page, size) -> TaskPageableDto.splitPageList(userList.getOpenids(), page, size));
                }
                if (StringUtils.isNotEmpty(userList.getNextOpenid())) {
                    taskProducerHelper.wechatOpenSyncCustomerList(entity, param.getTaskProgressId(), param.getThirdpartyAuthId(), param.getAppId(), userList.getNextOpenid());
                }
            } catch (Throwable e) {
                log.error("同步微信openId列表：fail，taskId={}", entity.getId(), e);
                entity.setResponse(e.getMessage());
                userTaskService.failedTask(param.getTaskProgressId(), e.getMessage());
                return ConsumerStatus.FAILED;
            }
        }
        return ConsumerStatus.SUCCESS;
    }


    private void mockSync(CemTask entity, TaskWechatOpenSyncCustomerListDto param) {
        int nextId = param.getNextId() == null ? 0 : Integer.parseInt(param.getNextId());
        int[] counts = new int[]{100, 20};
        if (nextId == 0) {
            userTaskService.updateTaskTotalSize(param.getTaskProgressId(), Arrays.stream(counts).sum());
        }
        int count = counts[nextId];
        List<String> openIds = IntStream.range(0, count).mapToObj(Objects::toString).collect(Collectors.toList());
        taskProducerHelper.wechatOpenSyncCustomerInfo(entity, param.getTaskProgressId(), param.getThirdpartyAuthId(), param.getAppId(), count, (page, size) -> TaskPageableDto.splitPageList(openIds, page, size));
        if (nextId < counts.length - 1) {
            taskProducerHelper.wechatOpenSyncCustomerList(entity, param.getTaskProgressId(), param.getThirdpartyAuthId(), param.getAppId(), String.valueOf(nextId + 1));
        }
    }
}
