package cn.hanyi.cem.task.consumer.wechatopen;

import cn.hanyi.cem.core.constant.ConsumerStatus;
import cn.hanyi.cem.core.constant.TaskType;
import cn.hanyi.cem.core.dto.task.TaskWechatOpenSyncTemplateDto;
import cn.hanyi.cem.core.entity.CemTask;
import cn.hanyi.cem.task.consumer.ITaskAutoUpdateProgressConsumer;
import cn.hanyi.ctm.service.ThirdPartyTemplateService;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.ThirdPartyAuthService;
import org.befun.auth.service.UserTaskService;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.task.constant.TaskStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class WechatOpenSyncTemplateTaskConsumer implements ITaskAutoUpdateProgressConsumer<TaskWechatOpenSyncTemplateDto> {
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    @Autowired
    private AuthWechatOpenService authWechatOpenService;
    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;

    @Override
    public TaskType type() {
        return TaskType.WECHAT_OPEN_SYNC_TEMPLATE;
    }

    @Override
    public ConsumerStatus consumer(CemTask entity, TaskWechatOpenSyncTemplateDto param) {
        // 设置任务状态为进行中
        userTaskService.updateTaskStatus(param.getTaskProgressId(), TaskStatus.RUNNING);
        List<ConfigWrap> list = null;
        try {
            if (param.isAll()) {
                list = getAll(param.getOrgId());
            } else if (param.getThirdpartyAuthId() != null) {
                list = wrapOne(param.getOrgId(), param.getThirdpartyAuthId());
            }
            if (CollectionUtils.isEmpty(list)) {
                userTaskService.failedTask(param.getTaskProgressId(), "没有指定同步的公众号id");
                return ConsumerStatus.FAILED;
            }
            userTaskService.updateTaskTotalSize(param.getTaskProgressId(), list.size());
            list.forEach(i -> {
                try {
                    thirdPartyTemplateService.syncTemplate(i.entity, i.config);
                } catch (Throwable e) {
                    log.error("同步微信消息模版：fail， orgId={}, appId={}, appName={}", entity.getOrgId(), i.config.getAppId(), i.config.getName());
                }
            });
        } catch (Throwable e) {
            log.error("同步微信消息模版：fail， orgId={}, thirdpartyAuthId={}, taskProgressId={}", entity.getOrgId(), param.getThirdpartyAuthId(), param.getTaskProgressId());
            entity.setResponse(e.getMessage());
            userTaskService.failedTask(param.getTaskProgressId(), e.getMessage());
            return ConsumerStatus.FAILED;
        }
        userTaskService.successTask(param.getTaskProgressId(), null);
        return ConsumerStatus.SUCCESS;
    }

    private List<ConfigWrap> getAll(Long orgId) {
        List<ThirdPartyAuth> list = thirdPartyAuthService.getListByOrg(orgId, ThirdPartyAuthType.WECHAT_OPEN, "cem");
        if (CollectionUtils.isNotEmpty(list)) {
            List<ConfigWrap> all = new ArrayList<>();
            list.forEach(i -> {
                WechatOpenConfig config = authWechatOpenService.getConfig(i);
                if (config != null && config.isAuthorized()) {
                    all.add(new ConfigWrap(i, config));
                }
            });
            return all;
        }
        return null;
    }

    private List<ConfigWrap> wrapOne(Long orgId, Long thirdpartyAuthId) {
        ThirdPartyAuth entity = thirdPartyAuthService.get(thirdpartyAuthId);
        if (entity != null && ThirdPartyAuthType.WECHAT_OPEN == entity.getAuthType() && orgId.equals(entity.getOrgId())) {
            WechatOpenConfig config = authWechatOpenService.getConfig(entity);
            if (config != null && config.isAuthorized()) {
                return List.of(new ConfigWrap(entity, config));
            }
        }
        return null;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfigWrap {
        private ThirdPartyAuth entity;
        private WechatOpenConfig config;
    }

}
