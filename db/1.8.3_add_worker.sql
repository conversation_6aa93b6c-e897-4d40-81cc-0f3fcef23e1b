CREATE TABLE `worker_event`
(
    `id`                bigint          NOT NULL    AUTO_INCREMENT,
    `org_id`            bigint          NULL        COMMENT '机构id',
    `user_id`           bigint          NULL        COMMENT '用户id',
    `type`              varchar(50)     NULL        COMMENT '事件类型',
    `status`            varchar(50)     NOT NULL    DEFAULT 'INIT' COMMENT '事件状态',
    `consumer_status`   varchar(512)    NULL        COMMENT '消费者状态：json格式',
    `content`           text            NULL        COMMENT '任务内容',
    `response`          text            NULL        COMMENT '响应内容',
    `source`            varchar(50)     NULL        COMMENT '事件来源',
    `delay`             varchar(50)     NULL        COMMENT '延迟执行时间',
    `counts`            int             NOT NULL    DEFAULT 0 COMMENT '任务运行次数',
    `create_time`       timestamp       NULL        DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`       timestamp       NULL        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT 'worker事件';

CREATE TABLE `worker_task`
(
    `id`                bigint          NOT NULL    AUTO_INCREMENT,
    `org_id`            bigint          NULL        COMMENT '机构id',
    `user_id`           bigint          NULL        COMMENT '用户id',
    `type`              varchar(50)     NULL        COMMENT '事件类型',
    `status`            varchar(50)     NOT NULL    DEFAULT 'INIT' COMMENT '事件状态',
    `consumer_status`   varchar(512)    NULL        COMMENT '消费者状态：json格式',
    `content`           text            NULL        COMMENT '任务内容',
    `response`          text            NULL        COMMENT '响应内容',
    `source`            varchar(50)     NULL        COMMENT '事件来源',
    `delay`             varchar(50)     NULL        COMMENT '延迟执行时间',
    `counts`            int             NOT NULL    DEFAULT 0 COMMENT '任务运行次数',
    `create_time`       timestamp       NULL        DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`       timestamp       NULL        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT 'worker任务';