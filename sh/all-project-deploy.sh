#!/usr/bin/env bash

dir=$(pwd)
if [[ ${dir} == *"sh" ]]
then
  source .env
else
  cd sh
  source .env
fi
cd ..

dir_core=$DIR_CORE
dir_auth=$DIR_AUTH
dir_ctm=$DIR_CTM
dir_survey=$DIR_SURVEY
dir_worker=$DIR_WORKER
dir_bi=$DIR_BI

cd ..
echo "开始发布所有模块：`pwd`"

cd ${dir_core}
./sh/deploy.sh

cd ../${dir_worker}
./sh/deploy.sh

cd ../${dir_auth}
./sh/deploy.sh

cd ../${dir_ctm}
./sh/deploy.sh

cd ../${dir_survey}
./sh/deploy.sh
