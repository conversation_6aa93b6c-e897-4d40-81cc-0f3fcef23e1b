#!/usr/bin/env bash

dir=$(pwd)
if [[ ${dir} == *"sh" ]]
then
  source .env
else
  cd sh
  source .env
fi
cd ..

dir_core=$DIR_CORE
dir_auth=$DIR_AUTH
dir_ctm=$DIR_CTM
dir_survey=$DIR_SURVEY
dir_worker=$DIR_WORKER
dir_bi=$DIR_BI

revision=$REVISION
sha1=$1
changelist=$2
echo "revision：$revision"
if [[ $sha1 ]]
 then
   echo "sha1：$sha1"
else
  echo "sha1：(未指定，使用模块默认值)"
fi
if [[ $changelist ]]
 then
   echo "changelist：$changelist"
else
  echo "changelist：(未指定，使用模块默认值)"
fi

cd ..
echo "开始安装所有模块：`pwd`"

cd ${dir_core}
./sh/install.sh $revision $sha1 $changelist

cd ../${dir_worker}
./sh/install.sh $revision $sha1 $changelist

cd ../${dir_auth}
./sh/install.sh $revision $sha1 $changelist

cd ../${dir_ctm}
./sh/install.sh $revision $sha1 $changelist

cd ../${dir_survey}
./sh/install.sh $revision $sha1 $changelist

cd ../${dir_bi}
./sh/install.sh $revision $sha1 $changelist

if [[ $sha1 ]]
 then
   if [[ $changelist ]]
    then
      echo "修改所有项目changelist为${changelist}"
   else
     changelist=SNAPSHOT
   fi

   cd ../${dir_core}
   ./sh/install.sh $revision $sha1 $changelist 1

   cd ../${dir_worker}
   ./sh/install.sh $revision $sha1 $changelist 1

   cd ../${dir_auth}
   ./sh/install.sh $revision $sha1 $changelist 1

   cd ../${dir_ctm}
   ./sh/install.sh $revision $sha1 $changelist 1

   cd ../${dir_survey}
   ./sh/install.sh $revision $sha1 $changelist 1

   cd ../${dir_bi}
   ./sh/install.sh $revision $sha1 $changelist 1
fi